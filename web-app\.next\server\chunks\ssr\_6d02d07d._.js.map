{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/KnowledgeCard.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - KnowledgeCard知识点卡片组件\n// 基于FeatureCard组件，专门用于展示MySQL知识点信息\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  BookOpen, \n  Clock, \n  Star, \n  ArrowRight, \n  Calendar,\n  Tag,\n  TrendingUp,\n  Database,\n  Code,\n  Settings\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { KnowledgeCardProps, KnowledgeItem } from '@/lib/types';\n\n// 难度等级配置\nconst difficultyConfig = {\n  beginner: {\n    label: '初级',\n    color: 'bg-green-100 text-green-700 border-green-200',\n    icon: '🟢'\n  },\n  intermediate: {\n    label: '中级', \n    color: 'bg-yellow-100 text-yellow-700 border-yellow-200',\n    icon: '🟡'\n  },\n  advanced: {\n    label: '高级',\n    color: 'bg-red-100 text-red-700 border-red-200', \n    icon: '🔴'\n  }\n};\n\n// 分类图标映射\nconst categoryIcons = {\n  'basics': Database,\n  'database-operations': Settings,\n  'table-operations': Code,\n  'data-operations': BookOpen,\n  'advanced-queries': TrendingUp,\n  'management': Settings\n};\n\n// 估算阅读时间（基于内容长度）\nconst estimateReadingTime = (content: string): number => {\n  const wordsPerMinute = 200; // 中文阅读速度约200字/分钟\n  const wordCount = content.length;\n  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));\n};\n\n// 格式化更新时间\nconst formatUpdateTime = (dateString: string): string => {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffTime = Math.abs(now.getTime() - date.getTime());\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  \n  if (diffDays === 1) return '今天更新';\n  if (diffDays <= 7) return `${diffDays}天前更新`;\n  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前更新`;\n  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\n};\n\nconst KnowledgeCard = React.forwardRef<HTMLDivElement, KnowledgeCardProps>(({\n  item,\n  displayMode = 'grid',\n  onClick,\n  className,\n  ...props\n}, ref) => {\n  const difficulty = difficultyConfig[item.difficulty];\n  const CategoryIcon = categoryIcons[(item.category_id || '') as keyof typeof categoryIcons] || BookOpen;\n  const readingTime = estimateReadingTime(item.content);\n  const updateTime = formatUpdateTime(item.last_updated);\n\n  // 网格模式（详细显示）- grid模式和list模式支持\n  if (displayMode === 'grid') {\n    return (\n      <motion.div\n        ref={ref}\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.4, ease: \"easeOut\" }}\n        viewport={{ once: true }}\n        className=\"group h-full\"\n        {...props}\n      >\n        <div\n          className={cn(\n            'relative bg-white rounded-2xl shadow-lg border border-mysql-border h-full',\n            'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',\n            'cursor-pointer overflow-hidden',\n            'hover:ring-2 hover:ring-mysql-primary/20',\n            className\n          )}\n          onClick={(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            onClick?.();\n          }}\n          data-testid=\"knowledge-card\"\n        >\n          {/* 顶部渐变装饰 */}\n          <div className=\"absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-mysql-primary to-mysql-accent\" />\n\n          <div className=\"p-6 h-full flex flex-col\">\n            {/* 头部：图标、标题和难度 */}\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex items-center flex-1 min-w-0\">\n                <div className=\"flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-mysql-primary to-mysql-accent shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0\">\n                  <CategoryIcon className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"ml-3 flex-1 min-w-0\">\n                  <h3 className=\"text-lg font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 line-clamp-2\">\n                    {item.title}\n                  </h3>\n                </div>\n              </div>\n              <div className={cn(\n                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',\n                difficulty.color\n              )}>\n                <span className=\"mr-1\">{difficulty.icon}</span>\n                {difficulty.label}\n              </div>\n            </div>\n\n            {/* 描述 */}\n            <p className=\"text-mysql-text-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3\">\n              {item.description}\n            </p>\n\n            {/* 标签云 */}\n            {item.tags && item.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-4\">\n                {item.tags.slice(0, 3).map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-2 py-1 text-xs bg-mysql-primary-light text-mysql-primary rounded-md\"\n                  >\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {tag}\n                  </span>\n                ))}\n                {item.tags.length > 3 && (\n                  <span className=\"text-xs text-mysql-text-light\">\n                    +{item.tags.length - 3}\n                  </span>\n                )}\n              </div>\n            )}\n\n            {/* 底部信息 */}\n            <div className=\"mt-auto pt-4 border-t border-mysql-border\">\n              <div className=\"flex items-center justify-between text-xs text-mysql-text-light mb-3\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-4 h-4 mr-1\" />\n                  <span>{readingTime}分钟阅读</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <Calendar className=\"w-4 h-4 mr-1\" />\n                  <span>{updateTime}</span>\n                </div>\n              </div>\n              \n              {/* 查看详情按钮 */}\n              <motion.div\n                className=\"flex items-center justify-between text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"text-sm font-medium\">查看详情</span>\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.div>\n            </div>\n          </div>\n\n          {/* 悬停光效 */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\" />\n        </div>\n      </motion.div>\n    );\n  }\n\n  // 列表模式（紧凑显示）\n  return (\n    <motion.div\n      ref={ref}\n      initial={{ opacity: 0, x: -20 }}\n      whileInView={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.3, ease: \"easeOut\" }}\n      viewport={{ once: true }}\n      className=\"group\"\n      {...props}\n    >\n      <div\n        className={cn(\n          'bg-white rounded-xl p-4 shadow-md border border-mysql-border',\n          'hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out',\n          'cursor-pointer hover:ring-2 hover:ring-mysql-primary/20',\n          className\n        )}\n        onClick={onClick}\n        data-testid=\"knowledge-card\"\n      >\n        <div className=\"flex items-center space-x-4\">\n          {/* 图标 */}\n          <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-mysql-primary to-mysql-accent text-white flex-shrink-0\">\n            <CategoryIcon className=\"w-5 h-5\" />\n          </div>\n\n          {/* 内容 */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center justify-between mb-1\">\n              <h4 className=\"text-base font-semibold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 truncate\">\n                {item.title}\n              </h4>\n              <div className={cn(\n                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',\n                difficulty.color\n              )}>\n                {difficulty.label}\n              </div>\n            </div>\n            \n            <p className=\"text-mysql-text-light text-sm line-clamp-2 mb-2\">\n              {item.description}\n            </p>\n\n            <div className=\"flex items-center justify-between text-xs text-mysql-text-light\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-3 h-3 mr-1\" />\n                  <span>{readingTime}分钟</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <Tag className=\"w-3 h-3 mr-1\" />\n                  <span>{item.tags?.length || 0}个标签</span>\n                </div>\n              </div>\n              <span>{updateTime}</span>\n            </div>\n          </div>\n\n          {/* 箭头 */}\n          <ArrowRight className=\"w-5 h-5 text-mysql-text-light group-hover:text-mysql-primary transition-colors duration-300 flex-shrink-0\" />\n        </div>\n      </div>\n    </motion.div>\n  );\n});\n\nKnowledgeCard.displayName = 'KnowledgeCard';\n\nexport default KnowledgeCard;\n"], "names": [], "mappings": ";;;;AAEA,oCAAoC;AACpC,mCAAmC;AAEnC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;AAsBA,SAAS;AACT,MAAM,mBAAmB;IACvB,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA,cAAc;QACZ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;IACR;AACF;AAEA,SAAS;AACT,MAAM,gBAAgB;IACpB,UAAU,0MAAA,CAAA,WAAQ;IAClB,uBAAuB,0MAAA,CAAA,WAAQ;IAC/B,oBAAoB,kMAAA,CAAA,OAAI;IACxB,mBAAmB,8MAAA,CAAA,WAAQ;IAC3B,oBAAoB,kNAAA,CAAA,aAAU;IAC9B,cAAc,0MAAA,CAAA,WAAQ;AACxB;AAEA,iBAAiB;AACjB,MAAM,sBAAsB,CAAC;IAC3B,MAAM,iBAAiB,KAAK,iBAAiB;IAC7C,MAAM,YAAY,QAAQ,MAAM;IAChC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,YAAY;AAC3C;AAEA,UAAU;AACV,MAAM,mBAAmB,CAAC;IACxB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;IACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE1D,IAAI,aAAa,GAAG,OAAO;IAC3B,IAAI,YAAY,GAAG,OAAO,GAAG,SAAS,IAAI,CAAC;IAC3C,IAAI,YAAY,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC3D,OAAO,KAAK,kBAAkB,CAAC,SAAS;QAAE,OAAO;QAAS,KAAK;IAAU;AAC3E;AAEA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAqC,CAAC,EAC1E,IAAI,EACJ,cAAc,MAAM,EACpB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,gBAAgB,CAAC,KAAK,UAAU,CAAC;IACpD,MAAM,eAAe,aAAa,CAAE,KAAK,WAAW,IAAI,GAAkC,IAAI,8MAAA,CAAA,WAAQ;IACtG,MAAM,cAAc,oBAAoB,KAAK,OAAO;IACpD,MAAM,aAAa,iBAAiB,KAAK,YAAY;IAErD,8BAA8B;IAC9B,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,UAAU;gBAAE,MAAM;YAAK;YACvB,WAAU;YACT,GAAG,KAAK;sBAET,cAAA,8OAAC;gBACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6EACA,yEACA,kCACA,4CACA;gBAEF,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB;gBACF;gBACA,eAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAa,WAAU;;;;;;;;;;;0DAE1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACf,wEACA,WAAW,KAAK;;0DAEhB,8OAAC;gDAAK,WAAU;0DAAQ,WAAW,IAAI;;;;;;4CACtC,WAAW,KAAK;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;4BAIlB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;oCAOR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;wCAAK,WAAU;;4CAAgC;4CAC5C,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;0CAO7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM;4DAAY;;;;;;;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAM;;;;;;;;;;;;;;;;;;kDAKX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,aAAa;IACb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,gEACA,2EACA,2DACA;YAEF,SAAS;YACT,eAAY;sBAEZ,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAa,WAAU;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACf,wEACA,WAAW,KAAK;kDAEf,WAAW,KAAK;;;;;;;;;;;;0CAIrB,8OAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAGnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM;4DAAY;;;;;;;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;;4DAAM,KAAK,IAAI,EAAE,UAAU;4DAAE;;;;;;;;;;;;;;;;;;;kDAGlC,8OAAC;kDAAM;;;;;;;;;;;;;;;;;;kCAKX,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKhC;AAEA,cAAc,WAAW,GAAG;uCAEb", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Breadcrumb面包屑导航组件\n// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getBreadcrumbs } from '@/lib/navigation';\nimport { BreadcrumbProps } from '@/lib/types';\n\nconst Breadcrumb: React.FC<BreadcrumbProps> = React.memo(({\n  pathname,\n  maxItems = 5,\n  className,\n  ...props\n}) => {\n  // 获取面包屑数据\n  const breadcrumbs = React.useMemo(() => getBreadcrumbs(pathname), [pathname]);\n\n  // 如果只有首页，不显示面包屑\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  // 处理超长路径的截断\n  const displayBreadcrumbs = breadcrumbs.length > maxItems\n    ? [\n        breadcrumbs[0], // 首页\n        { name: '...', href: '#', isEllipsis: true },\n        ...breadcrumbs.slice(-2) // 最后两项\n      ]\n    : breadcrumbs;\n\n  return (\n    <motion.nav\n      initial={{ opacity: 0, y: -10 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n      aria-label=\"面包屑导航\"\n      className={cn(\n        'flex items-center space-x-1 text-sm',\n        'py-2 px-1',\n        className\n      )}\n      {...props}\n    >\n      <ol className=\"flex items-center space-x-1 md:space-x-2\">\n        {displayBreadcrumbs.map((item, index) => {\n          const isLast = index === displayBreadcrumbs.length - 1;\n          const isEllipsis = 'isEllipsis' in item && item.isEllipsis;\n          const isHome = item.href === '/';\n\n          return (\n            <li key={`${item.href}-${index}`} className=\"flex items-center\">\n              {/* 分隔符 */}\n              {index > 0 && (\n                <ChevronRight \n                  className=\"w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0\" \n                  aria-hidden=\"true\"\n                />\n              )}\n\n              {/* 面包屑项 */}\n              {isEllipsis ? (\n                <span className=\"text-gray-400 px-1\">...</span>\n              ) : isLast ? (\n                // 当前页面（不可点击）\n                <span\n                  className={cn(\n                    'font-medium text-mysql-primary',\n                    'px-2 py-1 rounded-md',\n                    'bg-mysql-primary/5',\n                    'flex items-center space-x-1'\n                  )}\n                  aria-current=\"page\"\n                >\n                  {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                  <span className={cn(\n                    'truncate max-w-[80px] md:max-w-[120px]',\n                    isHome && 'hidden md:inline'\n                  )}>\n                    {item.name}\n                  </span>\n                </span>\n              ) : (\n                // 可点击的面包屑项\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'text-gray-600 hover:text-mysql-primary',\n                      'px-2 py-1 rounded-md',\n                      'hover:bg-mysql-primary/5',\n                      'transition-all duration-200',\n                      'flex items-center space-x-1',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                    <span className={cn(\n                      'truncate max-w-[80px] md:max-w-[120px]',\n                      isHome && 'hidden md:inline'\n                    )}>\n                      {item.name}\n                    </span>\n                  </Link>\n                </motion.div>\n              )}\n            </li>\n          );\n        })}\n      </ol>\n    </motion.nav>\n  );\n});\n\nBreadcrumb.displayName = 'Breadcrumb';\n\nexport default Breadcrumb;\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,mCAAmC;AAEnC;AACA;AACA;AACA;AAAA;AACA;AACA;AAVA;;;;;;;;AAaA,MAAM,2BAAwC,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EACxD,QAAQ,EACR,WAAW,CAAC,EACZ,SAAS,EACT,GAAG,OACJ;IACC,UAAU;IACV,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QAAC;KAAS;IAE5E,gBAAgB;IAChB,IAAI,YAAY,MAAM,IAAI,GAAG;QAC3B,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,qBAAqB,YAAY,MAAM,GAAG,WAC5C;QACE,WAAW,CAAC,EAAE;QACd;YAAE,MAAM;YAAO,MAAM;YAAK,YAAY;QAAK;WACxC,YAAY,KAAK,CAAC,CAAC,GAAG,OAAO;KACjC,GACD;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,cAAW;QACX,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,uCACA,aACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM;gBAC7B,MAAM,SAAS,UAAU,mBAAmB,MAAM,GAAG;gBACrD,MAAM,aAAa,gBAAgB,QAAQ,KAAK,UAAU;gBAC1D,MAAM,SAAS,KAAK,IAAI,KAAK;gBAE7B,qBACE,8OAAC;oBAAiC,WAAU;;wBAEzC,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;4BACX,WAAU;4BACV,eAAY;;;;;;wBAKf,2BACC,8OAAC;4BAAK,WAAU;sCAAqB;;;;;mCACnC,SACF,aAAa;sCACb,8OAAC;4BACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,kCACA,wBACA,sBACA;4BAEF,gBAAa;;gCAEZ,wBAAU,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;8CAET,KAAK,IAAI;;;;;;;;;;;mCAId,WAAW;sCACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0CACA,wBACA,4BACA,+BACA,+BACA;;oCAGD,wBAAU,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;kDAET,KAAK,IAAI;;;;;;;;;;;;;;;;;;mBArDX,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;YA4DpC;;;;;;;;;;;AAIR;AAEA,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/KnowledgePageClient.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - KnowledgePageClient客户端组件\n// 处理知识库首页的交互功能和状态管理\n\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport {\n  Clock,\n  Star,\n  ArrowRight\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { useKnowledgeData } from '@/hooks/useKnowledgeData';\nimport { KnowledgeItem } from '@/lib/types';\nimport KnowledgeCard from '@/components/knowledge/KnowledgeCard';\nimport Breadcrumb from '@/components/ui/Breadcrumb';\nimport { StateWrapper } from '@/components/ui/StateComponents';\n\nexport default function KnowledgePageClient() {\n  const router = useRouter();\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  // 使用 useKnowledgeData Hook 获取真实数据\n  const {\n    data: dbArticles,\n    loading,\n    error,\n    retry\n  } = useKnowledgeData('articles', {\n    includeCodeExamples: true,\n    includeRelated: true\n  }, {\n    autoFetch: true, // 启用自动获取\n    cacheTime: 5 * 60 * 1000, // 5分钟缓存\n    progressiveLoading: true, // 启用渐进加载\n    prioritizeBasicData: true, // 优先加载基础数据\n    debug: process.env.NODE_ENV === 'development'\n  });\n\n  // 数据处理 - useKnowledgeData 已经返回了正确格式的数据\n  const frontendArticles = useMemo(() => {\n    if (!dbArticles || dbArticles.length === 0) return [];\n    return dbArticles as KnowledgeItem[];\n  }, [dbArticles]);\n\n  // 计算热门文章（基于相关项目数量）\n  const popularItems = useMemo(() => {\n    return frontendArticles\n      .sort((a, b) => a.order_index - b.order_index)\n      .slice(0, 6);\n  }, [frontendArticles]);\n\n  // 计算最近更新的文章\n  const recentItems = useMemo(() => {\n    return frontendArticles\n      .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())\n      .slice(0, 6);\n  }, [frontendArticles]);\n\n  // 页面加载状态管理\n  useEffect(() => {\n    if (!loading) {\n      setIsLoaded(true);\n    }\n  }, [loading]);\n\n  // 处理知识点选择\n  const handleKnowledgeSelect = (item: KnowledgeItem) => {\n    // 跳转到知识点详情页面\n    router.push(`/knowledge/${item.category_id}/${item.id}`);\n  };\n\n  return (\n    <div className=\"flex flex-col min-h-full bg-white\" data-testid=\"knowledge-page\">\n      {/* 面包屑导航 */}\n      <div className=\"flex-shrink-0 border-b border-mysql-border bg-white\">\n        <div className=\"px-6 py-4\">\n          <Breadcrumb pathname=\"/knowledge\" />\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"px-6 py-8\">\n          <StateWrapper\n            loading={loading}\n            error={error || undefined}\n            isEmpty={!loading && !error && popularItems.length === 0 && recentItems.length === 0}\n            loadingProps={{\n              message: '正在加载知识库数据...',\n              variant: 'skeleton',\n              itemCount: 6\n            }}\n            errorProps={{\n              title: '加载失败',\n              error: error || '无法加载知识库数据，请检查网络连接或稍后重试',\n              onRetry: retry,\n              variant: 'network'\n            }}\n            emptyProps={{\n              title: '暂无知识库内容',\n              message: '知识库正在建设中，敬请期待更多精彩内容',\n              action: {\n                label: '刷新页面',\n                onClick: () => window.location.reload()\n              }\n            }}\n          >\n            {/* 热门推荐区域 */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}\n              transition={{ duration: 0.6, delay: 0.2, ease: \"easeOut\" }}\n              className=\"mb-12\"\n              data-testid=\"popular-articles\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <Star className=\"w-6 h-6 text-mysql-primary mr-3\" />\n                <h2 className=\"text-2xl font-bold text-mysql-text\">\n                  热门推荐\n                </h2>\n              </div>\n              <p className=\"text-mysql-text-light mb-8\">\n                最受欢迎的MySQL知识点，快速提升你的技能\n              </p>\n\n              {popularItems.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {popularItems.map((item, index) => (\n                    <motion.div\n                      key={item.id}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1, ease: \"easeOut\" }}\n                      data-testid=\"popular-article\"\n                    >\n                      <KnowledgeCard\n                        item={item}\n                        displayMode=\"grid\"\n                        onClick={() => handleKnowledgeSelect(item)}\n                      />\n                    </motion.div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-mysql-text-light\">暂无热门推荐内容</p>\n                </div>\n              )}\n\n                {/* 查看更多按钮 */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                  transition={{ duration: 0.6, delay: 0.5, ease: \"easeOut\" }}\n                  className=\"text-center mt-8\"\n                >\n                  <button\n                    type=\"button\"\n                    onClick={() => router.push('/knowledge/categories')}\n                    className={cn(\n                      'inline-flex items-center px-6 py-3 rounded-lg',\n                      'bg-mysql-primary text-white border border-mysql-primary',\n                      'hover:bg-mysql-primary-dark hover:border-mysql-primary-dark',\n                      'transition-all duration-200 ease-out',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    <span className=\"mr-2\">查看所有知识点</span>\n                    <ArrowRight className=\"w-4 h-4\" />\n                  </button>\n                </motion.div>\n              </motion.div>\n\n              {/* 最近更新区域 */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}\n                transition={{ duration: 0.6, delay: 0.6, ease: \"easeOut\" }}\n                data-testid=\"recent-articles\"\n              >\n                <div className=\"flex items-center mb-6\">\n                  <Clock className=\"w-6 h-6 text-mysql-primary mr-3\" />\n                  <h2 className=\"text-2xl font-bold text-mysql-text\">\n                    最近更新\n                  </h2>\n                </div>\n                <p className=\"text-mysql-text-light mb-8\">\n                  最新添加和更新的MySQL知识内容\n                </p>\n\n                {recentItems.length > 0 ? (\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                    {recentItems.map((item, index) => (\n                      <motion.div\n                        key={item.id}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                        transition={{ duration: 0.4, delay: 0.7 + index * 0.1, ease: \"easeOut\" }}\n                        data-testid=\"recent-article\"\n                      >\n                        <KnowledgeCard\n                          item={item}\n                          displayMode=\"grid\"\n                          onClick={() => handleKnowledgeSelect(item)}\n                        />\n                      </motion.div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-mysql-text-light\">暂无最近更新内容</p>\n                  </div>\n                )}\n              </motion.div>\n          </StateWrapper>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wCAAwC;AACxC,oBAAoB;AAEpB;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAEA;AACA;AACA;AAlBA;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kCAAkC;IAClC,MAAM,EACJ,MAAM,UAAU,EAChB,OAAO,EACP,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;QAC/B,qBAAqB;QACrB,gBAAgB;IAClB,GAAG;QACD,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,OAAO,oDAAyB;IAClC;IAEA,uCAAuC;IACvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG,OAAO,EAAE;QACrD,OAAO;IACT,GAAG;QAAC;KAAW;IAEf,mBAAmB;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,iBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,KAAK,CAAC,GAAG;IACd,GAAG;QAAC;KAAiB;IAErB,YAAY;IACZ,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,iBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,IACpF,KAAK,CAAC,GAAG;IACd,GAAG;QAAC;KAAiB;IAErB,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,YAAY;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,UAAU;IACV,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAoC,eAAY;;0BAE7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oJAAA,CAAA,UAAU;wBAAC,UAAS;;;;;;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yJAAA,CAAA,eAAY;wBACX,SAAS;wBACT,OAAO,SAAS;wBAChB,SAAS,CAAC,WAAW,CAAC,SAAS,aAAa,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK;wBACnF,cAAc;4BACZ,SAAS;4BACT,SAAS;4BACT,WAAW;wBACb;wBACA,YAAY;4BACV,OAAO;4BACP,OAAO,SAAS;4BAChB,SAAS;4BACT,SAAS;wBACX;wBACA,YAAY;4BACV,OAAO;4BACP,SAAS;4BACT,QAAQ;gCACN,OAAO;gCACP,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACvC;wBACF;;0CAGA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS,WAAW,IAAI;oCAAG,GAAG,WAAW,IAAI;gCAAG;gCAC3D,YAAY;oCAAE,UAAU;oCAAK,OAAO;oCAAK,MAAM;gCAAU;gCACzD,WAAU;gCACV,eAAY;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;kDAIrD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;oCAIzC,aAAa,MAAM,GAAG,kBACrB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS,WAAW,IAAI;oDAAG,GAAG,WAAW,IAAI;gDAAG;gDAC3D,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;oDAAK,MAAM;gDAAU;gDACvE,eAAY;0DAEZ,cAAA,8OAAC,8JAAA,CAAA,UAAa;oDACZ,MAAM;oDACN,aAAY;oDACZ,SAAS,IAAM,sBAAsB;;;;;;+CATlC,KAAK,EAAE;;;;;;;;;6DAelB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAKvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS,WAAW,IAAI;4CAAG,GAAG,WAAW,IAAI;wCAAG;wCAC3D,YAAY;4CAAE,UAAU;4CAAK,OAAO;4CAAK,MAAM;wCAAU;wCACzD,WAAU;kDAEV,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iDACA,2DACA,+DACA,wCACA;;8DAGF,8OAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS,WAAW,IAAI;oCAAG,GAAG,WAAW,IAAI;gCAAG;gCAC3D,YAAY;oCAAE,UAAU;oCAAK,OAAO;oCAAK,MAAM;gCAAU;gCACzD,eAAY;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;kDAIrD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;oCAIzC,YAAY,MAAM,GAAG,kBACpB,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS,WAAW,IAAI;oDAAG,GAAG,WAAW,IAAI;gDAAG;gDAC3D,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;oDAAK,MAAM;gDAAU;gDACvE,eAAY;0DAEZ,cAAA,8OAAC,8JAAA,CAAA,UAAa;oDACZ,MAAM;oDACN,aAAY;oDACZ,SAAS,IAAM,sBAAsB;;;;;;+CATlC,KAAK,EAAE;;;;;;;;;6DAelB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "file": "book-open.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/book-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 7v14', key: '1akyts' }],\n  [\n    'path',\n    {\n      d: 'M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z',\n      key: 'ruj8y',\n    },\n  ],\n];\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgN3YxNCIgLz4KICA8cGF0aCBkPSJNMyAxOGExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWg1YTQgNCAwIDAgMSA0IDQgNCA0IDAgMCAxIDQtNGg1YTEgMSAwIDAgMSAxIDF2MTNhMSAxIDAgMCAxLTEgMWgtNmEzIDMgMCAwIDAtMyAzIDMgMyAwIDAgMC0zLTN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('book-open', __iconNode);\n\nexport default BookOpen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "file": "tag.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/tag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z',\n      key: 'vktsd0',\n    },\n  ],\n  ['circle', { cx: '7.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'kqv944' }],\n];\n\n/**\n * @component @name Tag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNTg2IDIuNTg2QTIgMiAwIDAgMCAxMS4xNzIgMkg0YTIgMiAwIDAgMC0yIDJ2Ny4xNzJhMiAyIDAgMCAwIC41ODYgMS40MTRsOC43MDQgOC43MDRhMi40MjYgMi40MjYgMCAwIDAgMy40MiAwbDYuNTgtNi41OGEyLjQyNiAyLjQyNiAwIDAgMCAwLTMuNDJ6IiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tag = createLucideIcon('tag', __iconNode);\n\nexport default Tag;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,QAAA,CAAU;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAA,CAAA,CAAA,CAAG,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,cAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "file": "code.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/code.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 18 6-6-6-6', key: 'eg8j8' }],\n  ['path', { d: 'm8 6-6 6 6 6', key: 'ppft3o' }],\n];\n\n/**\n * @component @name Code\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTggNi02LTYtNiIgLz4KICA8cGF0aCBkPSJtOCA2LTYgNiA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/code\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Code = createLucideIcon('code', __iconNode);\n\nexport default Code;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC/C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "file": "settings.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}