module.exports = {

"[project]/web-app/src/components/layout/Header.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/web-app/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/web-app/src/components/layout/Header.tsx <module evaluation>", "default");
}}),
"[project]/web-app/src/components/layout/Header.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/web-app/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/web-app/src/components/layout/Header.tsx", "default");
}}),
"[project]/web-app/src/components/layout/Header.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Header.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Header.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/web-app/src/components/layout/Footer.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/web-app/src/components/layout/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/web-app/src/components/layout/Footer.tsx <module evaluation>", "default");
}}),
"[project]/web-app/src/components/layout/Footer.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/web-app/src/components/layout/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/web-app/src/components/layout/Footer.tsx", "default");
}}),
"[project]/web-app/src/components/layout/Footer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Footer.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Footer.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/web-app/src/lib/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 常量配置文件
// 包含项目中使用的所有常量、配置和静态数据
__turbopack_context__.s({
    "ABOUT_FEATURES": (()=>ABOUT_FEATURES),
    "ADVANTAGES_DATA": (()=>ADVANTAGES_DATA),
    "ANIMATION_CONFIG": (()=>ANIMATION_CONFIG),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "CHEN_ER_COLORS": (()=>CHEN_ER_COLORS),
    "CONTACT_INFO": (()=>CONTACT_INFO),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURES_DATA": (()=>FEATURES_DATA),
    "FOOTER_LEGAL_LINKS": (()=>FOOTER_LEGAL_LINKS),
    "FOOTER_SECTIONS": (()=>FOOTER_SECTIONS),
    "PAGE_METADATA": (()=>PAGE_METADATA),
    "SITE_CONFIG": (()=>SITE_CONFIG),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "THEME_COLORS": (()=>THEME_COLORS)
});
const SITE_CONFIG = {
    name: 'MySQLAi.de',
    title: 'MySQL智能分析专家',
    description: '专业的数据库知识分享与项目管理平台',
    url: 'https://mysqlai.de',
    author: 'MySQLAi Team',
    keywords: [
        'MySQL',
        '数据库',
        'AI分析',
        '项目管理',
        '知识分享',
        '性能优化'
    ]
};
const THEME_COLORS = {
    primary: '#00758F',
    primaryDark: '#003545',
    primaryLight: '#E6F3F7',
    accent: '#0066CC',
    text: '#2D3748',
    textLight: '#718096',
    border: '#E2E8F0',
    success: '#38A169',
    warning: '#D69E2E',
    error: '#E53E3E'
};
const CHEN_ER_COLORS = {
    primary: '#000000',
    primaryDark: '#000000',
    primaryLight: '#FFFFFF',
    accent: '#000000',
    text: '#000000',
    textLight: '#000000',
    border: '#000000',
    success: '#000000',
    warning: '#000000',
    error: '#000000',
    white: '#FFFFFF',
    background: '#FFFFFF'
};
const PAGE_METADATA = {
    home: {
        title: `${SITE_CONFIG.title} - ${SITE_CONFIG.description}`,
        description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '首页',
            '主页'
        ]
    },
    knowledge: {
        title: `MySQL知识库 - ${SITE_CONFIG.name}`,
        description: '丰富的MySQL知识库，包含数据库优化、性能调优、最佳实践等专业内容。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '知识库',
            '教程',
            '最佳实践'
        ]
    },
    projects: {
        title: `项目管理 - ${SITE_CONFIG.name}`,
        description: '专业的项目管理工具，支持任务跟踪、进度管理、团队协作。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '项目管理',
            '任务跟踪',
            '团队协作'
        ]
    },
    reports: {
        title: `报告展示 - ${SITE_CONFIG.name}`,
        description: '支持图片、视频的多媒体项目报告展示平台。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '报告展示',
            '多媒体',
            '数据可视化'
        ]
    },
    about: {
        title: `关于我们 - ${SITE_CONFIG.name}`,
        description: '了解MySQLAi.de团队，我们的使命是为用户提供最专业的MySQL解决方案。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '关于我们',
            '团队介绍',
            '公司简介'
        ]
    },
    contact: {
        title: `联系我们 - ${SITE_CONFIG.name}`,
        description: '联系MySQLAi.de团队，获取专业的MySQL咨询和技术支持。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '联系我们',
            '技术支持',
            '咨询服务'
        ]
    },
    // 法律声明页面元数据
    terms: {
        title: `服务条款 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台服务使用条款和用户协议，明确用户权利义务，保障双方合法权益。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '服务条款',
            '用户协议',
            '使用条款',
            '服务协议',
            '法律声明'
        ]
    },
    privacy: {
        title: `隐私政策 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台用户隐私保护政策，详细说明个人信息收集、使用、保护措施。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '隐私政策',
            '个人信息保护',
            '数据保护',
            '隐私保护',
            '信息安全'
        ]
    },
    disclaimer: {
        title: `免责声明 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台服务免责条款和责任限制说明，明确服务范围和责任界限。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '免责声明',
            '责任限制',
            '法律免责',
            '服务限制',
            '风险提示'
        ]
    },
    cookies: {
        title: `Cookie政策 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台Cookie使用说明和管理指南，保障用户知情权和选择权。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'Cookie政策',
            'Cookie使用',
            '网站Cookie',
            'Cookie管理',
            '用户隐私'
        ]
    },
    // 工具页面元数据
    tools: {
        title: `MySQL工具集 - ${SITE_CONFIG.name}`,
        description: '专业的MySQL工具集合，包含ER图生成、数据库安装配置等实用工具，提升数据库开发效率。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'MySQL工具',
            '数据库工具',
            'ER图生成',
            'MySQL安装',
            '开发工具'
        ]
    },
    'tools-er-diagram': {
        title: `ER图生成工具 - ${SITE_CONFIG.name}`,
        description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式，提升数据库设计效率。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'ER图生成',
            '数据库关系图',
            '数据库设计',
            '可视化工具',
            '数据库建模'
        ]
    },
    'tools-mysql-installer': {
        title: `MySQL安装工具 - ${SITE_CONFIG.name}`,
        description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置，简化数据库部署流程。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'MySQL安装',
            '数据库安装',
            '自动配置',
            '版本管理',
            '数据库部署'
        ]
    }
};
const FEATURES_DATA = [
    {
        title: 'MySQL知识库',
        description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',
        icon: 'Database',
        features: [
            '数据库性能优化',
            '查询语句调优',
            '索引设计最佳实践',
            '架构设计指南'
        ]
    },
    {
        title: '项目管理',
        description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',
        icon: 'FolderOpen',
        features: [
            '任务分配与跟踪',
            '项目进度管理',
            '团队协作工具',
            '时间管理优化'
        ]
    },
    {
        title: '报告展示',
        description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',
        icon: 'BarChart3',
        features: [
            '多媒体报告支持',
            '数据可视化图表',
            '实时数据展示',
            '自定义报告模板'
        ]
    }
];
const ABOUT_FEATURES = [
    {
        title: '智能分析',
        description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
        icon: 'Brain'
    },
    {
        title: '专业咨询',
        description: '资深数据库专家团队，提供一对一的专业咨询服务。',
        icon: 'Users'
    },
    {
        title: '高效管理',
        description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
        icon: 'Zap'
    },
    {
        title: '透明报告',
        description: '详细的项目报告和数据分析，确保项目进展透明可控。',
        icon: 'FileText'
    },
    {
        title: '7x24支持',
        description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
        icon: 'Clock'
    }
];
const ADVANTAGES_DATA = [
    {
        title: '🌍 #1 MySQL专家',
        description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
        details: '覆盖全球8个地区，超过5万用户信赖',
        icon: '🌍'
    },
    {
        title: '📝 兼容性与支持',
        description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
        details: '支持MySQL 5.7到8.0的所有主流版本',
        icon: '📝'
    },
    {
        title: '💰 灵活计费',
        description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
        details: '透明计费，性价比最高的MySQL服务',
        icon: '💰'
    },
    {
        title: '⚡ 全球布局',
        description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
        details: '全球用户享受一致的高速服务体验',
        icon: '⚡'
    },
    {
        title: '⏰ 服务保障',
        description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
        details: '专业运维团队，99.9%服务可用性保证',
        icon: '⏰'
    },
    {
        title: '🎈 透明计费',
        description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
        details: '无隐藏费用，按实际使用量计费',
        icon: '🎈'
    }
];
const CONTACT_INFO = {
    supportHours: '7×24小时全天候支持',
    email: '<EMAIL>',
    phone: '+86 ************',
    address: '中国 · 北京 · 朝阳区',
    socialLinks: [
        {
            name: 'GitHub',
            href: 'https://github.com/mysqlai',
            icon: 'Github'
        },
        {
            name: '微信',
            href: '#',
            icon: 'MessageCircle'
        },
        {
            name: 'QQ群',
            href: '#',
            icon: 'Users'
        }
    ]
};
const FOOTER_SECTIONS = [
    {
        title: '产品服务',
        links: [
            {
                name: 'MySQL优化',
                href: '/services/optimization'
            },
            {
                name: '性能调优',
                href: '/services/tuning'
            },
            {
                name: '架构设计',
                href: '/services/architecture'
            },
            {
                name: '数据迁移',
                href: '/services/migration'
            }
        ]
    },
    {
        title: '解决方案',
        links: [
            {
                name: '企业级方案',
                href: '/solutions/enterprise'
            },
            {
                name: '云数据库',
                href: '/solutions/cloud'
            },
            {
                name: '高可用架构',
                href: '/solutions/ha'
            },
            {
                name: '灾备方案',
                href: '/solutions/disaster-recovery'
            }
        ]
    },
    {
        title: '学习资源',
        links: [
            {
                name: '技术博客',
                href: '/blog'
            },
            {
                name: '视频教程',
                href: '/tutorials'
            },
            {
                name: 'API文档',
                href: '/docs'
            },
            {
                name: '最佳实践',
                href: '/best-practices'
            }
        ]
    },
    {
        title: '关于我们',
        links: [
            {
                name: '公司介绍',
                href: '/about'
            },
            {
                name: '团队成员',
                href: '/team'
            },
            {
                name: '招聘信息',
                href: '/careers'
            },
            {
                name: '联系我们',
                href: '/contact'
            }
        ]
    }
];
const FOOTER_LEGAL_LINKS = [
    {
        name: '服务条款',
        href: '/terms'
    },
    {
        name: '隐私政策',
        href: '/privacy'
    },
    {
        name: '免责声明',
        href: '/disclaimer'
    }
];
const ANIMATION_CONFIG = {
    duration: {
        fast: 0.2,
        normal: 0.3,
        slow: 0.5
    },
    easing: {
        easeInOut: [
            0.4,
            0,
            0.2,
            1
        ],
        easeOut: [
            0,
            0,
            0.2,
            1
        ],
        easeIn: [
            0.4,
            0,
            1,
            1
        ]
    },
    delay: {
        none: 0,
        short: 0.1,
        medium: 0.2,
        long: 0.3
    }
};
const BREAKPOINTS = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
};
const ERROR_MESSAGES = {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    minLength: (min)=>`最少需要${min}个字符`,
    maxLength: (max)=>`最多允许${max}个字符`,
    network: '网络连接失败，请稍后重试',
    server: '服务器错误，请联系技术支持',
    unknown: '未知错误，请稍后重试'
};
const SUCCESS_MESSAGES = {
    formSubmit: '表单提交成功！',
    dataSaved: '数据保存成功！',
    emailSent: '邮件发送成功！',
    copied: '已复制到剪贴板'
};
}}),
"[project]/web-app/src/app/metadata.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - SEO元数据配置
// 专业的SEO优化配置，包含Open Graph、Twitter Cards等
__turbopack_context__.s({
    "baseMetadata": (()=>baseMetadata),
    "generateJsonLd": (()=>generateJsonLd),
    "generatePageMetadata": (()=>generatePageMetadata),
    "homeMetadata": (()=>homeMetadata),
    "siteConfig": (()=>siteConfig),
    "structuredData": (()=>structuredData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/constants.ts [app-rsc] (ecmascript)");
;
const baseMetadata = {
    title: {
        default: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title,
        template: `%s - ${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name}`
    },
    description: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].description,
    keywords: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].keywords,
    authors: [
        {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].author
        }
    ],
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].author,
    publisher: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
    // 基础元数据
    metadataBase: new URL(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url),
    alternates: {
        canonical: '/',
        languages: {
            'zh-CN': '/zh-CN',
            'en-US': '/en-US'
        }
    },
    // Open Graph配置
    openGraph: {
        type: 'website',
        locale: 'zh_CN',
        url: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url,
        title: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].description,
        siteName: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
        images: [
            {
                url: '/og-image.png',
                width: 1200,
                height: 630,
                alt: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title}`
            },
            {
                url: '/og-image-square.png',
                width: 1200,
                height: 1200,
                alt: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name} Logo`
            }
        ]
    },
    // Twitter Cards配置
    twitter: {
        card: 'summary_large_image',
        title: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].description,
        creator: '@mysqlai',
        site: '@mysqlai',
        images: [
            '/twitter-image.png'
        ]
    },
    // 应用程序配置
    applicationName: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
    appleWebApp: {
        capable: true,
        title: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
        statusBarStyle: 'default'
    },
    // 格式检测
    formatDetection: {
        telephone: false,
        date: false,
        address: false,
        email: false,
        url: false
    },
    // 图标配置
    icons: {
        icon: '/favicon.ico'
    },
    // 清单文件
    manifest: '/site.webmanifest',
    // 其他元数据
    other: {
        'msapplication-TileColor': '#00758F',
        'msapplication-config': '/browserconfig.xml',
        'theme-color': '#00758F'
    }
};
const homeMetadata = {
    ...baseMetadata,
    title: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title} - 专业的MySQL智能分析平台`,
    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。7×24小时专业技术支持。',
    keywords: [
        ...__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].keywords,
        'MySQL优化',
        '数据库性能',
        '智能分析',
        'AI驱动',
        '项目管理',
        '报告展示',
        '技术支持',
        '企业级',
        '专业服务'
    ],
    openGraph: {
        ...baseMetadata.openGraph,
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title} - 专业的MySQL智能分析平台`,
        description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
        url: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url
    },
    twitter: {
        ...baseMetadata.twitter,
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title} - 专业的MySQL智能分析平台`,
        description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。'
    }
};
const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
    alternateName: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title,
    url: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url,
    logo: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url}/logo.png`,
    description: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].description,
    foundingDate: '2020',
    founder: {
        '@type': 'Person',
        name: 'MySQLAi Team'
    },
    contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+86-************',
        contactType: 'customer service',
        availableLanguage: [
            'Chinese',
            'English'
        ],
        areaServed: 'CN',
        hoursAvailable: {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: [
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
                'Sunday'
            ],
            opens: '00:00',
            closes: '23:59'
        }
    },
    address: {
        '@type': 'PostalAddress',
        addressLocality: '北京市',
        addressRegion: '朝阳区',
        addressCountry: 'CN',
        streetAddress: '科技园区'
    },
    sameAs: [
        'https://github.com/mysqlai',
        'https://twitter.com/mysqlai',
        'https://linkedin.com/company/mysqlai'
    ],
    offers: {
        '@type': 'Offer',
        category: 'Database Services',
        description: 'MySQL数据库优化和管理服务',
        areaServed: 'CN'
    },
    knowsAbout: [
        'MySQL',
        'Database Optimization',
        'Performance Tuning',
        'Project Management',
        'AI Analysis',
        'Technical Support'
    ],
    serviceType: [
        'MySQL知识库',
        '项目管理',
        '报告展示',
        'AI智能分析',
        '技术支持'
    ]
};
const siteConfig = {
    name: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name,
    title: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].title,
    description: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].description,
    url: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url,
    ogImage: `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url}/og-image.png`,
    links: {
        twitter: 'https://twitter.com/mysqlai',
        github: 'https://github.com/mysqlai',
        linkedin: 'https://linkedin.com/company/mysqlai'
    },
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].author
};
function generatePageMetadata(title, description, path = '', image) {
    const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SITE_CONFIG"].url}${path}`;
    const ogImage = image || '/og-image.png';
    return {
        title,
        description,
        openGraph: {
            title,
            description,
            url,
            images: [
                {
                    url: ogImage,
                    width: 1200,
                    height: 630,
                    alt: title
                }
            ]
        },
        twitter: {
            title,
            description,
            images: [
                ogImage
            ]
        },
        alternates: {
            canonical: url
        }
    };
}
function generateJsonLd(data) {
    return {
        __html: JSON.stringify(data)
    };
}
}}),
"[project]/web-app/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Header.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/layout/Footer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$app$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/app/metadata.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$app$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["homeMetadata"];
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "zh-CN",
        className: "scroll-smooth",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$app$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateJsonLd"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$app$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["structuredData"])
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "dns-prefetch",
                        href: "//github.com"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 26,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "dns-prefetch",
                        href: "//twitter.com"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "dns-prefetch",
                        href: "//linkedin.com"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/app/layout.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: "antialiased bg-white text-mysql-text",
                suppressHydrationWarning: true,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "pt-16 lg:pt-20 relative",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 35,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/web-app/src/app/layout.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/app/layout.tsx",
                lineNumber: 30,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/app/layout.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=_d0af109d._.js.map