module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 工具函数库
// 提供通用的工具函数和辅助方法
__turbopack_context__.s({
    "camelCase": (()=>camelCase),
    "capitalize": (()=>capitalize),
    "cn": (()=>cn),
    "copyToClipboard": (()=>copyToClipboard),
    "debounce": (()=>debounce),
    "deepClone": (()=>deepClone),
    "delay": (()=>delay),
    "downloadFile": (()=>downloadFile),
    "formatDate": (()=>formatDate),
    "formatFileSize": (()=>formatFileSize),
    "formatNumber": (()=>formatNumber),
    "generateColorVariants": (()=>generateColorVariants),
    "generateId": (()=>generateId),
    "getIconEmoji": (()=>getIconEmoji),
    "getNestedValue": (()=>getNestedValue),
    "groupBy": (()=>groupBy),
    "isDarkMode": (()=>isDarkMode),
    "isMobile": (()=>isMobile),
    "isValidEmail": (()=>isValidEmail),
    "isValidPhone": (()=>isValidPhone),
    "kebabCase": (()=>kebabCase),
    "mapDatabaseArticleToFrontend": (()=>mapDatabaseArticleToFrontend),
    "mapDatabaseArticlesToFrontend": (()=>mapDatabaseArticlesToFrontend),
    "mapDatabaseCategoriesToFrontend": (()=>mapDatabaseCategoriesToFrontend),
    "mapDatabaseCategoryToFrontend": (()=>mapDatabaseCategoryToFrontend),
    "mapDatabaseCodeExampleToFrontend": (()=>mapDatabaseCodeExampleToFrontend),
    "mapDatabaseCodeExamplesToFrontend": (()=>mapDatabaseCodeExamplesToFrontend),
    "mapFrontendArticleToDatabase": (()=>mapFrontendArticleToDatabase),
    "mapFrontendCategoryToDatabase": (()=>mapFrontendCategoryToDatabase),
    "mapFrontendCodeExampleToDatabase": (()=>mapFrontendCodeExampleToDatabase),
    "safeFieldMap": (()=>safeFieldMap),
    "scrollToElement": (()=>scrollToElement),
    "setNestedValue": (()=>setNestedValue),
    "throttle": (()=>throttle),
    "truncateText": (()=>truncateText),
    "uniqueArray": (()=>uniqueArray),
    "validateMappedData": (()=>validateMappedData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
;
// 图标名称到 emoji 的映射
const ICON_MAP = {
    'Database': '🗄️',
    'Server': '🖥️',
    'Table': '📋',
    'Edit': '✏️',
    'Search': '🔍',
    'Settings': '⚙️',
    'BookOpen': '📖',
    'Code': '💻',
    'FileText': '📄',
    'FolderOpen': '📁',
    'Users': '👥',
    'Mail': '📧',
    'Home': '🏠',
    'BarChart3': '📊',
    'Wrench': '🔧',
    'GitBranch': '🌿',
    'Download': '⬇️'
};
function getIconEmoji(iconName) {
    return ICON_MAP[iconName] || '📁';
}
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs);
}
function delay(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'Asia/Shanghai'
    };
    const finalOptions = {
        ...defaultOptions,
        ...options
    };
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('zh-CN', finalOptions).format(dateObj);
}
function formatNumber(num, options = {}) {
    return new Intl.NumberFormat('zh-CN', options).format(num);
}
function generateId(prefix = 'id') {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
}
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
function kebabCase(str) {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').replace(/[\s_]+/g, '-').toLowerCase();
}
function camelCase(str) {
    return str.replace(/[-_\s]+(.)?/g, (_, char)=>char ? char.toUpperCase() : '').replace(/^[A-Z]/, (char)=>char.toLowerCase());
}
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map((item)=>deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for(const key in obj){
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    return obj;
}
function getNestedValue(obj, path, defaultValue = undefined) {
    const keys = path.split('.');
    let result = obj;
    for (const key of keys){
        if (result === null || result === undefined || !(key in result)) {
            return defaultValue;
        }
        result = result[key];
    }
    return result;
}
function setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = obj;
    for (const key of keys){
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }
    current[lastKey] = value;
}
function uniqueArray(array, key) {
    if (!key) {
        return [
            ...new Set(array)
        ];
    }
    const seen = new Set();
    return array.filter((item)=>{
        const value = item[key];
        if (seen.has(value)) {
            return false;
        }
        seen.add(value);
        return true;
    });
}
function groupBy(array, key) {
    return array.reduce((groups, item)=>{
        const groupKey = String(item[key]);
        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(item);
        return groups;
    }, {});
}
function isMobile() {
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
}
function isDarkMode() {
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
}
function scrollToElement(elementId, offset = 0, behavior = 'smooth') {
    const element = document.getElementById(elementId);
    if (element) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior
        });
    }
}
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('复制失败:', err);
        return false;
    }
}
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function generateColorVariants(baseColor) {
    // 这是一个简化版本，实际项目中可能需要更复杂的颜色计算
    return {
        50: `${baseColor}0D`,
        100: `${baseColor}1A`,
        200: `${baseColor}33`,
        300: `${baseColor}4D`,
        400: `${baseColor}66`,
        500: baseColor,
        600: `${baseColor}CC`,
        700: `${baseColor}B3`,
        800: `${baseColor}99`,
        900: `${baseColor}80`
    };
}
function mapDatabaseCategoryToFrontend(dbCategory) {
    return {
        id: dbCategory.id,
        name: dbCategory.name,
        description: dbCategory.description,
        icon: dbCategory.icon,
        color: dbCategory.color,
        order_index: dbCategory.order_index,
        created_at: dbCategory.created_at
    };
}
function mapFrontendCategoryToDatabase(category) {
    return {
        id: category.id || generateId('cat'),
        name: category.name,
        description: category.description,
        icon: category.icon,
        color: category.color,
        order_index: category.order_index
    };
}
function mapDatabaseCodeExampleToFrontend(dbCodeExample) {
    return {
        id: dbCodeExample.id,
        article_id: dbCodeExample.article_id,
        title: dbCodeExample.title,
        code: dbCodeExample.code,
        language: dbCodeExample.language,
        description: dbCodeExample.description,
        order_index: dbCodeExample.order_index,
        created_at: dbCodeExample.created_at
    };
}
function mapFrontendCodeExampleToDatabase(codeExample) {
    return {
        id: codeExample.id || generateId('code'),
        article_id: codeExample.articleId,
        title: codeExample.title,
        code: codeExample.code,
        language: codeExample.language,
        description: codeExample.description || null,
        order_index: 0
    };
}
function mapDatabaseArticleToFrontend(dbArticle) {
    return {
        id: dbArticle.id,
        title: dbArticle.title,
        description: dbArticle.description,
        content: dbArticle.content,
        category_id: dbArticle.category_id,
        tags: dbArticle.tags,
        difficulty: dbArticle.difficulty,
        order_index: dbArticle.order_index,
        last_updated: dbArticle.last_updated,
        created_at: dbArticle.created_at,
        updated_at: dbArticle.updated_at
    };
}
function mapFrontendArticleToDatabase(article) {
    return {
        id: article.id || generateId('article'),
        title: article.title,
        description: article.description,
        content: article.content,
        category_id: article.category_id,
        tags: article.tags && article.tags.length > 0 ? article.tags : null,
        difficulty: article.difficulty,
        order_index: article.order_index,
        last_updated: article.last_updated
    };
}
function mapDatabaseCategoriesToFrontend(dbCategories) {
    return dbCategories.map(mapDatabaseCategoryToFrontend);
}
function mapDatabaseArticlesToFrontend(dbArticles) {
    return dbArticles.map((article)=>mapDatabaseArticleToFrontend(article));
}
function mapDatabaseCodeExamplesToFrontend(dbCodeExamples) {
    return dbCodeExamples.map(mapDatabaseCodeExampleToFrontend);
}
function safeFieldMap(value, mapper, defaultValue) {
    return value !== null && value !== undefined ? mapper(value) : defaultValue;
}
function validateMappedData(data, requiredFields) {
    const errors = [];
    data.forEach((item, index)=>{
        requiredFields.forEach((field)=>{
            if (!item[field]) {
                errors.push(`Item ${index} missing required field: ${String(field)}`);
            }
        });
    });
    return {
        isValid: errors.length === 0,
        errors
    };
}
}}),
"[project]/web-app/src/lib/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 常量配置文件
// 包含项目中使用的所有常量、配置和静态数据
__turbopack_context__.s({
    "ABOUT_FEATURES": (()=>ABOUT_FEATURES),
    "ADVANTAGES_DATA": (()=>ADVANTAGES_DATA),
    "ANIMATION_CONFIG": (()=>ANIMATION_CONFIG),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "CHEN_ER_COLORS": (()=>CHEN_ER_COLORS),
    "CONTACT_INFO": (()=>CONTACT_INFO),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURES_DATA": (()=>FEATURES_DATA),
    "FOOTER_LEGAL_LINKS": (()=>FOOTER_LEGAL_LINKS),
    "FOOTER_SECTIONS": (()=>FOOTER_SECTIONS),
    "PAGE_METADATA": (()=>PAGE_METADATA),
    "SITE_CONFIG": (()=>SITE_CONFIG),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "THEME_COLORS": (()=>THEME_COLORS)
});
const SITE_CONFIG = {
    name: 'MySQLAi.de',
    title: 'MySQL智能分析专家',
    description: '专业的数据库知识分享与项目管理平台',
    url: 'https://mysqlai.de',
    author: 'MySQLAi Team',
    keywords: [
        'MySQL',
        '数据库',
        'AI分析',
        '项目管理',
        '知识分享',
        '性能优化'
    ]
};
const THEME_COLORS = {
    primary: '#00758F',
    primaryDark: '#003545',
    primaryLight: '#E6F3F7',
    accent: '#0066CC',
    text: '#2D3748',
    textLight: '#718096',
    border: '#E2E8F0',
    success: '#38A169',
    warning: '#D69E2E',
    error: '#E53E3E'
};
const CHEN_ER_COLORS = {
    primary: '#000000',
    primaryDark: '#000000',
    primaryLight: '#FFFFFF',
    accent: '#000000',
    text: '#000000',
    textLight: '#000000',
    border: '#000000',
    success: '#000000',
    warning: '#000000',
    error: '#000000',
    white: '#FFFFFF',
    background: '#FFFFFF'
};
const PAGE_METADATA = {
    home: {
        title: `${SITE_CONFIG.title} - ${SITE_CONFIG.description}`,
        description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '首页',
            '主页'
        ]
    },
    knowledge: {
        title: `MySQL知识库 - ${SITE_CONFIG.name}`,
        description: '丰富的MySQL知识库，包含数据库优化、性能调优、最佳实践等专业内容。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '知识库',
            '教程',
            '最佳实践'
        ]
    },
    projects: {
        title: `项目管理 - ${SITE_CONFIG.name}`,
        description: '专业的项目管理工具，支持任务跟踪、进度管理、团队协作。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '项目管理',
            '任务跟踪',
            '团队协作'
        ]
    },
    reports: {
        title: `报告展示 - ${SITE_CONFIG.name}`,
        description: '支持图片、视频的多媒体项目报告展示平台。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '报告展示',
            '多媒体',
            '数据可视化'
        ]
    },
    about: {
        title: `关于我们 - ${SITE_CONFIG.name}`,
        description: '了解MySQLAi.de团队，我们的使命是为用户提供最专业的MySQL解决方案。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '关于我们',
            '团队介绍',
            '公司简介'
        ]
    },
    contact: {
        title: `联系我们 - ${SITE_CONFIG.name}`,
        description: '联系MySQLAi.de团队，获取专业的MySQL咨询和技术支持。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '联系我们',
            '技术支持',
            '咨询服务'
        ]
    },
    // 法律声明页面元数据
    terms: {
        title: `服务条款 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台服务使用条款和用户协议，明确用户权利义务，保障双方合法权益。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '服务条款',
            '用户协议',
            '使用条款',
            '服务协议',
            '法律声明'
        ]
    },
    privacy: {
        title: `隐私政策 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台用户隐私保护政策，详细说明个人信息收集、使用、保护措施。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '隐私政策',
            '个人信息保护',
            '数据保护',
            '隐私保护',
            '信息安全'
        ]
    },
    disclaimer: {
        title: `免责声明 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台服务免责条款和责任限制说明，明确服务范围和责任界限。',
        keywords: [
            ...SITE_CONFIG.keywords,
            '免责声明',
            '责任限制',
            '法律免责',
            '服务限制',
            '风险提示'
        ]
    },
    cookies: {
        title: `Cookie政策 - ${SITE_CONFIG.name}`,
        description: 'MySQLAi.de平台Cookie使用说明和管理指南，保障用户知情权和选择权。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'Cookie政策',
            'Cookie使用',
            '网站Cookie',
            'Cookie管理',
            '用户隐私'
        ]
    },
    // 工具页面元数据
    tools: {
        title: `MySQL工具集 - ${SITE_CONFIG.name}`,
        description: '专业的MySQL工具集合，包含ER图生成、数据库安装配置等实用工具，提升数据库开发效率。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'MySQL工具',
            '数据库工具',
            'ER图生成',
            'MySQL安装',
            '开发工具'
        ]
    },
    'tools-er-diagram': {
        title: `ER图生成工具 - ${SITE_CONFIG.name}`,
        description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式，提升数据库设计效率。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'ER图生成',
            '数据库关系图',
            '数据库设计',
            '可视化工具',
            '数据库建模'
        ]
    },
    'tools-mysql-installer': {
        title: `MySQL安装工具 - ${SITE_CONFIG.name}`,
        description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置，简化数据库部署流程。',
        keywords: [
            ...SITE_CONFIG.keywords,
            'MySQL安装',
            '数据库安装',
            '自动配置',
            '版本管理',
            '数据库部署'
        ]
    }
};
const FEATURES_DATA = [
    {
        title: 'MySQL知识库',
        description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',
        icon: 'Database',
        features: [
            '数据库性能优化',
            '查询语句调优',
            '索引设计最佳实践',
            '架构设计指南'
        ]
    },
    {
        title: '项目管理',
        description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',
        icon: 'FolderOpen',
        features: [
            '任务分配与跟踪',
            '项目进度管理',
            '团队协作工具',
            '时间管理优化'
        ]
    },
    {
        title: '报告展示',
        description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',
        icon: 'BarChart3',
        features: [
            '多媒体报告支持',
            '数据可视化图表',
            '实时数据展示',
            '自定义报告模板'
        ]
    }
];
const ABOUT_FEATURES = [
    {
        title: '智能分析',
        description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
        icon: 'Brain'
    },
    {
        title: '专业咨询',
        description: '资深数据库专家团队，提供一对一的专业咨询服务。',
        icon: 'Users'
    },
    {
        title: '高效管理',
        description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
        icon: 'Zap'
    },
    {
        title: '透明报告',
        description: '详细的项目报告和数据分析，确保项目进展透明可控。',
        icon: 'FileText'
    },
    {
        title: '7x24支持',
        description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
        icon: 'Clock'
    }
];
const ADVANTAGES_DATA = [
    {
        title: '🌍 #1 MySQL专家',
        description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
        details: '覆盖全球8个地区，超过5万用户信赖',
        icon: '🌍'
    },
    {
        title: '📝 兼容性与支持',
        description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
        details: '支持MySQL 5.7到8.0的所有主流版本',
        icon: '📝'
    },
    {
        title: '💰 灵活计费',
        description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
        details: '透明计费，性价比最高的MySQL服务',
        icon: '💰'
    },
    {
        title: '⚡ 全球布局',
        description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
        details: '全球用户享受一致的高速服务体验',
        icon: '⚡'
    },
    {
        title: '⏰ 服务保障',
        description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
        details: '专业运维团队，99.9%服务可用性保证',
        icon: '⏰'
    },
    {
        title: '🎈 透明计费',
        description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
        details: '无隐藏费用，按实际使用量计费',
        icon: '🎈'
    }
];
const CONTACT_INFO = {
    supportHours: '7×24小时全天候支持',
    email: '<EMAIL>',
    phone: '+86 ************',
    address: '中国 · 北京 · 朝阳区',
    socialLinks: [
        {
            name: 'GitHub',
            href: 'https://github.com/mysqlai',
            icon: 'Github'
        },
        {
            name: '微信',
            href: '#',
            icon: 'MessageCircle'
        },
        {
            name: 'QQ群',
            href: '#',
            icon: 'Users'
        }
    ]
};
const FOOTER_SECTIONS = [
    {
        title: '产品服务',
        links: [
            {
                name: 'MySQL优化',
                href: '/services/optimization'
            },
            {
                name: '性能调优',
                href: '/services/tuning'
            },
            {
                name: '架构设计',
                href: '/services/architecture'
            },
            {
                name: '数据迁移',
                href: '/services/migration'
            }
        ]
    },
    {
        title: '解决方案',
        links: [
            {
                name: '企业级方案',
                href: '/solutions/enterprise'
            },
            {
                name: '云数据库',
                href: '/solutions/cloud'
            },
            {
                name: '高可用架构',
                href: '/solutions/ha'
            },
            {
                name: '灾备方案',
                href: '/solutions/disaster-recovery'
            }
        ]
    },
    {
        title: '学习资源',
        links: [
            {
                name: '技术博客',
                href: '/blog'
            },
            {
                name: '视频教程',
                href: '/tutorials'
            },
            {
                name: 'API文档',
                href: '/docs'
            },
            {
                name: '最佳实践',
                href: '/best-practices'
            }
        ]
    },
    {
        title: '关于我们',
        links: [
            {
                name: '公司介绍',
                href: '/about'
            },
            {
                name: '团队成员',
                href: '/team'
            },
            {
                name: '招聘信息',
                href: '/careers'
            },
            {
                name: '联系我们',
                href: '/contact'
            }
        ]
    }
];
const FOOTER_LEGAL_LINKS = [
    {
        name: '服务条款',
        href: '/terms'
    },
    {
        name: '隐私政策',
        href: '/privacy'
    },
    {
        name: '免责声明',
        href: '/disclaimer'
    }
];
const ANIMATION_CONFIG = {
    duration: {
        fast: 0.2,
        normal: 0.3,
        slow: 0.5
    },
    easing: {
        easeInOut: [
            0.4,
            0,
            0.2,
            1
        ],
        easeOut: [
            0,
            0,
            0.2,
            1
        ],
        easeIn: [
            0.4,
            0,
            1,
            1
        ]
    },
    delay: {
        none: 0,
        short: 0.1,
        medium: 0.2,
        long: 0.3
    }
};
const BREAKPOINTS = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
};
const ERROR_MESSAGES = {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    minLength: (min)=>`最少需要${min}个字符`,
    maxLength: (max)=>`最多允许${max}个字符`,
    network: '网络连接失败，请稍后重试',
    server: '服务器错误，请联系技术支持',
    unknown: '未知错误，请稍后重试'
};
const SUCCESS_MESSAGES = {
    formSubmit: '表单提交成功！',
    dataSaved: '数据保存成功！',
    emailSent: '邮件发送成功！',
    copied: '已复制到剪贴板'
};
}}),
"[project]/web-app/src/lib/navigation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 导航配置文件
// 管理网站导航相关的配置和工具函数
__turbopack_context__.s({
    "FOOTER_NAVIGATION": (()=>FOOTER_NAVIGATION),
    "LEGAL_LINKS": (()=>LEGAL_LINKS),
    "MAIN_NAVIGATION": (()=>MAIN_NAVIGATION),
    "NAV_ANIMATIONS": (()=>NAV_ANIMATIONS),
    "NAV_STYLES": (()=>NAV_STYLES),
    "SOCIAL_LINKS": (()=>SOCIAL_LINKS),
    "TOOLS_NAVIGATION": (()=>TOOLS_NAVIGATION),
    "getBreadcrumbs": (()=>getBreadcrumbs),
    "getFullUrl": (()=>getFullUrl),
    "getNavItemAriaLabel": (()=>getNavItemAriaLabel),
    "getPageTitle": (()=>getPageTitle),
    "getRelatedNavItems": (()=>getRelatedNavItems),
    "isActiveNavItem": (()=>isActiveNavItem),
    "isExternalLink": (()=>isExternalLink)
});
const TOOLS_NAVIGATION = [
    {
        name: 'ER图生成工具',
        href: '/tools/er-diagram',
        icon: 'GitBranch'
    },
    {
        name: 'MySQL安装工具',
        href: '/tools/mysql-installer',
        icon: 'Download'
    }
];
const MAIN_NAVIGATION = [
    {
        name: '首页',
        href: '/',
        icon: 'Home'
    },
    {
        name: '知识库',
        href: '/knowledge',
        icon: 'BookOpen'
    },
    {
        name: '项目管理',
        href: '/projects',
        icon: 'FolderOpen'
    },
    {
        name: '报告展示',
        href: '/reports',
        icon: 'BarChart3'
    },
    {
        name: '关于我们',
        href: '/about',
        icon: 'Users'
    },
    {
        name: '工具',
        href: '/tools',
        icon: 'Wrench',
        children: TOOLS_NAVIGATION
    },
    {
        name: '联系我们',
        href: '/contact',
        icon: 'Mail'
    }
];
const FOOTER_NAVIGATION = {
    产品服务: [
        {
            name: 'MySQL优化',
            href: '/services/optimization'
        },
        {
            name: '性能调优',
            href: '/services/tuning'
        },
        {
            name: '架构设计',
            href: '/services/architecture'
        },
        {
            name: '数据迁移',
            href: '/services/migration'
        }
    ],
    解决方案: [
        {
            name: '企业级方案',
            href: '/solutions/enterprise'
        },
        {
            name: '云数据库',
            href: '/solutions/cloud'
        },
        {
            name: '高可用架构',
            href: '/solutions/ha'
        },
        {
            name: '灾备方案',
            href: '/solutions/disaster-recovery'
        }
    ],
    学习资源: [
        {
            name: '技术博客',
            href: '/blog'
        },
        {
            name: '视频教程',
            href: '/tutorials'
        },
        {
            name: 'API文档',
            href: '/docs'
        },
        {
            name: '最佳实践',
            href: '/best-practices'
        }
    ],
    关于我们: [
        {
            name: '公司介绍',
            href: '/about'
        },
        {
            name: '团队成员',
            href: '/team'
        },
        {
            name: '招聘信息',
            href: '/careers'
        },
        {
            name: '联系我们',
            href: '/contact'
        }
    ]
};
const SOCIAL_LINKS = [
    {
        name: 'GitHub',
        href: 'https://github.com/mysqlai',
        icon: 'Github',
        isExternal: true
    },
    {
        name: '微信公众号',
        href: '#wechat',
        icon: 'MessageCircle'
    },
    {
        name: 'QQ技术群',
        href: '#qq-group',
        icon: 'Users'
    },
    {
        name: '技术博客',
        href: '/blog',
        icon: 'BookOpen'
    }
];
const LEGAL_LINKS = [
    {
        name: '服务条款',
        href: '/terms'
    },
    {
        name: '隐私政策',
        href: '/privacy'
    },
    {
        name: '免责声明',
        href: '/disclaimer'
    }
];
function isActiveNavItem(href, currentPath) {
    if (href === '/') {
        return currentPath === '/';
    }
    return currentPath.startsWith(href);
}
function getFullUrl(href, baseUrl = '') {
    if (href.startsWith('http') || href.startsWith('//')) {
        return href;
    }
    return `${baseUrl}${href}`;
}
function isExternalLink(href) {
    return href.startsWith('http') || href.startsWith('//');
}
function getNavItemAriaLabel(item) {
    if (item.isExternal) {
        return `${item.name} (在新窗口中打开)`;
    }
    return `前往${item.name}页面`;
}
function getBreadcrumbs(pathname) {
    const breadcrumbs = [
        {
            name: '首页',
            href: '/'
        }
    ];
    if (pathname === '/') {
        return breadcrumbs;
    }
    // 页面的中文名称映射
    const pageNames = {
        'terms': '服务条款',
        'privacy': '隐私政策',
        'disclaimer': '免责声明',
        'cookies': 'Cookie政策',
        'contact': '联系我们',
        'about': '关于我们'
    };
    // 查找匹配的主导航项
    const mainNavItem = MAIN_NAVIGATION.find((item)=>pathname.startsWith(item.href) && item.href !== '/');
    if (mainNavItem) {
        breadcrumbs.push(mainNavItem);
        // 如果找到了主导航项，直接返回，不再处理子页面逻辑
        return breadcrumbs;
    }
    // 处理子页面路径
    const pathSegments = pathname.split('/').filter(Boolean);
    // 对于单级页面，只有在主导航中没有找到时才添加
    if (pathSegments.length === 1) {
        const segment = pathSegments[0];
        const name = pageNames[segment];
        if (!mainNavItem) {
            // 只有在主导航中没有找到时，才添加到面包屑
            if (name) {
                // 使用页面名称映射
                breadcrumbs.push({
                    name,
                    href: pathname
                });
            } else {
                // 生成fallback名称
                const fallbackName = segment.split('-').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                breadcrumbs.push({
                    name: fallbackName,
                    href: pathname
                });
            }
        }
    } else if (pathSegments.length > 1) {
        // 处理多级路径
        for(let i = 1; i < pathSegments.length; i++){
            const segment = pathSegments[i];
            const href = '/' + pathSegments.slice(0, i + 1).join('/');
            const name = pageNames[segment] || segment.split('-').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
            breadcrumbs.push({
                name,
                href
            });
        }
    }
    return breadcrumbs;
}
function getPageTitle(pathname, siteName = 'MySQLAi.de') {
    if (pathname === '/') {
        return `${siteName} - MySQL智能分析专家`;
    }
    const navItem = MAIN_NAVIGATION.find((item)=>pathname.startsWith(item.href) && item.href !== '/');
    if (navItem) {
        return `${navItem.name} - ${siteName}`;
    }
    // 从路径生成标题
    const pathSegments = pathname.split('/').filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];
    const title = lastSegment.split('-').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    return `${title} - ${siteName}`;
}
function getRelatedNavItems(currentHref, maxItems = 3) {
    return MAIN_NAVIGATION.filter((item)=>item.href !== currentHref && item.href !== '/').slice(0, maxItems);
}
const NAV_ANIMATIONS = {
    // 移动端菜单动画
    mobileMenu: {
        initial: {
            x: '100%'
        },
        animate: {
            x: 0
        },
        exit: {
            x: '100%'
        },
        transition: {
            type: 'spring',
            damping: 25,
            stiffness: 200
        }
    },
    // 背景遮罩动画
    overlay: {
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        exit: {
            opacity: 0
        },
        transition: {
            duration: 0.2
        }
    },
    // 菜单项动画
    menuItem: {
        initial: {
            opacity: 0,
            x: 20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        transition: (index)=>({
                delay: index * 0.1
            })
    },
    // Header背景变化动画
    headerBackground: {
        transition: {
            duration: 0.3
        }
    }
};
const NAV_STYLES = {
    // 桌面端导航链接
    desktopLink: [
        'relative px-3 py-2 text-sm font-medium transition-all duration-200',
        'text-mysql-text hover:text-mysql-primary',
        'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5',
        'before:bg-mysql-primary before:transition-all before:duration-300',
        'hover:before:w-full'
    ].join(' '),
    // 移动端菜单链接
    mobileLink: [
        'flex items-center px-4 py-3 text-mysql-text',
        'hover:text-mysql-primary hover:bg-mysql-primary-light',
        'rounded-lg transition-all duration-200'
    ].join(' '),
    // 主要操作按钮
    primaryButton: [
        'inline-flex items-center px-4 py-2 bg-mysql-primary text-white',
        'text-sm font-medium rounded-lg hover:bg-mysql-primary-dark',
        'transition-colors duration-200'
    ].join(' '),
    // 汉堡菜单按钮
    hamburgerButton: [
        'lg:hidden p-2 rounded-lg text-mysql-text',
        'hover:text-mysql-primary hover:bg-mysql-primary-light',
        'transition-all duration-200'
    ].join(' ')
};
}}),
"[project]/web-app/src/components/ui/DropdownMenu.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - DropdownMenu下拉菜单组件
// 支持键盘导航、无障碍访问和平滑动画的可复用下拉菜单
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const DropdownMenu = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef(({ trigger, items, align = 'left', onItemClick, isOpen: controlledIsOpen, onOpenChange, className, ...props }, ref)=>{
    // 路由导航
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // 状态管理
    const [internalIsOpen, setInternalIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [focusedIndex, setFocusedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(-1);
    // 使用受控或非受控状态
    const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;
    const setIsOpen = onOpenChange || setInternalIsOpen;
    // Refs
    const triggerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const menuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const itemRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]);
    // 切换菜单状态
    const toggleMenu = ()=>{
        setIsOpen(!isOpen);
        if (!isOpen) {
            setFocusedIndex(-1);
        }
    };
    // 关闭菜单
    const closeMenu = ()=>{
        setIsOpen(false);
        setFocusedIndex(-1);
        triggerRef.current?.focus();
    };
    // 处理菜单项点击
    const handleItemClick = (item, event)=>{
        event?.preventDefault();
        onItemClick?.(item);
        // 导航到目标页面
        if (item.isExternal) {
            window.open(item.href, '_blank', 'noopener,noreferrer');
        } else {
            router.push(item.href);
        }
        closeMenu();
    };
    // 键盘导航处理
    const handleKeyDown = (event)=>{
        switch(event.key){
            case 'Escape':
                event.preventDefault();
                closeMenu();
                break;
            case 'ArrowDown':
                event.preventDefault();
                if (!isOpen) {
                    setIsOpen(true);
                    setFocusedIndex(0);
                } else {
                    const nextIndex = focusedIndex < items.length - 1 ? focusedIndex + 1 : 0;
                    setFocusedIndex(nextIndex);
                    itemRefs.current[nextIndex]?.focus();
                }
                break;
            case 'ArrowUp':
                event.preventDefault();
                if (isOpen) {
                    const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : items.length - 1;
                    setFocusedIndex(prevIndex);
                    itemRefs.current[prevIndex]?.focus();
                }
                break;
            case 'Enter':
            case ' ':
                event.preventDefault();
                if (!isOpen) {
                    setIsOpen(true);
                    setFocusedIndex(0);
                } else if (focusedIndex >= 0) {
                    const item = items[focusedIndex];
                    handleItemClick(item);
                }
                break;
            case 'Tab':
                if (isOpen) {
                    closeMenu();
                }
                break;
        }
    };
    // 点击外部关闭菜单
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (isOpen && triggerRef.current && menuRef.current && !triggerRef.current.contains(event.target) && !menuRef.current.contains(event.target)) {
                closeMenu();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, [
        isOpen
    ]);
    // 对齐样式
    const alignmentStyles = {
        left: 'left-0',
        right: 'right-0',
        center: 'left-1/2 transform -translate-x-1/2'
    };
    // 动画配置
    const menuVariants = {
        hidden: {
            opacity: 0,
            scale: 0.95,
            y: -10
        },
        visible: {
            opacity: 1,
            scale: 1,
            y: 0
        },
        exit: {
            opacity: 0,
            scale: 0.95,
            y: -10
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('relative inline-block', className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                ref: triggerRef,
                type: "button",
                onClick: toggleMenu,
                onKeyDown: handleKeyDown,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('inline-flex items-center space-x-1 px-3 py-2 text-sm font-medium', 'text-mysql-text hover:text-mysql-primary transition-colors duration-200', 'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30 rounded-lg', isOpen && 'text-mysql-primary'),
                "aria-expanded": isOpen ? 'true' : 'false',
                "aria-haspopup": "true",
                "aria-label": "打开菜单",
                children: [
                    trigger,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('w-4 h-4 transition-transform duration-200', isOpen && 'rotate-180')
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                        lineNumber: 185,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                lineNumber: 169,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    ref: menuRef,
                    variants: menuVariants,
                    initial: "hidden",
                    animate: "visible",
                    exit: "exit",
                    transition: {
                        duration: 0.2,
                        ease: 'easeOut'
                    },
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('absolute top-full mt-2 w-56 bg-white rounded-lg shadow-xl border border-mysql-border z-50', alignmentStyles[align]),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-2",
                        role: "menu",
                        "aria-orientation": "vertical",
                        children: items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                ref: (el)=>{
                                    itemRefs.current[index] = el;
                                },
                                href: item.href,
                                onClick: (e)=>handleItemClick(item, e),
                                onKeyDown: (e)=>{
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        handleItemClick(item);
                                    }
                                },
                                onFocus: ()=>setFocusedIndex(index),
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center px-4 py-3 text-sm text-mysql-text', 'hover:bg-mysql-primary-light hover:text-mysql-primary', 'focus:bg-mysql-primary-light focus:text-mysql-primary focus:outline-none', 'transition-colors duration-200 cursor-pointer', focusedIndex === index && 'bg-mysql-primary-light text-mysql-primary'),
                                role: "menuitem",
                                tabIndex: -1,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-medium",
                                    children: item.name
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                                    lineNumber: 234,
                                    columnNumber: 19
                                }, this)
                            }, item.href, false, {
                                fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                                lineNumber: 210,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                        lineNumber: 208,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                    lineNumber: 196,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
                lineNumber: 194,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/ui/DropdownMenu.tsx",
        lineNumber: 163,
        columnNumber: 5
    }, this);
});
DropdownMenu.displayName = 'DropdownMenu';
const __TURBOPACK__default__export__ = DropdownMenu;
}}),
"[project]/web-app/src/components/layout/Header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Header)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - Header导航组件
// 专业的响应式导航栏，支持桌面和移动端
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-ssr] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-ssr] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$navigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/navigation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$DropdownMenu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/ui/DropdownMenu.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
function Header({ className }) {
    const [isMenuOpen, setIsMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isScrolled, setIsScrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [expandedSubmenus, setExpandedSubmenus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // 监听滚动事件，改变Header背景
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleScroll = ()=>{
            setIsScrolled(window.scrollY > 10);
        };
        window.addEventListener('scroll', handleScroll);
        return ()=>window.removeEventListener('scroll', handleScroll);
    }, []);
    // 切换移动端菜单
    const toggleMenu = ()=>{
        setIsMenuOpen(!isMenuOpen);
    };
    // 关闭移动端菜单
    const closeMenu = ()=>{
        setIsMenuOpen(false);
        setExpandedSubmenus([]);
    };
    // 切换子菜单展开状态
    const toggleSubmenu = (itemName)=>{
        setExpandedSubmenus((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [
                ...prev,
                itemName
            ]);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('fixed top-0 left-0 right-0 z-50 transition-all duration-300', isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-mysql-border' : 'bg-transparent', className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between h-16 lg:h-20",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            href: "/",
                            className: "flex items-center space-x-3 group",
                            onClick: closeMenu,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-center w-10 h-10 bg-mysql-primary rounded-lg group-hover:bg-mysql-primary-dark transition-colors duration-200",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                                        className: "w-6 h-6 text-white"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                        lineNumber: 74,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                    lineNumber: 73,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-200",
                                            children: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                            lineNumber: 77,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs text-mysql-text-light hidden sm:block",
                                            children: "MySQL智能分析专家"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                            lineNumber: 80,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                            lineNumber: 68,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "hidden lg:flex items-center space-x-8",
                            children: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$navigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MAIN_NAVIGATION"].map((item)=>{
                                // 检查是否有子菜单
                                if (item.children && item.children.length > 0) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$DropdownMenu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        trigger: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: item.name
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                            lineNumber: 94,
                                            columnNumber: 30
                                        }, void 0),
                                        items: item.children,
                                        align: "left",
                                        className: "relative"
                                    }, item.name, false, {
                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                        lineNumber: 92,
                                        columnNumber: 19
                                    }, this);
                                }
                                // 普通导航项
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: item.href,
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('relative px-3 py-2 text-sm font-medium transition-all duration-200', 'text-mysql-text hover:text-mysql-primary', 'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5', 'before:bg-mysql-primary before:transition-all before:duration-300', 'hover:before:w-full'),
                                    children: item.name
                                }, item.name, false, {
                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                    lineNumber: 104,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: toggleMenu,
                                className: "lg:hidden p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200",
                                "aria-label": "切换菜单",
                                children: isMenuOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "w-6 h-6"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                    lineNumber: 131,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                                    className: "w-6 h-6"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                    lineNumber: 133,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                lineNumber: 124,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                    lineNumber: 66,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                lineNumber: 65,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: isMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                opacity: 0
                            },
                            animate: {
                                opacity: 1
                            },
                            exit: {
                                opacity: 0
                            },
                            transition: {
                                duration: 0.2
                            },
                            className: "fixed inset-0 bg-black/50 backdrop-blur-sm lg:hidden",
                            onClick: closeMenu
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                x: '100%'
                            },
                            animate: {
                                x: 0
                            },
                            exit: {
                                x: '100%'
                            },
                            transition: {
                                type: 'spring',
                                damping: 25,
                                stiffness: 200
                            },
                            className: "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl lg:hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col h-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between p-6 border-b border-mysql-border",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-center w-8 h-8 bg-mysql-primary rounded-lg",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                                                            className: "w-5 h-5 text-white"
                                                        }, void 0, false, {
                                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                            lineNumber: 167,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                        lineNumber: 166,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-lg font-bold text-mysql-text",
                                                        children: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SITE_CONFIG"].name
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                        lineNumber: 169,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                lineNumber: 165,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: closeMenu,
                                                className: "p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200",
                                                "aria-label": "关闭菜单",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                    className: "w-5 h-5"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                    lineNumber: 179,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                lineNumber: 173,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                        lineNumber: 164,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                        className: "flex-1 px-6 py-6",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$navigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MAIN_NAVIGATION"].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                                    initial: {
                                                        opacity: 0,
                                                        x: 20
                                                    },
                                                    animate: {
                                                        opacity: 1,
                                                        x: 0
                                                    },
                                                    transition: {
                                                        delay: index * 0.1
                                                    },
                                                    children: item.children && item.children.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                type: "button",
                                                                onClick: ()=>toggleSubmenu(item.name),
                                                                className: "flex items-center justify-between w-full px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: item.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                        lineNumber: 202,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('w-4 h-4 transition-transform duration-200', expandedSubmenus.includes(item.name) && 'rotate-90')
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                        lineNumber: 203,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                lineNumber: 197,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                                                                children: expandedSubmenus.includes(item.name) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                                                    initial: {
                                                                        opacity: 0,
                                                                        height: 0
                                                                    },
                                                                    animate: {
                                                                        opacity: 1,
                                                                        height: 'auto'
                                                                    },
                                                                    exit: {
                                                                        opacity: 0,
                                                                        height: 0
                                                                    },
                                                                    transition: {
                                                                        duration: 0.2
                                                                    },
                                                                    className: "ml-4 mt-2 space-y-1",
                                                                    children: item.children.map((subItem)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                            href: subItem.href,
                                                                            onClick: closeMenu,
                                                                            className: "flex items-center px-4 py-2 text-sm text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                children: subItem.name
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                                lineNumber: 228,
                                                                                columnNumber: 39
                                                                            }, this)
                                                                        }, subItem.name, false, {
                                                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                            lineNumber: 222,
                                                                            columnNumber: 37
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                    lineNumber: 214,
                                                                    columnNumber: 33
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                                lineNumber: 212,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                        lineNumber: 195,
                                                        columnNumber: 27
                                                    }, this) : /* 普通菜单项 */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        onClick: closeMenu,
                                                        className: "flex items-center px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-medium",
                                                            children: item.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                            lineNumber: 242,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                        lineNumber: 237,
                                                        columnNumber: 27
                                                    }, this)
                                                }, item.name, false, {
                                                    fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 23
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                            lineNumber: 185,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                        lineNumber: 184,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-6 border-t border-mysql-border",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/contact",
                                            onClick: closeMenu,
                                            className: "flex items-center justify-center w-full px-4 py-3 bg-mysql-primary text-white font-medium rounded-lg hover:bg-mysql-primary-dark transition-colors duration-200",
                                            children: "联系我们"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                            lineNumber: 252,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                        lineNumber: 251,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                                lineNumber: 162,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/Header.tsx",
                            lineNumber: 155,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Header.tsx",
                lineNumber: 141,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/layout/Header.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
}}),
"[project]/web-app/src/components/layout/Footer.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Footer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$github$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Github$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/github.js [app-ssr] (ecmascript) <export default as Github>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/twitter.js [app-ssr] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-ssr] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mail.js [app-ssr] (ecmascript) <export default as Mail>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone.js [app-ssr] (ecmascript) <export default as Phone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
// 页脚导航数据
const FOOTER_NAVIGATION = {
    products: {
        title: '产品服务',
        links: [
            {
                name: 'MySQL知识库',
                href: '/knowledge'
            },
            {
                name: '项目管理',
                href: '/projects'
            },
            {
                name: '报告展示',
                href: '/reports'
            },
            {
                name: 'AI智能分析',
                href: '/ai-analysis'
            }
        ]
    },
    company: {
        title: '关于我们',
        links: [
            {
                name: '公司介绍',
                href: '/about'
            },
            {
                name: '团队成员',
                href: '/team'
            },
            {
                name: '新闻动态',
                href: '/news'
            },
            {
                name: '招聘信息',
                href: '/careers'
            }
        ]
    },
    support: {
        title: '技术支持',
        links: [
            {
                name: '帮助中心',
                href: '/help'
            },
            {
                name: '技术文档',
                href: '/docs'
            },
            {
                name: '联系我们',
                href: '/contact'
            },
            {
                name: '在线客服',
                href: '/chat'
            }
        ]
    },
    legal: {
        title: '法律声明',
        links: [
            {
                name: '服务条款',
                href: '/terms'
            },
            {
                name: '隐私政策',
                href: '/privacy'
            },
            {
                name: '免责声明',
                href: '/disclaimer'
            },
            {
                name: 'Cookie政策',
                href: '/cookies'
            }
        ]
    }
};
// 社交媒体链接
const SOCIAL_LINKS = [
    {
        name: 'GitHub',
        href: 'https://github.com/mysqlai',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$github$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Github$3e$__["Github"],
        color: 'hover:text-gray-900'
    },
    {
        name: 'Twitter',
        href: 'https://twitter.com/mysqlai',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"],
        color: 'hover:text-blue-400'
    },
    {
        name: 'LinkedIn',
        href: 'https://linkedin.com/company/mysqlai',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"],
        color: 'hover:text-blue-600'
    },
    {
        name: 'Email',
        href: 'mailto:<EMAIL>',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"],
        color: 'hover:text-mysql-primary'
    }
];
// 联系信息
const CONTACT_INFO = [
    {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"],
        text: '+86 ************',
        href: 'tel:+8640088899999'
    },
    {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"],
        text: '<EMAIL>',
        href: 'mailto:<EMAIL>'
    },
    {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"],
        text: '北京市朝阳区科技园区',
        href: '#'
    }
];
function Footer({ className }) {
    const currentYear = new Date().getFullYear();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('bg-gradient-to-b from-mysql-primary-dark to-mysql-primary-dark/90 text-white', className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 py-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center lg:text-left",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-bold text-white mb-2",
                                    children: "MySQLAi.de"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                    lineNumber: 118,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-mysql-primary-light text-sm max-w-md",
                                    children: "专业的MySQL智能分析平台"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                    lineNumber: 119,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap justify-center lg:justify-end gap-8",
                            children: Object.entries(FOOTER_NAVIGATION).map(([key, section])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col items-center lg:items-start",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-white font-medium text-sm mb-3",
                                            children: section.title
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                            lineNumber: 128,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col space-y-2",
                                            children: section.links.slice(0, 3).map((link, linkIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                    href: link.href,
                                                    className: "text-mysql-primary-light hover:text-white transition-colors duration-200 text-xs",
                                                    children: link.name
                                                }, linkIndex, false, {
                                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                                    lineNumber: 131,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                            lineNumber: 129,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, key, true, {
                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                    lineNumber: 127,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                    lineNumber: 115,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                lineNumber: 113,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-mysql-primary-light/10"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 py-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col md:flex-row justify-between items-center gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-mysql-primary-light/70 text-xs",
                            children: [
                                "© ",
                                currentYear,
                                " MySQLAi.de"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-6 text-xs",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: "mailto:<EMAIL>",
                                    className: "text-mysql-primary-light/70 hover:text-white transition-colors duration-200",
                                    children: "<EMAIL>"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                    lineNumber: 159,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: "tel:+8640088899999",
                                    className: "text-mysql-primary-light/70 hover:text-white transition-colors duration-200",
                                    children: "+86 ************"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                                    lineNumber: 165,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                            lineNumber: 158,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                    lineNumber: 151,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                lineNumber: 150,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
                initial: {
                    opacity: 0,
                    scale: 0.8
                },
                whileInView: {
                    opacity: 1,
                    scale: 1
                },
                whileHover: {
                    scale: 1.1,
                    y: -2
                },
                whileTap: {
                    scale: 0.95
                },
                transition: {
                    duration: 0.3,
                    ease: "easeOut"
                },
                viewport: {
                    once: true
                },
                onClick: ()=>window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    }),
                className: "fixed bottom-8 right-8 p-3 bg-mysql-primary text-white rounded-full shadow-lg hover:bg-mysql-primary-dark transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-mysql-primary/30 z-50",
                "aria-label": "回到顶部",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M5 10l7-7m0 0l7 7m-7-7v18"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                    lineNumber: 187,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/layout/Footer.tsx",
                lineNumber: 176,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/layout/Footer.tsx",
        lineNumber: 106,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0d9ebfa9._.js.map