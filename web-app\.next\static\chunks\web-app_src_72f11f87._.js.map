{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/api/knowledge.ts"], "sourcesContent": ["// MySQLAi.de - 知识库 API 客户端\n// 提供类型安全的 API 调用方法\n\nimport type { Database } from '@/lib/database.types';\n\ntype KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];\ntype KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];\ntype CodeExample = Database['public']['Tables']['code_examples']['Row'];\n\n// API 响应类型\ninterface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n  details?: string;\n}\n\ninterface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\ninterface SearchResponse extends PaginatedResponse<KnowledgeArticle> {\n  query?: {\n    text: string;\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    sortBy?: string;\n    sortOrder?: string;\n  };\n}\n\n// API缓存管理\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiry: number;\n}\n\nclass APICache {\n  private cache = new Map<string, CacheItem<any>>();\n  private readonly defaultTTL = 5 * 60 * 1000; // 5分钟默认缓存时间\n\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    if (!item) return null;\n\n    if (Date.now() > item.expiry) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return item.data;\n  }\n\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    this.cache.set(key, {\n      data,\n      timestamp: now,\n      expiry: now + ttl\n    });\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n\n  generateKey(endpoint: string, options?: RequestInit): string {\n    const method = options?.method || 'GET';\n    const body = options?.body || '';\n    return `${method}:${endpoint}:${btoa(body).slice(0, 20)}`;\n  }\n}\n\n// 全局API缓存实例\nconst apiCache = new APICache();\n\n// 防抖管理\nconst debounceMap = new Map<string, NodeJS.Timeout>();\n\nfunction debounce<T extends (...args: any[]) => any>(\n  func: T,\n  delay: number,\n  key: string\n): (...args: Parameters<T>) => Promise<ReturnType<T>> {\n  return (...args: Parameters<T>): Promise<ReturnType<T>> => {\n    return new Promise((resolve, reject) => {\n      // 清除之前的定时器\n      if (debounceMap.has(key)) {\n        clearTimeout(debounceMap.get(key)!);\n      }\n\n      // 设置新的定时器\n      const timeoutId = setTimeout(async () => {\n        try {\n          const result = await func(...args);\n          resolve(result);\n        } catch (error) {\n          reject(error);\n        } finally {\n          debounceMap.delete(key);\n        }\n      }, delay);\n\n      debounceMap.set(key, timeoutId);\n    });\n  };\n}\n\n// 基础 API 调用函数\nasync function apiCall<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {\n  try {\n    // 构建正确的API URL\n    let apiUrl: string;\n\n    if (typeof window !== 'undefined') {\n      // 客户端：使用绝对URL\n      apiUrl = `${window.location.origin}/api/knowledge${endpoint}`;\n    } else {\n      // 服务端：构建完整的URL\n      const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';\n      apiUrl = `${baseUrl}/api/knowledge${endpoint}`;\n    }\n\n    // 检查缓存（仅对GET请求）\n    const method = options?.method || 'GET';\n    if (method === 'GET') {\n      const cacheKey = apiCache.generateKey(endpoint, options);\n      const cachedData = apiCache.get<ApiResponse<T>>(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    // 创建带超时的fetch请求\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时\n\n    try {\n      const response = await fetch(apiUrl, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        signal: controller.signal,\n        ...options,\n      });\n\n      clearTimeout(timeoutId);\n\n      // 检查响应状态\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      // 检查响应内容类型\n      const contentType = response.headers.get('content-type');\n      if (!contentType || !contentType.includes('application/json')) {\n        throw new Error('响应不是有效的JSON格式');\n      }\n\n      const data = await response.json();\n\n      // 缓存成功的GET请求结果\n      if (method === 'GET' && data.success) {\n        const cacheKey = apiCache.generateKey(endpoint, options);\n        // 搜索结果缓存时间较短，其他数据缓存时间较长\n        const ttl = endpoint.includes('/search') ? 2 * 60 * 1000 : 5 * 60 * 1000;\n        apiCache.set(cacheKey, data, ttl);\n      }\n\n      return data;\n    } catch (fetchError) {\n      clearTimeout(timeoutId);\n      throw fetchError;\n    }\n  } catch (error) {\n    console.error('API调用失败:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '网络错误'\n    };\n  }\n}\n\n// 知识库分类 API\nexport const categoriesApi = {\n  // 获取所有分类\n  getAll: async (includeStats = false): Promise<ApiResponse<KnowledgeCategory[]>> => {\n    const params = includeStats ? '?includeStats=true' : '';\n    return apiCall<KnowledgeCategory[]>(`/categories${params}`);\n  },\n\n  // 获取单个分类\n  getById: async (id: string, includeArticles = false): Promise<ApiResponse<KnowledgeCategory>> => {\n    const params = includeArticles ? '?includeArticles=true' : '';\n    return apiCall<KnowledgeCategory>(`/categories/${id}${params}`);\n  },\n\n  // 创建分类\n  create: async (category: Omit<KnowledgeCategory, 'created_at'>): Promise<ApiResponse<KnowledgeCategory>> => {\n    return apiCall<KnowledgeCategory>('/categories', {\n      method: 'POST',\n      body: JSON.stringify(category),\n    });\n  },\n\n  // 更新分类\n  update: async (id: string, updates: Partial<KnowledgeCategory>): Promise<ApiResponse<KnowledgeCategory>> => {\n    return apiCall<KnowledgeCategory>(`/categories/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除分类\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/categories/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 批量更新排序\n  updateOrder: async (categories: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {\n    return apiCall<void>('/categories', {\n      method: 'PUT',\n      body: JSON.stringify({ categories }),\n    });\n  },\n};\n\n// 知识库文章 API\nexport const articlesApi = {\n  // 获取文章列表\n  getAll: async (params?: {\n    category?: string;\n    search?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n    includeCodeExamples?: boolean;\n    includeRelated?: boolean;\n  }): Promise<PaginatedResponse<KnowledgeArticle>> => {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.category) searchParams.set('category', params.category);\n    if (params?.search) searchParams.set('search', params.search);\n    if (params?.tags) searchParams.set('tags', params.tags);\n    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.includeCodeExamples) searchParams.set('includeCodeExamples', 'true');\n    if (params?.includeRelated) searchParams.set('includeRelated', 'true');\n\n    const query = searchParams.toString();\n    return apiCall<KnowledgeArticle[]>(`/articles${query ? `?${query}` : ''}`);\n  },\n\n  // 获取单个文章\n  getById: async (id: string, options?: {\n    includeCodeExamples?: boolean;\n    includeRelated?: boolean;\n  }): Promise<ApiResponse<KnowledgeArticle>> => {\n    const params = new URLSearchParams();\n    if (options?.includeCodeExamples === false) params.set('includeCodeExamples', 'false');\n    if (options?.includeRelated === false) params.set('includeRelated', 'false');\n    \n    const query = params.toString();\n    return apiCall<KnowledgeArticle>(`/articles/${id}${query ? `?${query}` : ''}`);\n  },\n\n  // 创建文章\n  create: async (article: Omit<KnowledgeArticle, 'created_at' | 'updated_at' | 'last_updated'>): Promise<ApiResponse<KnowledgeArticle>> => {\n    return apiCall<KnowledgeArticle>('/articles', {\n      method: 'POST',\n      body: JSON.stringify(article),\n    });\n  },\n\n  // 更新文章\n  update: async (id: string, updates: Partial<KnowledgeArticle>): Promise<ApiResponse<KnowledgeArticle>> => {\n    return apiCall<KnowledgeArticle>(`/articles/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除文章\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/articles/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 搜索文章（带防抖）\n  search: debounce(async (query: string, params?: {\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n  }): Promise<SearchResponse> => {\n    const searchParams = new URLSearchParams();\n    searchParams.set('search', query);\n\n    if (params?.category) searchParams.set('category', params.category);\n    if (params?.tags) searchParams.set('tags', params.tags);\n    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n\n    return apiCall<KnowledgeArticle[]>(`/articles?${searchParams.toString()}`);\n  }, 300, 'articles-search'),\n};\n\n// 代码示例 API\nexport const codeExamplesApi = {\n  // 获取代码示例列表\n  getAll: async (params?: {\n    articleId?: string;\n    language?: string;\n    search?: string;\n    page?: number;\n    limit?: number;\n  }): Promise<PaginatedResponse<CodeExample>> => {\n    const searchParams = new URLSearchParams();\n\n    if (params?.articleId) searchParams.set('articleId', params.articleId);\n    if (params?.language) searchParams.set('language', params.language);\n    if (params?.search) searchParams.set('search', params.search);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n\n    const query = searchParams.toString();\n    return apiCall<CodeExample[]>(`/code-examples${query ? `?${query}` : ''}`);\n  },\n\n  // 创建代码示例\n  create: async (example: Omit<CodeExample, 'created_at'>): Promise<ApiResponse<CodeExample>> => {\n    return apiCall<CodeExample>('/code-examples', {\n      method: 'POST',\n      body: JSON.stringify(example),\n    });\n  },\n\n  // 更新代码示例\n  update: async (id: string, updates: Partial<CodeExample>): Promise<ApiResponse<CodeExample>> => {\n    return apiCall<CodeExample>(`/code-examples/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除代码示例\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/code-examples/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 批量更新排序\n  updateOrder: async (examples: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {\n    return apiCall<void>('/code-examples', {\n      method: 'PUT',\n      body: JSON.stringify({ examples }),\n    });\n  },\n};\n\n// 搜索 API\nexport const searchApi = {\n  // 搜索文章（带防抖和缓存）\n  search: debounce(async (params: {\n    query: string;\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n    sortBy?: string;\n    sortOrder?: string;\n  }): Promise<SearchResponse> => {\n    const searchParams = new URLSearchParams();\n\n    searchParams.set('q', params.query);\n    if (params.category) searchParams.set('category', params.category);\n    if (params.tags) searchParams.set('tags', params.tags);\n    if (params.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params.page) searchParams.set('page', params.page.toString());\n    if (params.limit) searchParams.set('limit', params.limit.toString());\n    if (params.sortBy) searchParams.set('sortBy', params.sortBy);\n    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);\n\n    return apiCall<KnowledgeArticle[]>(`/search?${searchParams.toString()}`);\n  }, 300, 'search-main'),\n\n  // 获取搜索建议（带防抖）\n  getSuggestions: debounce(async (query: string, limit = 5): Promise<ApiResponse<Array<{\n    type: 'article' | 'query';\n    text: string;\n    id?: string;\n    category?: string;\n  }>>> => {\n    // 查询长度小于2时直接返回空结果，避免无意义的API调用\n    if (!query || query.trim().length < 2) {\n      return {\n        success: true,\n        data: []\n      };\n    }\n\n    return apiCall('/search', {\n      method: 'POST',\n      body: JSON.stringify({ query: query.trim(), limit }),\n    });\n  }, 200, 'search-suggestions'),\n\n  // 清除搜索缓存\n  clearCache: (): void => {\n    apiCache.clear();\n  },\n\n  // 取消所有防抖请求\n  cancelPendingRequests: (): void => {\n    debounceMap.forEach((timeoutId) => {\n      clearTimeout(timeoutId);\n    });\n    debounceMap.clear();\n  }\n};\n\n// 统计 API\nexport const statsApi = {\n  // 获取统计数据\n  get: async (params?: {\n    period?: string;\n    includeSearchStats?: boolean;\n  }): Promise<ApiResponse<{\n    overview: {\n      totalCategories: number;\n      totalArticles: number;\n      totalCodeExamples: number;\n      totalRelations: number;\n    };\n    categoryStats: Array<{\n      id: string;\n      name: string;\n      articleCount: number;\n    }>;\n    difficultyStats: Record<string, number>;\n    languageStats: Record<string, number>;\n    recentArticles: Array<{\n      id: string;\n      title: string;\n      last_updated: string;\n      knowledge_categories: { name: string };\n    }>;\n    searchStats?: {\n      totalSearches: number;\n      popularQueries: Array<{ query: string; count: number }>;\n      searchTrends: Record<string, number>;\n    };\n  }>> => {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.period) searchParams.set('period', params.period);\n    if (params?.includeSearchStats) searchParams.set('includeSearchStats', 'true');\n\n    const query = searchParams.toString();\n    return apiCall(`/stats${query ? `?${query}` : ''}`);\n  },\n\n  // 导出统计数据\n  export: async (format = 'json', includeDetails = false): Promise<ApiResponse<any>> => {\n    return apiCall('/stats/export', {\n      method: 'POST',\n      body: JSON.stringify({ format, includeDetails }),\n    });\n  },\n};\n"], "names": [], "mappings": "AAAA,2BAA2B;AAC3B,mBAAmB;;;;;;;;AA4CnB,MAAM;IACI,QAAQ,IAAI,MAA8B;IACjC,aAAa,IAAI,KAAK,KAAK;IAE5C,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW;YACX,QAAQ,MAAM;QAChB;IACF;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,YAAY,QAAgB,EAAE,OAAqB,EAAU;QAC3D,MAAM,SAAS,SAAS,UAAU;QAClC,MAAM,OAAO,SAAS,QAAQ;QAC9B,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK;IAC3D;AACF;AAEA,YAAY;AACZ,MAAM,WAAW,IAAI;AAErB,OAAO;AACP,MAAM,cAAc,IAAI;AAExB,SAAS,SACP,IAAO,EACP,KAAa,EACb,GAAW;IAEX,OAAO,CAAC,GAAG;QACT,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,WAAW;YACX,IAAI,YAAY,GAAG,CAAC,MAAM;gBACxB,aAAa,YAAY,GAAG,CAAC;YAC/B;YAEA,UAAU;YACV,MAAM,YAAY,WAAW;gBAC3B,IAAI;oBACF,MAAM,SAAS,MAAM,QAAQ;oBAC7B,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT,SAAU;oBACR,YAAY,MAAM,CAAC;gBACrB;YACF,GAAG;YAEH,YAAY,GAAG,CAAC,KAAK;QACvB;IACF;AACF;AAEA,cAAc;AACd,eAAe,QAAW,QAAgB,EAAE,OAAqB;IAC/D,IAAI;QACF,eAAe;QACf,IAAI;QAEJ,wCAAmC;YACjC,cAAc;YACd,SAAS,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU;QAC/D,OAAO;;QAIP;QAEA,gBAAgB;QAChB,MAAM,SAAS,SAAS,UAAU;QAClC,IAAI,WAAW,OAAO;YACpB,MAAM,WAAW,SAAS,WAAW,CAAC,UAAU;YAChD,MAAM,aAAa,SAAS,GAAG,CAAiB;YAChD,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,QAAQ;QAEvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,QAAQ,WAAW,MAAM;gBACzB,GAAG,OAAO;YACZ;YAEA,aAAa;YAEb,SAAS;YACT,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,WAAW;YACX,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,qBAAqB;gBAC7D,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,eAAe;YACf,IAAI,WAAW,SAAS,KAAK,OAAO,EAAE;gBACpC,MAAM,WAAW,SAAS,WAAW,CAAC,UAAU;gBAChD,wBAAwB;gBACxB,MAAM,MAAM,SAAS,QAAQ,CAAC,aAAa,IAAI,KAAK,OAAO,IAAI,KAAK;gBACpE,SAAS,GAAG,CAAC,UAAU,MAAM;YAC/B;YAEA,OAAO;QACT,EAAE,OAAO,YAAY;YACnB,aAAa;YACb,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,QAAQ,OAAO,eAAe,KAAK;QACjC,MAAM,SAAS,eAAe,uBAAuB;QACrD,OAAO,QAA6B,CAAC,WAAW,EAAE,QAAQ;IAC5D;IAEA,SAAS;IACT,SAAS,OAAO,IAAY,kBAAkB,KAAK;QACjD,MAAM,SAAS,kBAAkB,0BAA0B;QAC3D,OAAO,QAA2B,CAAC,YAAY,EAAE,KAAK,QAAQ;IAChE;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAA2B,eAAe;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,OAAO,QAA2B,CAAC,YAAY,EAAE,IAAI,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,YAAY,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,OAAO,QAAc,eAAe;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;IACF;AACF;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,QAAQ,OAAO;QAUb,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACtD,IAAI,QAAQ,YAAY,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACxE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,qBAAqB,aAAa,GAAG,CAAC,uBAAuB;QACzE,IAAI,QAAQ,gBAAgB,aAAa,GAAG,CAAC,kBAAkB;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAA4B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC3E;IAEA,SAAS;IACT,SAAS,OAAO,IAAY;QAI1B,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,wBAAwB,OAAO,OAAO,GAAG,CAAC,uBAAuB;QAC9E,IAAI,SAAS,mBAAmB,OAAO,OAAO,GAAG,CAAC,kBAAkB;QAEpE,MAAM,QAAQ,OAAO,QAAQ;QAC7B,OAAO,QAA0B,CAAC,UAAU,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC/E;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAA0B,aAAa;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,OAAO,QAA0B,CAAC,UAAU,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,UAAU,EAAE,IAAI,EAAE;YACtC,QAAQ;QACV;IACF;IAEA,YAAY;IACZ,QAAQ,SAAS,OAAO,OAAe;QAOrC,MAAM,eAAe,IAAI;QACzB,aAAa,GAAG,CAAC,UAAU;QAE3B,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACtD,IAAI,QAAQ,YAAY,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACxE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAElE,OAAO,QAA4B,CAAC,UAAU,EAAE,aAAa,QAAQ,IAAI;IAC3E,GAAG,KAAK;AACV;AAGO,MAAM,kBAAkB;IAC7B,WAAW;IACX,QAAQ,OAAO;QAOb,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAAuB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC3E;IAEA,SAAS;IACT,QAAQ,OAAO;QACb,OAAO,QAAqB,kBAAkB;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,SAAS;IACT,QAAQ,OAAO,IAAY;QACzB,OAAO,QAAqB,CAAC,eAAe,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,SAAS;IACT,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,eAAe,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,OAAO,QAAc,kBAAkB;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;IACF;AACF;AAGO,MAAM,YAAY;IACvB,eAAe;IACf,QAAQ,SAAS,OAAO;QAUtB,MAAM,eAAe,IAAI;QAEzB,aAAa,GAAG,CAAC,KAAK,OAAO,KAAK;QAClC,IAAI,OAAO,QAAQ,EAAE,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QACjE,IAAI,OAAO,IAAI,EAAE,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACrD,IAAI,OAAO,UAAU,EAAE,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACvE,IAAI,OAAO,IAAI,EAAE,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC9D,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC3D,IAAI,OAAO,SAAS,EAAE,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QAEpE,OAAO,QAA4B,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI;IACzE,GAAG,KAAK;IAER,cAAc;IACd,gBAAgB,SAAS,OAAO,OAAe,QAAQ,CAAC;QAMtD,8BAA8B;QAC9B,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;YACV;QACF;QAEA,OAAO,QAAQ,WAAW;YACxB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,OAAO,MAAM,IAAI;gBAAI;YAAM;QACpD;IACF,GAAG,KAAK;IAER,SAAS;IACT,YAAY;QACV,SAAS,KAAK;IAChB;IAEA,WAAW;IACX,uBAAuB;QACrB,YAAY,OAAO,CAAC,CAAC;YACnB,aAAa;QACf;QACA,YAAY,KAAK;IACnB;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;IACT,KAAK,OAAO;QA6BV,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,oBAAoB,aAAa,GAAG,CAAC,sBAAsB;QAEvE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACpD;IAEA,SAAS;IACT,QAAQ,OAAO,SAAS,MAAM,EAAE,iBAAiB,KAAK;QACpD,OAAO,QAAQ,iBAAiB;YAC9B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ;YAAe;QAChD;IACF;AACF", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 智能搜索建议组件\n// 提供实时搜索建议、键盘导航、搜索历史显示功能\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { searchApi } from '@/lib/api/knowledge';\nimport { ChevronRight, Clock, TrendingUp, FileText } from 'lucide-react';\n\n// 建议项类型\ninterface SuggestionItem {\n  type: 'article' | 'query' | 'history';\n  text: string;\n  id?: string;\n  category?: string;\n  icon?: React.ReactNode;\n}\n\n// 组件属性\ninterface SearchSuggestionsProps {\n  query: string;\n  isVisible: boolean;\n  onSelect: (suggestion: SuggestionItem) => void;\n  onClose: () => void;\n  searchHistory?: string[];\n  className?: string;\n  maxSuggestions?: number;\n  showHistory?: boolean;\n  showCategories?: boolean;\n}\n\nexport default function SearchSuggestions({\n  query,\n  isVisible,\n  onSelect,\n  onClose,\n  searchHistory = [],\n  className = '',\n  maxSuggestions = 8,\n  showHistory = true,\n  showCategories = true\n}: SearchSuggestionsProps) {\n  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const [error, setError] = useState<string | null>(null);\n  \n  const containerRef = useRef<HTMLDivElement>(null);\n  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);\n\n  // 使用ref存储最新值，避免useEffect依赖问题\n  const suggestionsRef = useRef(suggestions);\n  const selectedIndexRef = useRef(selectedIndex);\n\n  // 更新ref值\n  suggestionsRef.current = suggestions;\n  selectedIndexRef.current = selectedIndex;\n\n  // 获取搜索建议\n  const fetchSuggestions = useCallback(async (searchQuery: string) => {\n    if (!searchQuery || searchQuery.length < 2) {\n      setSuggestions([]);\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await searchApi.getSuggestions(searchQuery, maxSuggestions - 2);\n\n      if (response.success && response.data) {\n        const apiSuggestions: SuggestionItem[] = response.data.map(item => ({\n          ...item,\n          icon: item.type === 'article' ? <FileText className=\"w-4 h-4\" /> : <TrendingUp className=\"w-4 h-4\" />\n        }));\n\n        // 添加搜索历史建议 - 使用当前的props值而不是依赖\n        const historySuggestions: SuggestionItem[] = showHistory\n          ? searchHistory\n              .filter(historyItem =>\n                historyItem.toLowerCase().includes(searchQuery.toLowerCase()) &&\n                historyItem !== searchQuery\n              )\n              .slice(0, 2)\n              .map(historyItem => ({\n                type: 'history' as const,\n                text: historyItem,\n                icon: <Clock className=\"w-4 h-4\" />\n              }))\n          : [];\n\n        // 合并建议并去重\n        const allSuggestions = [...historySuggestions, ...apiSuggestions];\n        const uniqueSuggestions = allSuggestions.filter((suggestion, index, self) =>\n          index === self.findIndex(s => s.text === suggestion.text)\n        );\n\n        setSuggestions(uniqueSuggestions.slice(0, maxSuggestions));\n      } else {\n        setError(response.error || '获取建议失败');\n        setSuggestions([]);\n      }\n    } catch (err) {\n      console.error('获取搜索建议失败:', err);\n      setError('网络错误，请稍后重试');\n      setSuggestions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [maxSuggestions]); // 只保留稳定的依赖\n\n  // 监听查询变化\n  useEffect(() => {\n    if (isVisible && query) {\n      fetchSuggestions(query);\n    } else {\n      setSuggestions([]);\n      setSelectedIndex(-1);\n    }\n  }, [query, isVisible]); // 移除fetchSuggestions依赖，避免循环\n\n  // 键盘导航处理\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (!isVisible || suggestionsRef.current.length === 0) return;\n\n      switch (event.key) {\n        case 'ArrowDown':\n          event.preventDefault();\n          setSelectedIndex(prev =>\n            prev < suggestionsRef.current.length - 1 ? prev + 1 : 0\n          );\n          break;\n        case 'ArrowUp':\n          event.preventDefault();\n          setSelectedIndex(prev =>\n            prev > 0 ? prev - 1 : suggestionsRef.current.length - 1\n          );\n          break;\n        case 'Enter':\n          event.preventDefault();\n          if (selectedIndexRef.current >= 0 && selectedIndexRef.current < suggestionsRef.current.length) {\n            onSelect(suggestionsRef.current[selectedIndexRef.current]);\n          }\n          break;\n        case 'Escape':\n          event.preventDefault();\n          onClose();\n          break;\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }\n  }, [isVisible, onSelect, onClose]); // 只依赖isVisible和稳定的回调函数\n\n  // 滚动到选中项\n  useEffect(() => {\n    if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {\n      itemRefs.current[selectedIndex]?.scrollIntoView({\n        behavior: 'smooth',\n        block: 'nearest'\n      });\n    }\n  }, [selectedIndex]);\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isVisible, onClose]);\n\n  // 处理建议项点击\n  const handleSuggestionClick = (suggestion: SuggestionItem) => {\n    onSelect(suggestion);\n  };\n\n  // 获取建议项样式\n  const getSuggestionItemClass = (index: number) => {\n    const baseClass = \"flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150\";\n    const selectedClass = index === selectedIndex \n      ? \"bg-blue-50 text-blue-700 border-l-2 border-blue-500\" \n      : \"hover:bg-gray-50 text-gray-700\";\n    \n    return `${baseClass} ${selectedClass}`;\n  };\n\n  // 获取建议类型标签\n  const getTypeLabel = (type: string) => {\n    switch (type) {\n      case 'article': return '文章';\n      case 'query': return '热门';\n      case 'history': return '历史';\n      default: return '';\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div \n      ref={containerRef}\n      className={`absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto ${className}`}\n      role=\"listbox\"\n      aria-label=\"搜索建议\"\n    >\n      {loading && (\n        <div className=\"flex items-center justify-center py-4\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>\n          <span className=\"ml-2 text-sm text-gray-500\">获取建议中...</span>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"px-4 py-3 text-sm text-red-600 bg-red-50\">\n          {error}\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length === 0 && query.length >= 2 && (\n        <div className=\"px-4 py-3 text-sm text-gray-500 text-center\">\n          暂无相关建议\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length > 0 && (\n        <div className=\"py-1\">\n          {suggestions.map((suggestion, index) => (\n            <div\n              key={`${suggestion.type}-${suggestion.text}-${index}`}\n              ref={el => itemRefs.current[index] = el}\n              className={getSuggestionItemClass(index)}\n              onClick={() => handleSuggestionClick(suggestion)}\n              role=\"option\"\n              aria-selected={index === selectedIndex}\n            >\n              <div className=\"flex-shrink-0 text-gray-400\">\n                {suggestion.icon}\n              </div>\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"font-medium truncate\">\n                    {suggestion.text}\n                  </span>\n                  {showCategories && suggestion.category && (\n                    <span className=\"text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full\">\n                      {suggestion.category}\n                    </span>\n                  )}\n                </div>\n                <div className=\"text-xs text-gray-500 mt-1\">\n                  {getTypeLabel(suggestion.type)}\n                </div>\n              </div>\n\n              <ChevronRight className=\"w-4 h-4 text-gray-400 flex-shrink-0\" />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length > 0 && (\n        <div className=\"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\">\n          使用 ↑↓ 键导航，回车选择，ESC 关闭\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,yBAAyB;AAEzB;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;AA+Be,SAAS,kBAAkB,EACxC,KAAK,EACL,SAAS,EACT,QAAQ,EACR,OAAO,EACP,gBAAgB,EAAE,EAClB,YAAY,EAAE,EACd,iBAAiB,CAAC,EAClB,cAAc,IAAI,EAClB,iBAAiB,IAAI,EACE;;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA6B,EAAE;IAErD,6BAA6B;IAC7B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,SAAS;IACT,eAAe,OAAO,GAAG;IACzB,iBAAiB,OAAO,GAAG;IAE3B,SAAS;IACT,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC1C,IAAI,CAAC,eAAe,YAAY,MAAM,GAAG,GAAG;gBAC1C,eAAe,EAAE;gBACjB;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,+IAAA,CAAA,YAAS,CAAC,cAAc,CAAC,aAAa,iBAAiB;gBAE9E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,iBAAmC,SAAS,IAAI,CAAC,GAAG;0FAAC,CAAA,OAAQ,CAAC;gCAClE,GAAG,IAAI;gCACP,MAAM,KAAK,IAAI,KAAK,0BAAY,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAAe,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;4BAC3F,CAAC;;oBAED,8BAA8B;oBAC9B,MAAM,qBAAuC,cACzC,cACG,MAAM;2EAAC,CAAA,cACN,YAAY,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,gBAAgB;0EAEjB,KAAK,CAAC,GAAG,GACT,GAAG;2EAAC,CAAA,cAAe,CAAC;gCACnB,MAAM;gCACN,MAAM;gCACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;4BACzB,CAAC;4EACH,EAAE;oBAEN,UAAU;oBACV,MAAM,iBAAiB;2BAAI;2BAAuB;qBAAe;oBACjE,MAAM,oBAAoB,eAAe,MAAM;6FAAC,CAAC,YAAY,OAAO,OAClE,UAAU,KAAK,SAAS;qGAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,IAAI;;;oBAG1D,eAAe,kBAAkB,KAAK,CAAC,GAAG;gBAC5C,OAAO;oBACL,SAAS,SAAS,KAAK,IAAI;oBAC3B,eAAe,EAAE;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,eAAe,EAAE;YACnB,SAAU;gBACR,WAAW;YACb;QACF;0DAAG;QAAC;KAAe,GAAG,WAAW;IAEjC,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa,OAAO;gBACtB,iBAAiB;YACnB,OAAO;gBACL,eAAe,EAAE;gBACjB,iBAAiB,CAAC;YACpB;QACF;sCAAG;QAAC;QAAO;KAAU,GAAG,4BAA4B;IAEpD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB,CAAC;oBACrB,IAAI,CAAC,aAAa,eAAe,OAAO,CAAC,MAAM,KAAK,GAAG;oBAEvD,OAAQ,MAAM,GAAG;wBACf,KAAK;4BACH,MAAM,cAAc;4BACpB;6EAAiB,CAAA,OACf,OAAO,eAAe,OAAO,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAExD;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;6EAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,eAAe,OAAO,CAAC,MAAM,GAAG;;4BAExD;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB,IAAI,iBAAiB,OAAO,IAAI,KAAK,iBAAiB,OAAO,GAAG,eAAe,OAAO,CAAC,MAAM,EAAE;gCAC7F,SAAS,eAAe,OAAO,CAAC,iBAAiB,OAAO,CAAC;4BAC3D;4BACA;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;4BACA;oBACJ;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,WAAW;gBACrC;mDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;YACvD;QACF;sCAAG;QAAC;QAAW;QAAU;KAAQ,GAAG,uBAAuB;IAE3D,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,iBAAiB,KAAK,SAAS,OAAO,CAAC,cAAc,EAAE;gBACzD,SAAS,OAAO,CAAC,cAAc,EAAE,eAAe;oBAC9C,UAAU;oBACV,OAAO;gBACT;YACF;QACF;sCAAG;QAAC;KAAc;IAElB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAChF;oBACF;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;gBACvC;mDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;YACzD;QACF;sCAAG;QAAC;QAAW;KAAQ;IAEvB,UAAU;IACV,MAAM,wBAAwB,CAAC;QAC7B,SAAS;IACX;IAEA,UAAU;IACV,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAY;QAClB,MAAM,gBAAgB,UAAU,gBAC5B,wDACA;QAEJ,OAAO,GAAG,UAAU,CAAC,EAAE,eAAe;IACxC;IAEA,WAAW;IACX,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,oHAAoH,EAAE,WAAW;QAC7I,MAAK;QACL,cAAW;;YAEV,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;YAIhD,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,KAAK,KAAK,MAAM,MAAM,IAAI,mBACjE,6LAAC;gBAAI,WAAU;0BAA8C;;;;;;YAK9D,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,GAAG,mBAC1C,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;wBACrC,WAAW,uBAAuB;wBAClC,SAAS,IAAM,sBAAsB;wBACrC,MAAK;wBACL,iBAAe,UAAU;;0CAEzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,IAAI;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,WAAW,IAAI;;;;;;4CAEjB,kBAAkB,WAAW,QAAQ,kBACpC,6LAAC;gDAAK,WAAU;0DACb,WAAW,QAAQ;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;kDACZ,aAAa,WAAW,IAAI;;;;;;;;;;;;0CAIjC,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;uBA3BnB,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;YAiC5D,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,GAAG,mBAC1C,6LAAC;gBAAI,WAAU;0BAAsD;;;;;;;;;;;;AAM7E;GAzPwB;KAAA", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/AdvancedSearchFilters.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 高级搜索筛选组件\n// 提供分类、难度、标签、排序等多维度搜索筛选功能\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Filter,\n  X,\n  ChevronDown,\n  Tag,\n  Settings,\n  Calendar,\n  Star\n} from 'lucide-react';\n\n// 筛选选项类型\ninterface FilterOption {\n  value: string;\n  label: string;\n  count?: number;\n  color?: string;\n  icon?: React.ReactNode;\n}\n\n// 筛选状态类型\ninterface FilterState {\n  category?: string;\n  difficulty?: string;\n  tags: string[];\n  sortBy: 'relevance' | 'date' | 'title';\n  sortOrder: 'asc' | 'desc';\n  dateRange?: {\n    start?: string;\n    end?: string;\n  };\n}\n\n// 组件属性\ninterface AdvancedSearchFiltersProps {\n  filters: FilterState;\n  onFiltersChange: (filters: FilterState) => void;\n  categories?: FilterOption[];\n  availableTags?: FilterOption[];\n  className?: string;\n  isCollapsed?: boolean;\n  onToggleCollapse?: () => void;\n  showResultCount?: boolean;\n  resultCount?: number;\n}\n\n// 难度选项\nconst DIFFICULTY_OPTIONS: FilterOption[] = [\n  { value: 'beginner', label: '初级', color: 'bg-green-100 text-green-800' },\n  { value: 'intermediate', label: '中级', color: 'bg-yellow-100 text-yellow-800' },\n  { value: 'advanced', label: '高级', color: 'bg-red-100 text-red-800' },\n  { value: 'expert', label: '专家', color: 'bg-purple-100 text-purple-800' }\n];\n\n// 排序选项\nconst SORT_OPTIONS: FilterOption[] = [\n  { value: 'relevance', label: '相关性', icon: <Star className=\"w-4 h-4\" /> },\n  { value: 'date', label: '更新时间', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'title', label: '标题', icon: <Tag className=\"w-4 h-4\" /> }\n];\n\nexport default function AdvancedSearchFilters({\n  filters,\n  onFiltersChange,\n  categories = [],\n  availableTags = [],\n  className = '',\n  isCollapsed = false,\n  onToggleCollapse,\n  showResultCount = true,\n  resultCount = 0\n}: AdvancedSearchFiltersProps) {\n  const [isExpanded, setIsExpanded] = useState(!isCollapsed);\n  const [tagSearchQuery, setTagSearchQuery] = useState('');\n  const [showTagDropdown, setShowTagDropdown] = useState(false);\n\n  // 处理筛选器变化\n  const handleFilterChange = useCallback((key: keyof FilterState, value: any) => {\n    const newFilters = { ...filters, [key]: value };\n    onFiltersChange(newFilters);\n  }, [filters, onFiltersChange]);\n\n  // 处理标签添加\n  const handleTagAdd = useCallback((tagValue: string) => {\n    if (!filters.tags.includes(tagValue)) {\n      const newTags = [...filters.tags, tagValue];\n      handleFilterChange('tags', newTags);\n    }\n    setTagSearchQuery('');\n    setShowTagDropdown(false);\n  }, [filters.tags, handleFilterChange]);\n\n  // 处理标签移除\n  const handleTagRemove = useCallback((tagValue: string) => {\n    const newTags = filters.tags.filter(tag => tag !== tagValue);\n    handleFilterChange('tags', newTags);\n  }, [filters.tags, handleFilterChange]);\n\n  // 清空所有筛选器\n  const handleClearAll = useCallback(() => {\n    onFiltersChange({\n      tags: [],\n      sortBy: 'relevance',\n      sortOrder: 'desc'\n    });\n  }, [onFiltersChange]);\n\n  // 切换展开/收起\n  const handleToggleExpanded = () => {\n    const newExpanded = !isExpanded;\n    setIsExpanded(newExpanded);\n    onToggleCollapse?.();\n  };\n\n  // 过滤可用标签\n  const filteredTags = availableTags.filter(tag => \n    tag.label.toLowerCase().includes(tagSearchQuery.toLowerCase()) &&\n    !filters.tags.includes(tag.value)\n  );\n\n  // 检查是否有活动筛选器\n  const hasActiveFilters = filters.category || \n                          filters.difficulty || \n                          filters.tags.length > 0 ||\n                          filters.sortBy !== 'relevance' ||\n                          filters.sortOrder !== 'desc';\n\n  return (\n    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <div className=\"flex items-center gap-3\">\n          <button\n            type=\"button\"\n            onClick={handleToggleExpanded}\n            className=\"flex items-center gap-2 text-gray-700 hover:text-gray-900 transition-colors\"\n          >\n            <Filter className=\"w-5 h-5\" />\n            <span className=\"font-medium\">高级筛选</span>\n            <ChevronDown\n              className={`w-4 h-4 transition-transform duration-200 ${\n                isExpanded ? 'rotate-180' : ''\n              }`}\n            />\n          </button>\n          \n          {hasActiveFilters && (\n            <span className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\">\n              {[\n                filters.category ? 1 : 0,\n                filters.difficulty ? 1 : 0,\n                filters.tags.length,\n                (filters.sortBy !== 'relevance' || filters.sortOrder !== 'desc') ? 1 : 0\n              ].reduce((a, b) => a + b, 0)} 个筛选器\n            </span>\n          )}\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          {showResultCount && (\n            <span className=\"text-sm text-gray-500\">\n              {resultCount} 个结果\n            </span>\n          )}\n          \n          {hasActiveFilters && (\n            <button\n              type=\"button\"\n              onClick={handleClearAll}\n              className=\"text-sm text-gray-500 hover:text-red-600 transition-colors\"\n            >\n              清空筛选\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 筛选器内容 */}\n      {isExpanded && (\n        <div className=\"p-4 space-y-4\">\n          {/* 分类筛选 */}\n          {categories.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                分类\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n                <button\n                  type=\"button\"\n                  onClick={() => handleFilterChange('category', undefined)}\n                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                    !filters.category\n                      ? 'bg-blue-50 border-blue-200 text-blue-700'\n                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  全部分类\n                </button>\n                {categories.map((category) => (\n                  <button\n                    key={category.value}\n                    type=\"button\"\n                    onClick={() => handleFilterChange('category', category.value)}\n                    className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                      filters.category === category.value\n                        ? 'bg-blue-50 border-blue-200 text-blue-700'\n                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                    }`}\n                  >\n                    {category.label}\n                    {category.count && (\n                      <span className=\"ml-1 text-xs text-gray-500\">\n                        ({category.count})\n                      </span>\n                    )}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 难度筛选 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              难度等级\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                type=\"button\"\n                onClick={() => handleFilterChange('difficulty', undefined)}\n                className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                  !filters.difficulty\n                    ? 'bg-blue-50 border-blue-200 text-blue-700'\n                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                全部难度\n              </button>\n              {DIFFICULTY_OPTIONS.map((difficulty) => (\n                <button\n                  key={difficulty.value}\n                  type=\"button\"\n                  onClick={() => handleFilterChange('difficulty', difficulty.value)}\n                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                    filters.difficulty === difficulty.value\n                      ? 'bg-blue-50 border-blue-200 text-blue-700'\n                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${difficulty.color?.split(' ')[0] || 'bg-gray-300'}`}></span>\n                  {difficulty.label}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* 标签筛选 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              标签\n            </label>\n            \n            {/* 已选标签 */}\n            {filters.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {filters.tags.map((tag) => {\n                  const tagOption = availableTags.find(t => t.value === tag);\n                  return (\n                    <span\n                      key={tag}\n                      className=\"inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\"\n                    >\n                      <Tag className=\"w-3 h-3\" />\n                      {tagOption?.label || tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => handleTagRemove(tag)}\n                        className=\"ml-1 hover:text-blue-600\"\n                      >\n                        <X className=\"w-3 h-3\" />\n                      </button>\n                    </span>\n                  );\n                })}\n              </div>\n            )}\n\n            {/* 标签搜索 */}\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={tagSearchQuery}\n                onChange={(e) => setTagSearchQuery(e.target.value)}\n                onFocus={() => setShowTagDropdown(true)}\n                onBlur={() => setTimeout(() => setShowTagDropdown(false), 200)}\n                placeholder=\"搜索标签...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n              \n              {/* 标签下拉列表 */}\n              {showTagDropdown && filteredTags.length > 0 && (\n                <div className=\"absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto\">\n                  {filteredTags.slice(0, 10).map((tag) => (\n                    <button\n                      key={tag.value}\n                      type=\"button\"\n                      onClick={() => handleTagAdd(tag.value)}\n                      className=\"w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2\"\n                    >\n                      <Tag className=\"w-4 h-4 text-gray-400\" />\n                      <span>{tag.label}</span>\n                      {tag.count && (\n                        <span className=\"ml-auto text-xs text-gray-500\">\n                          {tag.count}\n                        </span>\n                      )}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 排序选项 */}\n          <div className=\"flex items-center gap-4 pt-2 border-t border-gray-200\">\n            <div className=\"flex items-center gap-2\">\n              <Settings className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">排序:</span>\n            </div>\n            \n            <div className=\"flex items-center gap-2\">\n              {SORT_OPTIONS.map((option) => (\n                <button\n                  key={option.value}\n                  type=\"button\"\n                  onClick={() => handleFilterChange('sortBy', option.value)}\n                  className={`flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors ${\n                    filters.sortBy === option.value\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-100'\n                  }`}\n                >\n                  {option.icon}\n                  {option.label}\n                </button>\n              ))}\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}\n              className=\"flex items-center gap-1 px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors\"\n              title={filters.sortOrder === 'asc' ? '升序' : '降序'}\n            >\n              <span className={`transform transition-transform ${filters.sortOrder === 'asc' ? 'rotate-180' : ''}`}>\n                ↓\n              </span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,0BAA0B;AAE1B;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;AAmDA,OAAO;AACP,MAAM,qBAAqC;IACzC;QAAE,OAAO;QAAY,OAAO;QAAM,OAAO;IAA8B;IACvE;QAAE,OAAO;QAAgB,OAAO;QAAM,OAAO;IAAgC;IAC7E;QAAE,OAAO;QAAY,OAAO;QAAM,OAAO;IAA0B;IACnE;QAAE,OAAO;QAAU,OAAO;QAAM,OAAO;IAAgC;CACxE;AAED,OAAO;AACP,MAAM,eAA+B;IACnC;QAAE,OAAO;QAAa,OAAO;QAAO,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;IACvE;QAAE,OAAO;QAAQ,OAAO;QAAQ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IACvE;QAAE,OAAO;QAAS,OAAO;QAAM,oBAAM,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAAa;CAClE;AAEc,SAAS,sBAAsB,EAC5C,OAAO,EACP,eAAe,EACf,aAAa,EAAE,EACf,gBAAgB,EAAE,EAClB,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,gBAAgB,EAChB,kBAAkB,IAAI,EACtB,cAAc,CAAC,EACY;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,UAAU;IACV,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC,KAAwB;YAC9D,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE,CAAC,IAAI,EAAE;YAAM;YAC9C,gBAAgB;QAClB;gEAAG;QAAC;QAAS;KAAgB;IAE7B,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW;gBACpC,MAAM,UAAU;uBAAI,QAAQ,IAAI;oBAAE;iBAAS;gBAC3C,mBAAmB,QAAQ;YAC7B;YACA,kBAAkB;YAClB,mBAAmB;QACrB;0DAAG;QAAC,QAAQ,IAAI;QAAE;KAAmB;IAErC,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACnC,MAAM,UAAU,QAAQ,IAAI,CAAC,MAAM;8EAAC,CAAA,MAAO,QAAQ;;YACnD,mBAAmB,QAAQ;QAC7B;6DAAG;QAAC,QAAQ,IAAI;QAAE;KAAmB;IAErC,UAAU;IACV,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACjC,gBAAgB;gBACd,MAAM,EAAE;gBACR,QAAQ;gBACR,WAAW;YACb;QACF;4DAAG;QAAC;KAAgB;IAEpB,UAAU;IACV,MAAM,uBAAuB;QAC3B,MAAM,cAAc,CAAC;QACrB,cAAc;QACd;IACF;IAEA,SAAS;IACT,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,MACxC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC3D,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK;IAGlC,aAAa;IACb,MAAM,mBAAmB,QAAQ,QAAQ,IACjB,QAAQ,UAAU,IAClB,QAAQ,IAAI,CAAC,MAAM,GAAG,KACtB,QAAQ,MAAM,KAAK,eACnB,QAAQ,SAAS,KAAK;IAE9C,qBACE,6LAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAEjF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,6LAAC,uNAAA,CAAA,cAAW;wCACV,WAAW,CAAC,0CAA0C,EACpD,aAAa,eAAe,IAC5B;;;;;;;;;;;;4BAIL,kCACC,6LAAC;gCAAK,WAAU;;oCACb;wCACC,QAAQ,QAAQ,GAAG,IAAI;wCACvB,QAAQ,UAAU,GAAG,IAAI;wCACzB,QAAQ,IAAI,CAAC,MAAM;wCAClB,QAAQ,MAAM,KAAK,eAAe,QAAQ,SAAS,KAAK,SAAU,IAAI;qCACxE,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;oCAAG;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;4BACZ,iCACC,6LAAC;gCAAK,WAAU;;oCACb;oCAAY;;;;;;;4BAIhB,kCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAQN,4BACC,6LAAC;gBAAI,WAAU;;oBAEZ,WAAW,MAAM,GAAG,mBACnB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,YAAY;wCAC9C,WAAW,CAAC,sDAAsD,EAChE,CAAC,QAAQ,QAAQ,GACb,6CACA,2DACJ;kDACH;;;;;;oCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,mBAAmB,YAAY,SAAS,KAAK;4CAC5D,WAAW,CAAC,sDAAsD,EAChE,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAC/B,6CACA,2DACJ;;gDAED,SAAS,KAAK;gDACd,SAAS,KAAK,kBACb,6LAAC;oDAAK,WAAU;;wDAA6B;wDACzC,SAAS,KAAK;wDAAC;;;;;;;;2CAZhB,SAAS,KAAK;;;;;;;;;;;;;;;;;kCAsB7B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,cAAc;wCAChD,WAAW,CAAC,sDAAsD,EAChE,CAAC,QAAQ,UAAU,GACf,6CACA,2DACJ;kDACH;;;;;;oCAGA,mBAAmB,GAAG,CAAC,CAAC,2BACvB,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,mBAAmB,cAAc,WAAW,KAAK;4CAChE,WAAW,CAAC,sDAAsD,EAChE,QAAQ,UAAU,KAAK,WAAW,KAAK,GACnC,6CACA,2DACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,uCAAuC,EAAE,WAAW,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,eAAe;;;;;;gDAC5G,WAAW,KAAK;;2CAVZ,WAAW,KAAK;;;;;;;;;;;;;;;;;kCAiB7B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;4BAK/D,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;oCACjB,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oCACtD,qBACE,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,WAAW,SAAS;0DACrB,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAVV;;;;;gCAcX;;;;;;0CAKJ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,SAAS,IAAM,mBAAmB;wCAClC,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;wCAC1D,aAAY;wCACZ,WAAU;;;;;;oCAIX,mBAAmB,aAAa,MAAM,GAAG,mBACxC,6LAAC;wCAAI,WAAU;kDACZ,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,oBAC9B,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,IAAI,KAAK;gDACrC,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAM,IAAI,KAAK;;;;;;oDACf,IAAI,KAAK,kBACR,6LAAC;wDAAK,WAAU;kEACb,IAAI,KAAK;;;;;;;+CATT,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAoB1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,mBAAmB,UAAU,OAAO,KAAK;wCACxD,WAAW,CAAC,uEAAuE,EACjF,QAAQ,MAAM,KAAK,OAAO,KAAK,GAC3B,8BACA,mCACJ;;4CAED,OAAO,IAAI;4CACX,OAAO,KAAK;;uCAVR,OAAO,KAAK;;;;;;;;;;0CAevB,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,mBAAmB,aAAa,QAAQ,SAAS,KAAK,QAAQ,SAAS;gCACtF,WAAU;gCACV,OAAO,QAAQ,SAAS,KAAK,QAAQ,OAAO;0CAE5C,cAAA,6LAAC;oCAAK,WAAW,CAAC,+BAA+B,EAAE,QAAQ,SAAS,KAAK,QAAQ,eAAe,IAAI;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpH;GA9SwB;KAAA", "debugId": null}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/hooks/useSearchHistory.ts"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索历史管理Hook\n// 提供本地搜索历史存储、去重、排序和清理功能\n\nimport { useState, useEffect, useCallback } from 'react';\n\n// 搜索历史项类型\ninterface SearchHistoryItem {\n  query: string;\n  timestamp: number;\n  count: number; // 搜索次数\n}\n\n// Hook配置选项\ninterface UseSearchHistoryOptions {\n  maxItems?: number; // 最大保存数量\n  storageKey?: string; // 本地存储键名\n  enableFrequencyTracking?: boolean; // 是否启用频率统计\n  autoCleanup?: boolean; // 是否自动清理过期项\n  maxAge?: number; // 最大保存时间（毫秒）\n}\n\n// Hook返回值类型\ninterface UseSearchHistoryReturn {\n  // 历史记录\n  history: string[];\n  recentHistory: string[];\n  popularHistory: string[];\n  \n  // 操作方法\n  addToHistory: (query: string) => void;\n  removeFromHistory: (query: string) => void;\n  clearHistory: () => void;\n  getSearchCount: (query: string) => number;\n  \n  // 统计信息\n  totalSearches: number;\n  uniqueSearches: number;\n  \n  // 工具方法\n  exportHistory: () => string;\n  importHistory: (data: string) => boolean;\n}\n\n// 默认配置\nconst DEFAULT_OPTIONS: UseSearchHistoryOptions = {\n  maxItems: 50,\n  storageKey: 'mysqlai_search_history',\n  enableFrequencyTracking: true,\n  autoCleanup: true,\n  maxAge: 30 * 24 * 60 * 60 * 1000 // 30天\n};\n\nexport function useSearchHistory(options: UseSearchHistoryOptions = {}): UseSearchHistoryReturn {\n  const opts = { ...DEFAULT_OPTIONS, ...options };\n  const [historyItems, setHistoryItems] = useState<SearchHistoryItem[]>([]);\n\n  // 从本地存储加载历史记录\n  const loadHistory = useCallback(() => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      const stored = localStorage.getItem(opts.storageKey!);\n      if (stored) {\n        const parsed: SearchHistoryItem[] = JSON.parse(stored);\n        \n        // 自动清理过期项\n        if (opts.autoCleanup) {\n          const now = Date.now();\n          const validItems = parsed.filter(item => \n            now - item.timestamp <= opts.maxAge!\n          );\n          setHistoryItems(validItems);\n          \n          // 如果清理了项目，更新存储\n          if (validItems.length !== parsed.length) {\n            localStorage.setItem(opts.storageKey!, JSON.stringify(validItems));\n          }\n        } else {\n          setHistoryItems(parsed);\n        }\n      }\n    } catch (error) {\n      console.error('加载搜索历史失败:', error);\n      setHistoryItems([]);\n    }\n  }, [opts.storageKey, opts.autoCleanup, opts.maxAge]);\n\n  // 保存历史记录到本地存储\n  const saveHistory = useCallback((items: SearchHistoryItem[]) => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      localStorage.setItem(opts.storageKey!, JSON.stringify(items));\n    } catch (error) {\n      console.error('保存搜索历史失败:', error);\n    }\n  }, [opts.storageKey]);\n\n  // 初始化加载\n  useEffect(() => {\n    loadHistory();\n  }, [loadHistory]);\n\n  // 添加搜索记录\n  const addToHistory = useCallback((query: string) => {\n    if (!query || query.trim().length < 2) return;\n    \n    const trimmedQuery = query.trim();\n    const now = Date.now();\n    \n    setHistoryItems(prevItems => {\n      // 查找是否已存在\n      const existingIndex = prevItems.findIndex(item => \n        item.query.toLowerCase() === trimmedQuery.toLowerCase()\n      );\n      \n      let newItems: SearchHistoryItem[];\n      \n      if (existingIndex >= 0) {\n        // 更新现有项目\n        newItems = [...prevItems];\n        newItems[existingIndex] = {\n          ...newItems[existingIndex],\n          timestamp: now,\n          count: opts.enableFrequencyTracking ? newItems[existingIndex].count + 1 : 1\n        };\n      } else {\n        // 添加新项目\n        const newItem: SearchHistoryItem = {\n          query: trimmedQuery,\n          timestamp: now,\n          count: 1\n        };\n        newItems = [newItem, ...prevItems];\n      }\n      \n      // 限制数量\n      if (newItems.length > opts.maxItems!) {\n        newItems = newItems.slice(0, opts.maxItems!);\n      }\n      \n      // 按时间戳排序（最新的在前）\n      newItems.sort((a, b) => b.timestamp - a.timestamp);\n      \n      // 保存到本地存储\n      saveHistory(newItems);\n      \n      return newItems;\n    });\n  }, [opts.maxItems, opts.enableFrequencyTracking, saveHistory]);\n\n  // 删除搜索记录\n  const removeFromHistory = useCallback((query: string) => {\n    setHistoryItems(prevItems => {\n      const newItems = prevItems.filter(item => \n        item.query.toLowerCase() !== query.toLowerCase()\n      );\n      saveHistory(newItems);\n      return newItems;\n    });\n  }, [saveHistory]);\n\n  // 清空搜索历史\n  const clearHistory = useCallback(() => {\n    setHistoryItems([]);\n    saveHistory([]);\n  }, [saveHistory]);\n\n  // 获取搜索次数\n  const getSearchCount = useCallback((query: string) => {\n    const item = historyItems.find(item => \n      item.query.toLowerCase() === query.toLowerCase()\n    );\n    return item?.count || 0;\n  }, [historyItems]);\n\n  // 导出历史记录\n  const exportHistory = useCallback(() => {\n    return JSON.stringify({\n      version: '1.0',\n      timestamp: Date.now(),\n      data: historyItems\n    });\n  }, [historyItems]);\n\n  // 导入历史记录\n  const importHistory = useCallback((data: string) => {\n    try {\n      const parsed = JSON.parse(data);\n      if (parsed.data && Array.isArray(parsed.data)) {\n        setHistoryItems(parsed.data);\n        saveHistory(parsed.data);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('导入搜索历史失败:', error);\n      return false;\n    }\n  }, [saveHistory]);\n\n  // 计算派生数据\n  const history = historyItems.map(item => item.query);\n  \n  const recentHistory = historyItems\n    .sort((a, b) => b.timestamp - a.timestamp)\n    .slice(0, 10)\n    .map(item => item.query);\n  \n  const popularHistory = opts.enableFrequencyTracking\n    ? historyItems\n        .filter(item => item.count > 1)\n        .sort((a, b) => b.count - a.count)\n        .slice(0, 10)\n        .map(item => item.query)\n    : [];\n\n  const totalSearches = historyItems.reduce((sum, item) => sum + item.count, 0);\n  const uniqueSearches = historyItems.length;\n\n  return {\n    // 历史记录\n    history,\n    recentHistory,\n    popularHistory,\n    \n    // 操作方法\n    addToHistory,\n    removeFromHistory,\n    clearHistory,\n    getSearchCount,\n    \n    // 统计信息\n    totalSearches,\n    uniqueSearches,\n    \n    // 工具方法\n    exportHistory,\n    importHistory\n  };\n}\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B,wBAAwB;AAExB;;AALA;;AA6CA,OAAO;AACP,MAAM,kBAA2C;IAC/C,UAAU;IACV,YAAY;IACZ,yBAAyB;IACzB,aAAa;IACb,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACzC;AAEO,SAAS,iBAAiB,UAAmC,CAAC,CAAC;;IACpE,MAAM,OAAO;QAAE,GAAG,eAAe;QAAE,GAAG,OAAO;IAAC;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAExE,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC9B,IAAI;gBACF,uCAAmC;;gBAAM;gBAEzC,MAAM,SAAS,aAAa,OAAO,CAAC,KAAK,UAAU;gBACnD,IAAI,QAAQ;oBACV,MAAM,SAA8B,KAAK,KAAK,CAAC;oBAE/C,UAAU;oBACV,IAAI,KAAK,WAAW,EAAE;wBACpB,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,aAAa,OAAO,MAAM;oFAAC,CAAA,OAC/B,MAAM,KAAK,SAAS,IAAI,KAAK,MAAM;;wBAErC,gBAAgB;wBAEhB,eAAe;wBACf,IAAI,WAAW,MAAM,KAAK,OAAO,MAAM,EAAE;4BACvC,aAAa,OAAO,CAAC,KAAK,UAAU,EAAG,KAAK,SAAS,CAAC;wBACxD;oBACF,OAAO;wBACL,gBAAgB;oBAClB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,gBAAgB,EAAE;YACpB;QACF;oDAAG;QAAC,KAAK,UAAU;QAAE,KAAK,WAAW;QAAE,KAAK,MAAM;KAAC;IAEnD,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC/B,IAAI;gBACF,uCAAmC;;gBAAM;gBAEzC,aAAa,OAAO,CAAC,KAAK,UAAU,EAAG,KAAK,SAAS,CAAC;YACxD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;oDAAG;QAAC,KAAK,UAAU;KAAC;IAEpB,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAChC,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YAEvC,MAAM,eAAe,MAAM,IAAI;YAC/B,MAAM,MAAM,KAAK,GAAG;YAEpB;8DAAgB,CAAA;oBACd,UAAU;oBACV,MAAM,gBAAgB,UAAU,SAAS;oFAAC,CAAA,OACxC,KAAK,KAAK,CAAC,WAAW,OAAO,aAAa,WAAW;;oBAGvD,IAAI;oBAEJ,IAAI,iBAAiB,GAAG;wBACtB,SAAS;wBACT,WAAW;+BAAI;yBAAU;wBACzB,QAAQ,CAAC,cAAc,GAAG;4BACxB,GAAG,QAAQ,CAAC,cAAc;4BAC1B,WAAW;4BACX,OAAO,KAAK,uBAAuB,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI;wBAC5E;oBACF,OAAO;wBACL,QAAQ;wBACR,MAAM,UAA6B;4BACjC,OAAO;4BACP,WAAW;4BACX,OAAO;wBACT;wBACA,WAAW;4BAAC;+BAAY;yBAAU;oBACpC;oBAEA,OAAO;oBACP,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,EAAG;wBACpC,WAAW,SAAS,KAAK,CAAC,GAAG,KAAK,QAAQ;oBAC5C;oBAEA,gBAAgB;oBAChB,SAAS,IAAI;sEAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;oBAEjD,UAAU;oBACV,YAAY;oBAEZ,OAAO;gBACT;;QACF;qDAAG;QAAC,KAAK,QAAQ;QAAE,KAAK,uBAAuB;QAAE;KAAY;IAE7D,SAAS;IACT,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACrC;mEAAgB,CAAA;oBACd,MAAM,WAAW,UAAU,MAAM;oFAAC,CAAA,OAChC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;;oBAEhD,YAAY;oBACZ,OAAO;gBACT;;QACF;0DAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,gBAAgB,EAAE;YAClB,YAAY,EAAE;QAChB;qDAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAClC,MAAM,OAAO,aAAa,IAAI;qEAAC,CAAA,OAC7B,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;;YAEhD,OAAO,MAAM,SAAS;QACxB;uDAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,OAAO,KAAK,SAAS,CAAC;gBACpB,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,MAAM;YACR;QACF;sDAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACjC,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;oBAC7C,gBAAgB,OAAO,IAAI;oBAC3B,YAAY,OAAO,IAAI;oBACvB,OAAO;gBACT;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO;YACT;QACF;sDAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,UAAU,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAEnD,MAAM,gBAAgB,aACnB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAEzB,MAAM,iBAAiB,KAAK,uBAAuB,GAC/C,aACG,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,GAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,IACzB,EAAE;IAEN,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3E,MAAM,iBAAiB,aAAa,MAAM;IAE1C,OAAO;QACL,OAAO;QACP;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA;QACA;QAEA,OAAO;QACP;QACA;QAEA,OAAO;QACP;QACA;IACF;AACF;GA5LgB", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchHistory.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索历史界面组件\n// 提供搜索历史显示、管理和快速重新搜索功能\n\nimport React, { useState } from 'react';\nimport { useSearchHistory } from '@/hooks/useSearchHistory';\nimport {\n  Clock,\n  TrendingUp,\n  X,\n  Trash2,\n  RotateCcw,\n  BarChart3\n} from 'lucide-react';\n\n// 组件属性\ninterface SearchHistoryProps {\n  onSearchSelect: (query: string) => void;\n  onClose?: () => void;\n  className?: string;\n  showStats?: boolean;\n  maxRecentItems?: number;\n  maxPopularItems?: number;\n}\n\n// 历史项组件属性\ninterface HistoryItemProps {\n  query: string;\n  count?: number;\n  timestamp?: number;\n  onSelect: (query: string) => void;\n  onRemove: (query: string) => void;\n  showCount?: boolean;\n}\n\n// 历史项组件\nfunction HistoryItem({\n  query,\n  count,\n  timestamp,\n  onSelect,\n  onRemove,\n  showCount = false\n}: HistoryItemProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const formatTimestamp = (ts?: number) => {\n    if (!ts) return '';\n    const date = new Date(ts);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) return '今天';\n    if (diffDays === 1) return '昨天';\n    if (diffDays < 7) return `${diffDays}天前`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;\n    return `${Math.floor(diffDays / 30)}月前`;\n  };\n\n  return (\n    <div\n      className=\"flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 group\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <button\n        type=\"button\"\n        onClick={() => onSelect(query)}\n        className=\"flex-1 flex items-center gap-3 text-left min-w-0\"\n      >\n        <Clock className=\"w-4 h-4 text-gray-400 flex-shrink-0\" />\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"font-medium text-gray-900 truncate\">\n            {query}\n          </div>\n          <div className=\"flex items-center gap-2 text-xs text-gray-500 mt-1\">\n            {timestamp && (\n              <span>{formatTimestamp(timestamp)}</span>\n            )}\n            {showCount && count && count > 1 && (\n              <>\n                <span>•</span>\n                <span>{count}次搜索</span>\n              </>\n            )}\n          </div>\n        </div>\n      </button>\n\n      {isHovered && (\n        <button\n          type=\"button\"\n          onClick={(e) => {\n            e.stopPropagation();\n            onRemove(query);\n          }}\n          className=\"p-1 text-gray-400 hover:text-red-500 transition-colors duration-150\"\n          title=\"删除此搜索记录\"\n        >\n          <X className=\"w-4 h-4\" />\n        </button>\n      )}\n    </div>\n  );\n}\n\nexport default function SearchHistory({\n  onSearchSelect,\n  onClose,\n  className = '',\n  showStats = true,\n  maxRecentItems = 8,\n  maxPopularItems = 5\n}: SearchHistoryProps) {\n  const {\n    recentHistory,\n    popularHistory,\n    totalSearches,\n    uniqueSearches,\n    removeFromHistory,\n    clearHistory,\n    getSearchCount\n  } = useSearchHistory();\n\n  const [activeTab, setActiveTab] = useState<'recent' | 'popular'>('recent');\n  const [showClearConfirm, setShowClearConfirm] = useState(false);\n\n  // 处理搜索选择\n  const handleSearchSelect = (query: string) => {\n    onSearchSelect(query);\n    onClose?.();\n  };\n\n  // 处理删除单个记录\n  const handleRemoveItem = (query: string) => {\n    removeFromHistory(query);\n  };\n\n  // 处理清空所有记录\n  const handleClearAll = () => {\n    if (showClearConfirm) {\n      clearHistory();\n      setShowClearConfirm(false);\n      onClose?.();\n    } else {\n      setShowClearConfirm(true);\n    }\n  };\n\n  // 取消清空确认\n  const handleCancelClear = () => {\n    setShowClearConfirm(false);\n  };\n\n  const hasHistory = recentHistory.length > 0 || popularHistory.length > 0;\n\n  return (\n    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg ${className}`}>\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">搜索历史</h3>\n        <div className=\"flex items-center gap-2\">\n          {hasHistory && (\n            <button\n              type=\"button\"\n              onClick={handleClearAll}\n              className={`px-3 py-1 text-sm rounded-md transition-colors duration-150 ${\n                showClearConfirm\n                  ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                  : 'text-gray-500 hover:text-red-600 hover:bg-red-50'\n              }`}\n            >\n              {showClearConfirm ? (\n                <div className=\"flex items-center gap-2\">\n                  <span>确认清空?</span>\n                  <button\n                    type=\"button\"\n                    onClick={handleCancelClear}\n                    className=\"text-gray-500 hover:text-gray-700\"\n                  >\n                    取消\n                  </button>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1\">\n                  <Trash2 className=\"w-4 h-4\" />\n                  <span>清空</span>\n                </div>\n              )}\n            </button>\n          )}\n          {onClose && (\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-150\"\n              title=\"关闭搜索历史\"\n              aria-label=\"关闭搜索历史\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      {showStats && hasHistory && (\n        <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n          <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n            <div className=\"flex items-center gap-1\">\n              <BarChart3 className=\"w-4 h-4\" />\n              <span>总搜索: {totalSearches}次</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <RotateCcw className=\"w-4 h-4\" />\n              <span>不同查询: {uniqueSearches}个</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 标签页 */}\n      {hasHistory && (\n        <div className=\"flex border-b border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={() => setActiveTab('recent')}\n            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${\n              activeTab === 'recent'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <div className=\"flex items-center justify-center gap-2\">\n              <Clock className=\"w-4 h-4\" />\n              <span>最近搜索</span>\n              {recentHistory.length > 0 && (\n                <span className=\"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full\">\n                  {recentHistory.length}\n                </span>\n              )}\n            </div>\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => setActiveTab('popular')}\n            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${\n              activeTab === 'popular'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <div className=\"flex items-center justify-center gap-2\">\n              <TrendingUp className=\"w-4 h-4\" />\n              <span>热门搜索</span>\n              {popularHistory.length > 0 && (\n                <span className=\"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full\">\n                  {popularHistory.length}\n                </span>\n              )}\n            </div>\n          </button>\n        </div>\n      )}\n\n      {/* 内容区域 */}\n      <div className=\"p-4\">\n        {!hasHistory ? (\n          <div className=\"text-center py-8\">\n            <Clock className=\"w-12 h-12 text-gray-300 mx-auto mb-3\" />\n            <p className=\"text-gray-500 text-sm\">暂无搜索历史</p>\n            <p className=\"text-gray-400 text-xs mt-1\">开始搜索后，历史记录会显示在这里</p>\n          </div>\n        ) : (\n          <div className=\"space-y-1\">\n            {activeTab === 'recent' && (\n              <>\n                {recentHistory.slice(0, maxRecentItems).map((query, index) => (\n                  <HistoryItem\n                    key={`recent-${query}-${index}`}\n                    query={query}\n                    count={getSearchCount(query)}\n                    onSelect={handleSearchSelect}\n                    onRemove={handleRemoveItem}\n                    showCount={true}\n                  />\n                ))}\n                {recentHistory.length > maxRecentItems && (\n                  <div className=\"text-center py-2\">\n                    <span className=\"text-xs text-gray-500\">\n                      还有 {recentHistory.length - maxRecentItems} 条记录...\n                    </span>\n                  </div>\n                )}\n              </>\n            )}\n\n            {activeTab === 'popular' && (\n              <>\n                {popularHistory.length === 0 ? (\n                  <div className=\"text-center py-4\">\n                    <TrendingUp className=\"w-8 h-8 text-gray-300 mx-auto mb-2\" />\n                    <p className=\"text-gray-500 text-sm\">暂无热门搜索</p>\n                    <p className=\"text-gray-400 text-xs mt-1\">多次搜索相同内容后会显示在这里</p>\n                  </div>\n                ) : (\n                  popularHistory.slice(0, maxPopularItems).map((query, index) => (\n                    <HistoryItem\n                      key={`popular-${query}-${index}`}\n                      query={query}\n                      count={getSearchCount(query)}\n                      onSelect={handleSearchSelect}\n                      onRemove={handleRemoveItem}\n                      showCount={true}\n                    />\n                  ))\n                )}\n              </>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,uBAAuB;AAEvB;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;AAoCA,QAAQ;AACR,SAAS,YAAY,EACnB,KAAK,EACL,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EACA;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,OAAO;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;QACxC,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QACzD,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC;IACzC;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,6LAAC;gBACC,MAAK;gBACL,SAAS,IAAM,SAAS;gBACxB,WAAU;;kCAEV,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,6LAAC;gCAAI,WAAU;;oCACZ,2BACC,6LAAC;kDAAM,gBAAgB;;;;;;oCAExB,aAAa,SAAS,QAAQ,mBAC7B;;0DACE,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtB,2BACC,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,SAAS;gBACX;gBACA,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKvB;GArES;KAAA;AAuEM,SAAS,cAAc,EACpC,cAAc,EACd,OAAO,EACP,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,iBAAiB,CAAC,EAClB,kBAAkB,CAAC,EACA;;IACnB,MAAM,EACJ,aAAa,EACb,cAAc,EACd,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;IACpB;IAEA,WAAW;IACX,MAAM,iBAAiB;QACrB,IAAI,kBAAkB;YACpB;YACA,oBAAoB;YACpB;QACF,OAAO;YACL,oBAAoB;QACtB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,oBAAoB;IACtB;IAEA,MAAM,aAAa,cAAc,MAAM,GAAG,KAAK,eAAe,MAAM,GAAG;IAEvE,qBACE,6LAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAEjF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;4BACZ,4BACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAW,CAAC,4DAA4D,EACtE,mBACI,6CACA,oDACJ;0CAED,iCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;4BAKb,yBACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;gCACN,cAAW;0CAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOpB,aAAa,4BACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;;wCAAK;wCAAM;wCAAc;;;;;;;;;;;;;sCAE5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;;wCAAK;wCAAO;wCAAe;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,oEAAoE,EAC9E,cAAc,WACV,wDACA,qCACJ;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;gCACL,cAAc,MAAM,GAAG,mBACtB,6LAAC;oCAAK,WAAU;8CACb,cAAc,MAAM;;;;;;;;;;;;;;;;;kCAK7B,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,oEAAoE,EAC9E,cAAc,YACV,wDACA,qCACJ;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;8CAAK;;;;;;gCACL,eAAe,MAAM,GAAG,mBACvB,6LAAC;oCAAK,WAAU;8CACb,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BASlC,6LAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCACrC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,0BACb;;gCACG,cAAc,KAAK,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAClD,6LAAC;wCAEC,OAAO;wCACP,OAAO,eAAe;wCACtB,UAAU;wCACV,UAAU;wCACV,WAAW;uCALN,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,OAAO;;;;;gCAQlC,cAAc,MAAM,GAAG,gCACtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAAwB;4CAClC,cAAc,MAAM,GAAG;4CAAe;;;;;;;;;;;;;;wBAOnD,cAAc,2BACb;sCACG,eAAe,MAAM,KAAK,kBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;uCAG5C,eAAe,KAAK,CAAC,GAAG,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBACnD,6LAAC;oCAEC,OAAO;oCACP,OAAO,eAAe;oCACtB,UAAU;oCACV,UAAU;oCACV,WAAW;mCALN,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAgBtD;IA1NwB;;QAgBlB,iJAAA,CAAA,mBAAgB;;;MAhBE", "debugId": null}}, {"offset": {"line": 2117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchResultItem.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索结果项组件\n// 提供搜索结果高亮显示、相关性分数、快速操作功能\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport {\n  FileText,\n  Tag,\n  Calendar,\n  Star,\n  Eye,\n  Bookmark,\n  Share,\n  ChevronRight\n} from 'lucide-react';\n\n// 搜索结果项类型\ninterface SearchResultItemData {\n  id: string;\n  title: string;\n  description?: string;\n  content?: string;\n  category?: {\n    id: string;\n    name: string;\n    icon?: string;\n    color?: string;\n  };\n  tags?: string[];\n  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert';\n  last_updated: string;\n  created_at: string;\n  view_count?: number;\n  relevanceScore?: number;\n  highlight?: {\n    title?: string;\n    description?: string;\n    content?: string;\n  };\n}\n\n// 组件属性\ninterface SearchResultItemProps {\n  item: SearchResultItemData;\n  query?: string;\n  onBookmark?: (id: string, bookmarked: boolean) => void;\n  onShare?: (item: SearchResultItemData) => void;\n  showRelevanceScore?: boolean;\n  showActions?: boolean;\n  className?: string;\n  isBookmarked?: boolean;\n}\n\n// 难度配置\nconst DIFFICULTY_CONFIG = {\n  beginner: { label: '初级', color: 'bg-green-100 text-green-800', icon: '●' },\n  intermediate: { label: '中级', color: 'bg-yellow-100 text-yellow-800', icon: '●●' },\n  advanced: { label: '高级', color: 'bg-orange-100 text-orange-800', icon: '●●●' },\n  expert: { label: '专家', color: 'bg-red-100 text-red-800', icon: '●●●●' }\n};\n\nexport default function SearchResultItem({\n  item,\n  query,\n  onBookmark,\n  onShare,\n  showRelevanceScore = false,\n  showActions = true,\n  className = '',\n  isBookmarked = false\n}: SearchResultItemProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  // 处理高亮文本渲染\n  const renderHighlightedText = (text: string, highlightedText?: string) => {\n    if (!highlightedText || !query) {\n      return <span>{text}</span>;\n    }\n\n    // 如果有高亮文本，直接使用（已包含<mark>标签）\n    return (\n      <span \n        dangerouslySetInnerHTML={{ __html: highlightedText }}\n        className=\"[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded\"\n      />\n    );\n  };\n\n  // 格式化时间\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) return '今天';\n    if (diffDays === 1) return '昨天';\n    if (diffDays < 7) return `${diffDays}天前`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;\n    return `${Math.floor(diffDays / 30)}月前`;\n  };\n\n  // 处理收藏\n  const handleBookmark = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onBookmark?.(item.id, !isBookmarked);\n  };\n\n  // 处理分享\n  const handleShare = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onShare?.(item);\n  };\n\n  // 获取难度配置\n  const difficultyConfig = item.difficulty ? DIFFICULTY_CONFIG[item.difficulty] : null;\n\n  return (\n    <div\n      className={`group bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 ${className}`}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <Link href={`/knowledge/${item.id}`} className=\"block p-6\">\n        {/* 头部信息 */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex-1 min-w-0\">\n            {/* 标题 */}\n            <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2\">\n              {renderHighlightedText(item.title, item.highlight?.title)}\n            </h3>\n\n            {/* 元信息 */}\n            <div className=\"flex items-center gap-4 text-sm text-gray-500 mb-3\">\n              {/* 分类 */}\n              {item.category && (\n                <div className=\"flex items-center gap-1\">\n                  <div \n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: item.category.color || '#6B7280' }}\n                  />\n                  <span>{item.category.name}</span>\n                </div>\n              )}\n\n              {/* 难度 */}\n              {difficultyConfig && (\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${difficultyConfig.color}`}>\n                  {difficultyConfig.icon} {difficultyConfig.label}\n                </span>\n              )}\n\n              {/* 更新时间 */}\n              <div className=\"flex items-center gap-1\">\n                <Calendar className=\"w-4 h-4\" />\n                <span>{formatDate(item.last_updated)}</span>\n              </div>\n\n              {/* 浏览次数 */}\n              {item.view_count && item.view_count > 0 && (\n                <div className=\"flex items-center gap-1\">\n                  <Eye className=\"w-4 h-4\" />\n                  <span>{item.view_count}</span>\n                </div>\n              )}\n\n              {/* 相关性分数 */}\n              {showRelevanceScore && item.relevanceScore && (\n                <div className=\"flex items-center gap-1\">\n                  <Star className=\"w-4 h-4\" />\n                  <span>相关性: {Math.round(item.relevanceScore)}</span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          {showActions && isHovered && (\n            <div className=\"flex items-center gap-2 ml-4\">\n              <button\n                type=\"button\"\n                onClick={handleBookmark}\n                className=\"p-2 text-gray-400 hover:text-yellow-500 transition-colors\"\n                title={isBookmarked ? '取消收藏' : '收藏文章'}\n              >\n                {isBookmarked ? (\n                  <Bookmark className=\"w-5 h-5 text-yellow-500 fill-current\" />\n                ) : (\n                  <Bookmark className=\"w-5 h-5\" />\n                )}\n              </button>\n              \n              <button\n                type=\"button\"\n                onClick={handleShare}\n                className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\"\n                title=\"分享文章\"\n              >\n                <Share className=\"w-5 h-5\" />\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* 描述 */}\n        {item.description && (\n          <p className=\"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2\">\n            {renderHighlightedText(item.description, item.highlight?.description)}\n          </p>\n        )}\n\n        {/* 内容预览 */}\n        {item.highlight?.content && (\n          <div className=\"bg-gray-50 border-l-4 border-blue-500 p-3 mb-4\">\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\n              <span \n                dangerouslySetInnerHTML={{ __html: item.highlight.content }}\n                className=\"[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded\"\n              />\n            </p>\n          </div>\n        )}\n\n        {/* 标签 */}\n        {item.tags && item.tags.length > 0 && (\n          <div className=\"flex items-center gap-2 mb-4\">\n            <Tag className=\"w-4 h-4 text-gray-400\" />\n            <div className=\"flex flex-wrap gap-2\">\n              {item.tags.slice(0, 5).map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full hover:bg-gray-200 transition-colors\"\n                >\n                  {tag}\n                </span>\n              ))}\n              {item.tags.length > 5 && (\n                <span className=\"px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full\">\n                  +{item.tags.length - 5}\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* 底部 */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2 text-xs text-gray-400\">\n            <FileText className=\"w-4 h-4\" />\n            <span>知识库文章</span>\n          </div>\n\n          <div className=\"flex items-center gap-1 text-blue-600 group-hover:text-blue-700 transition-colors\">\n            <span className=\"text-sm font-medium\">查看详情</span>\n            <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\n          </div>\n        </div>\n      </Link>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,uBAAuB;AACvB,0BAA0B;AAE1B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;AAuDA,OAAO;AACP,MAAM,oBAAoB;IACxB,UAAU;QAAE,OAAO;QAAM,OAAO;QAA+B,MAAM;IAAI;IACzE,cAAc;QAAE,OAAO;QAAM,OAAO;QAAiC,MAAM;IAAK;IAChF,UAAU;QAAE,OAAO;QAAM,OAAO;QAAiC,MAAM;IAAM;IAC7E,QAAQ;QAAE,OAAO;QAAM,OAAO;QAA2B,MAAM;IAAO;AACxE;AAEe,SAAS,iBAAiB,EACvC,IAAI,EACJ,KAAK,EACL,UAAU,EACV,OAAO,EACP,qBAAqB,KAAK,EAC1B,cAAc,IAAI,EAClB,YAAY,EAAE,EACd,eAAe,KAAK,EACE;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,WAAW;IACX,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAC9B,qBAAO,6LAAC;0BAAM;;;;;;QAChB;QAEA,4BAA4B;QAC5B,qBACE,6LAAC;YACC,yBAAyB;gBAAE,QAAQ;YAAgB;YACnD,WAAU;;;;;;IAGhB;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;QACxC,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QACzD,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC;IACzC;IAEA,OAAO;IACP,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,aAAa,KAAK,EAAE,EAAE,CAAC;IACzB;IAEA,OAAO;IACP,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,mBAAmB,KAAK,UAAU,GAAG,iBAAiB,CAAC,KAAK,UAAU,CAAC,GAAG;IAEhF,qBACE,6LAAC;QACC,WAAW,CAAC,6FAA6F,EAAE,WAAW;QACtH,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;YAAE,WAAU;;8BAE7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAG,WAAU;8CACX,sBAAsB,KAAK,KAAK,EAAE,KAAK,SAAS,EAAE;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;wCAEZ,KAAK,QAAQ,kBACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,KAAK,QAAQ,CAAC,KAAK,IAAI;oDAAU;;;;;;8DAE7D,6LAAC;8DAAM,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;wCAK5B,kCACC,6LAAC;4CAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,KAAK,EAAE;;gDACpF,iBAAiB,IAAI;gDAAC;gDAAE,iBAAiB,KAAK;;;;;;;sDAKnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAM,WAAW,KAAK,YAAY;;;;;;;;;;;;wCAIpC,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,mBACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;8DAAM,KAAK,UAAU;;;;;;;;;;;;wCAKzB,sBAAsB,KAAK,cAAc,kBACxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;wDAAK;wDAAM,KAAK,KAAK,CAAC,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;wBAOjD,eAAe,2BACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAO,eAAe,SAAS;8CAE9B,6BACC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAIxB,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOxB,KAAK,WAAW,kBACf,6LAAC;oBAAE,WAAU;8BACV,sBAAsB,KAAK,WAAW,EAAE,KAAK,SAAS,EAAE;;;;;;gBAK5D,KAAK,SAAS,EAAE,yBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACX,cAAA,6LAAC;4BACC,yBAAyB;gCAAE,QAAQ,KAAK,SAAS,CAAC,OAAO;4BAAC;4BAC1D,WAAU;;;;;;;;;;;;;;;;gBAOjB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;oCAAK,WAAU;;wCAA2D;wCACvE,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;8BAQ/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;sCAGR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GAzMwB;KAAA", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/IntelligentSearch.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 智能搜索主组件\n// 整合搜索输入、建议、筛选、历史、结果等所有搜索功能\n\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Search, X } from 'lucide-react';\nimport SearchSuggestions from './SearchSuggestions';\nimport AdvancedSearchFilters from './AdvancedSearchFilters';\nimport SearchHistory from './SearchHistory';\nimport SearchResultItem from './SearchResultItem';\nimport { useSearchHistory } from '@/hooks/useSearchHistory';\nimport { searchApi } from '@/lib/api/knowledge';\n\n// 搜索状态类型\ninterface SearchState {\n  query: string;\n  isSearching: boolean;\n  results: any[];\n  suggestions: any[];\n  showSuggestions: boolean;\n  showHistory: boolean;\n  showFilters: boolean;\n  filters: {\n    category?: string;\n    difficulty?: string;\n    tags: string[];\n    sortBy: 'relevance' | 'date' | 'title';\n    sortOrder: 'asc' | 'desc';\n  };\n  error?: string;\n  totalResults: number;\n}\n\n// 组件属性\ninterface IntelligentSearchProps {\n  placeholder?: string;\n  autoFocus?: boolean;\n  showAdvancedFilters?: boolean;\n  showSearchHistory?: boolean;\n  maxResults?: number;\n  className?: string;\n  onResultSelect?: (result: any) => void;\n  categories?: Array<{ value: string; label: string; count?: number }>;\n  availableTags?: Array<{ value: string; label: string; count?: number }>;\n}\n\nexport default function IntelligentSearch({\n  placeholder = '搜索知识库...',\n  autoFocus = false,\n  showAdvancedFilters = true,\n  showSearchHistory = true,\n  maxResults = 20,\n  className = '',\n  onResultSelect,\n  categories = [],\n  availableTags = []\n}: IntelligentSearchProps) {\n  // 状态管理\n  const [searchState, setSearchState] = useState<SearchState>({\n    query: '',\n    isSearching: false,\n    results: [],\n    suggestions: [],\n    showSuggestions: false,\n    showHistory: false,\n    showFilters: false,\n    filters: {\n      tags: [],\n      sortBy: 'relevance',\n      sortOrder: 'desc'\n    },\n    totalResults: 0\n  });\n\n  // 搜索历史Hook\n  const {\n    addToHistory,\n    recentHistory,\n    popularHistory,\n    clearHistory,\n    removeFromHistory,\n    getSearchCount,\n    totalSearches,\n    uniqueSearches\n  } = useSearchHistory();\n\n  // 引用\n  const searchInputRef = useRef<HTMLInputElement>(null);\n  const searchTimeoutRef = useRef<NodeJS.Timeout>();\n\n  // 执行搜索\n  const performSearch = useCallback(async (query: string, filters?: any) => {\n    if (!query.trim()) {\n      setSearchState(prev => ({\n        ...prev,\n        results: [],\n        totalResults: 0,\n        showSuggestions: false\n      }));\n      return;\n    }\n\n    setSearchState(prev => ({\n      ...prev,\n      isSearching: true,\n      error: undefined\n    }));\n\n    try {\n      // 使用传入的filters或当前状态的filters\n      const currentFilters = filters || searchState.filters;\n\n      // 调用搜索API\n      const response = await searchApi.search({\n        query,\n        category: currentFilters.category,\n        difficulty: currentFilters.difficulty,\n        tags: currentFilters.tags,\n        sortBy: currentFilters.sortBy,\n        sortOrder: currentFilters.sortOrder,\n        limit: maxResults\n      });\n\n      if (response.success) {\n        // 添加到搜索历史\n        addToHistory(query);\n\n        setSearchState(prev => ({\n          ...prev,\n          results: response.data || [],\n          totalResults: response.data?.length || 0,\n          isSearching: false,\n          showSuggestions: false,\n          showHistory: false\n        }));\n      } else {\n        throw new Error(response.error || '搜索失败');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSearchState(prev => ({\n        ...prev,\n        error: error instanceof Error ? error.message : '搜索出错，请稍后重试',\n        isSearching: false,\n        results: [],\n        totalResults: 0\n      }));\n    }\n  }, [maxResults, addToHistory]); // 移除searchState.filters依赖\n\n  // 处理搜索输入变化\n  const handleSearchChange = useCallback((value: string) => {\n    setSearchState(prev => ({\n      ...prev,\n      query: value,\n      showSuggestions: value.length >= 2,\n      showHistory: value.length === 0\n    }));\n\n    // 清除之前的搜索定时器\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // 如果查询为空，清空结果\n    if (!value.trim()) {\n      setSearchState(prev => ({\n        ...prev,\n        results: [],\n        totalResults: 0,\n        showSuggestions: false\n      }));\n      return;\n    }\n\n    // 延迟执行搜索 - 只有当输入长度>=2时才搜索\n    if (value.trim().length >= 2) {\n      searchTimeoutRef.current = setTimeout(() => {\n        performSearch(value);\n      }, 800); // 增加延迟到800ms，减少频繁搜索\n    }\n  }, [performSearch]);\n\n  // 处理搜索提交\n  const handleSearchSubmit = useCallback((query: string) => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    performSearch(query);\n  }, [performSearch]);\n\n  // 处理建议选择\n  const handleSuggestionSelect = useCallback((suggestion: any) => {\n    const query = suggestion.text || suggestion.query;\n    setSearchState(prev => ({ ...prev, query }));\n    handleSearchSubmit(query);\n  }, [handleSearchSubmit]);\n\n  // 关闭建议\n  const handleCloseSuggestions = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showSuggestions: false }));\n  }, []);\n\n  // 关闭历史\n  const handleCloseHistory = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showHistory: false }));\n  }, []);\n\n  // 处理历史选择\n  const handleHistorySelect = useCallback((query: string) => {\n    setSearchState(prev => ({ ...prev, query }));\n    handleSearchSubmit(query);\n  }, [handleSearchSubmit]);\n\n  // 处理筛选器变化\n  const handleFiltersChange = useCallback((filters: any) => {\n    setSearchState(prev => {\n      const newState = { ...prev, filters };\n\n      // 如果有查询，重新搜索\n      if (prev.query.trim()) {\n        // 使用setTimeout避免在setState中直接调用performSearch\n        setTimeout(() => {\n          performSearch(prev.query, filters);\n        }, 0);\n      }\n\n      return newState;\n    });\n  }, [performSearch]);\n\n  // 清空搜索\n  const handleClearSearch = useCallback(() => {\n    setSearchState(prev => ({\n      ...prev,\n      query: '',\n      results: [],\n      totalResults: 0,\n      showSuggestions: false,\n      showHistory: true,\n      error: undefined\n    }));\n    searchInputRef.current?.focus();\n  }, []);\n\n  // 切换筛选器显示\n  const toggleFilters = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showFilters: !prev.showFilters }));\n  }, []);\n\n  // 组件挂载时自动聚焦\n  useEffect(() => {\n    if (autoFocus && searchInputRef.current) {\n      searchInputRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <div className={`w-full max-w-4xl mx-auto ${className}`}>\n      {/* 搜索输入区域 */}\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Search className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          \n          <input\n            ref={searchInputRef}\n            type=\"text\"\n            value={searchState.query}\n            onChange={(e) => handleSearchChange(e.target.value)}\n            onKeyDown={(e) => {\n              if (e.key === 'Enter') {\n                handleSearchSubmit(searchState.query);\n              }\n            }}\n            placeholder={placeholder}\n            className=\"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n          />\n\n          {searchState.query && (\n            <button\n              type=\"button\"\n              onClick={handleClearSearch}\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          )}\n        </div>\n\n        {/* 搜索建议 */}\n        {searchState.showSuggestions && (\n          <div className=\"absolute top-full left-0 right-0 z-20 mt-1\">\n            <SearchSuggestions\n              query={searchState.query}\n              onSelect={handleSuggestionSelect}\n              onClose={handleCloseSuggestions}\n            />\n          </div>\n        )}\n\n        {/* 搜索历史 */}\n        {searchState.showHistory && showSearchHistory && (\n          <div className=\"absolute top-full left-0 right-0 z-20 mt-1\">\n            <SearchHistory\n              onSelect={handleHistorySelect}\n              onClose={handleCloseHistory}\n              maxItems={8}\n              showStats={true}\n            />\n          </div>\n        )}\n      </div>\n\n      {/* 高级筛选器 */}\n      {showAdvancedFilters && (\n        <div className=\"mt-4\">\n          <AdvancedSearchFilters\n            filters={searchState.filters}\n            onFiltersChange={handleFiltersChange}\n            categories={categories}\n            availableTags={availableTags}\n            isCollapsed={!searchState.showFilters}\n            onToggleCollapse={toggleFilters}\n            showResultCount={searchState.results.length > 0}\n            resultCount={searchState.totalResults}\n          />\n        </div>\n      )}\n\n      {/* 搜索状态和结果 */}\n      <div className=\"mt-6\">\n        {/* 加载状态 */}\n        {searchState.isSearching && (\n          <div className=\"text-center py-8\">\n            <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-2 text-gray-600\">搜索中...</p>\n          </div>\n        )}\n\n        {/* 错误状态 */}\n        {searchState.error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 text-center\">\n            <p className=\"text-red-600\">{searchState.error}</p>\n          </div>\n        )}\n\n        {/* 搜索结果 */}\n        {!searchState.isSearching && !searchState.error && searchState.results.length > 0 && (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                搜索结果 ({searchState.totalResults})\n              </h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              {searchState.results.map((result, index) => (\n                <SearchResultItem\n                  key={result.id || index}\n                  item={result}\n                  query={searchState.query}\n                  showRelevanceScore={true}\n                  onBookmark={(id, bookmarked) => {\n                    // TODO: 实现收藏功能\n                    console.log('Bookmark:', id, bookmarked);\n                  }}\n                  onShare={(item) => {\n                    // TODO: 实现分享功能\n                    console.log('Share:', item);\n                  }}\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 无结果状态 */}\n        {!searchState.isSearching && !searchState.error && searchState.query && searchState.results.length === 0 && (\n          <div className=\"text-center py-8\">\n            <Search className=\"mx-auto h-12 w-12 text-gray-300\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">未找到相关结果</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              尝试使用不同的关键词或调整筛选条件\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,uBAAuB;AACvB,4BAA4B;AAE5B;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;AA+Ce,SAAS,kBAAkB,EACxC,cAAc,UAAU,EACxB,YAAY,KAAK,EACjB,sBAAsB,IAAI,EAC1B,oBAAoB,IAAI,EACxB,aAAa,EAAE,EACf,YAAY,EAAE,EACd,cAAc,EACd,aAAa,EAAE,EACf,gBAAgB,EAAE,EACK;;IACvB,OAAO;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,OAAO;QACP,aAAa;QACb,SAAS,EAAE;QACX,aAAa,EAAE;QACf,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,SAAS;YACP,MAAM,EAAE;YACR,QAAQ;YACR,WAAW;QACb;QACA,cAAc;IAChB;IAEA,WAAW;IACX,MAAM,EACJ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD;IAEnB,KAAK;IACL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE9B,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO,OAAe;YACtD,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB;oEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,SAAS,EAAE;4BACX,cAAc;4BACd,iBAAiB;wBACnB,CAAC;;gBACD;YACF;YAEA;gEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,aAAa;wBACb,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,iBAAiB,WAAW,YAAY,OAAO;gBAErD,UAAU;gBACV,MAAM,WAAW,MAAM,+IAAA,CAAA,YAAS,CAAC,MAAM,CAAC;oBACtC;oBACA,UAAU,eAAe,QAAQ;oBACjC,YAAY,eAAe,UAAU;oBACrC,MAAM,eAAe,IAAI;oBACzB,QAAQ,eAAe,MAAM;oBAC7B,WAAW,eAAe,SAAS;oBACnC,OAAO;gBACT;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,UAAU;oBACV,aAAa;oBAEb;wEAAe,CAAA,OAAQ,CAAC;gCACtB,GAAG,IAAI;gCACP,SAAS,SAAS,IAAI,IAAI,EAAE;gCAC5B,cAAc,SAAS,IAAI,EAAE,UAAU;gCACvC,aAAa;gCACb,iBAAiB;gCACjB,aAAa;4BACf,CAAC;;gBACH,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B;oEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;4BAChD,aAAa;4BACb,SAAS,EAAE;4BACX,cAAc;wBAChB,CAAC;;YACH;QACF;uDAAG;QAAC;QAAY;KAAa,GAAG,0BAA0B;IAE1D,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC;qEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,iBAAiB,MAAM,MAAM,IAAI;wBACjC,aAAa,MAAM,MAAM,KAAK;oBAChC,CAAC;;YAED,aAAa;YACb,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,cAAc;YACd,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB;yEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,SAAS,EAAE;4BACX,cAAc;4BACd,iBAAiB;wBACnB,CAAC;;gBACD;YACF;YAEA,0BAA0B;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG;gBAC5B,iBAAiB,OAAO,GAAG;yEAAW;wBACpC,cAAc;oBAChB;wEAAG,MAAM,oBAAoB;YAC/B;QACF;4DAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YACA,cAAc;QAChB;4DAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YAC1C,MAAM,QAAQ,WAAW,IAAI,IAAI,WAAW,KAAK;YACjD;yEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAM,CAAC;;YAC1C,mBAAmB;QACrB;gEAAG;QAAC;KAAmB;IAEvB,OAAO;IACP,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACzC;yEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAM,CAAC;;QAC7D;gEAAG,EAAE;IAEL,OAAO;IACP,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC;qEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAM,CAAC;;QACzD;4DAAG,EAAE;IAEL,SAAS;IACT,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAM,CAAC;;YAC1C,mBAAmB;QACrB;6DAAG;QAAC;KAAmB;IAEvB,UAAU;IACV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAe,CAAA;oBACb,MAAM,WAAW;wBAAE,GAAG,IAAI;wBAAE;oBAAQ;oBAEpC,aAAa;oBACb,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI;wBACrB,4CAA4C;wBAC5C;kFAAW;gCACT,cAAc,KAAK,KAAK,EAAE;4BAC5B;iFAAG;oBACL;oBAEA,OAAO;gBACT;;QACF;6DAAG;QAAC;KAAc;IAElB,OAAO;IACP,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACpC;oEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,SAAS,EAAE;wBACX,cAAc;wBACd,iBAAiB;wBACjB,aAAa;wBACb,OAAO;oBACT,CAAC;;YACD,eAAe,OAAO,EAAE;QAC1B;2DAAG,EAAE;IAEL,UAAU;IACV,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAChC;gEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa,CAAC,KAAK,WAAW;oBAAC,CAAC;;QACrE;uDAAG,EAAE;IAEL,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa,eAAe,OAAO,EAAE;gBACvC,eAAe,OAAO,CAAC,KAAK;YAC9B;QACF;sCAAG;QAAC;KAAU;IAEd,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;+CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BAErD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,OAAO,YAAY,KAAK;gCACxB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,SAAS;wCACrB,mBAAmB,YAAY,KAAK;oCACtC;gCACF;gCACA,aAAa;gCACb,WAAU;;;;;;4BAGX,YAAY,KAAK,kBAChB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMlB,YAAY,eAAe,kBAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qKAAA,CAAA,UAAiB;4BAChB,OAAO,YAAY,KAAK;4BACxB,UAAU;4BACV,SAAS;;;;;;;;;;;oBAMd,YAAY,WAAW,IAAI,mCAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iKAAA,CAAA,UAAa;4BACZ,UAAU;4BACV,SAAS;4BACT,UAAU;4BACV,WAAW;;;;;;;;;;;;;;;;;YAOlB,qCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yKAAA,CAAA,UAAqB;oBACpB,SAAS,YAAY,OAAO;oBAC5B,iBAAiB;oBACjB,YAAY;oBACZ,eAAe;oBACf,aAAa,CAAC,YAAY,WAAW;oBACrC,kBAAkB;oBAClB,iBAAiB,YAAY,OAAO,CAAC,MAAM,GAAG;oBAC9C,aAAa,YAAY,YAAY;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,WAAW,kBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAKrC,YAAY,KAAK,kBAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB,YAAY,KAAK;;;;;;;;;;;oBAKjD,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,OAAO,CAAC,MAAM,GAAG,mBAC9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAsC;wCAC3C,YAAY,YAAY;wCAAC;;;;;;;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAChC,6LAAC,oKAAA,CAAA,UAAgB;wCAEf,MAAM;wCACN,OAAO,YAAY,KAAK;wCACxB,oBAAoB;wCACpB,YAAY,CAAC,IAAI;4CACf,eAAe;4CACf,QAAQ,GAAG,CAAC,aAAa,IAAI;wCAC/B;wCACA,SAAS,CAAC;4CACR,eAAe;4CACf,QAAQ,GAAG,CAAC,UAAU;wCACxB;uCAXK,OAAO,EAAE,IAAI;;;;;;;;;;;;;;;;oBAmB3B,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,IAAI,YAAY,OAAO,CAAC,MAAM,KAAK,mBACrG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAlWwB;;QAsClB,iJAAA,CAAA,mBAAgB;;;KAtCE", "debugId": null}}, {"offset": {"line": 3143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/test-search/page.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 智能搜索测试页面\n// 用于测试和演示智能搜索系统的所有功能\n\nimport React from 'react';\nimport IntelligentSearch from '@/components/knowledge/IntelligentSearch';\n\nexport default function TestSearchPage() {\n  // 模拟分类数据\n  const categories = [\n    { value: '1', label: 'MySQL基础', count: 25 },\n    { value: '2', label: '查询优化', count: 18 },\n    { value: '3', label: '索引设计', count: 22 },\n    { value: '4', label: '存储引擎', count: 15 },\n    { value: '5', label: '备份恢复', count: 12 },\n    { value: '6', label: '主从复制', count: 8 },\n    { value: '7', label: '性能监控', count: 10 }\n  ];\n\n  // 模拟标签数据\n  const availableTags = [\n    { value: 'select', label: 'SELECT查询', count: 45 },\n    { value: 'index', label: '索引优化', count: 38 },\n    { value: 'performance', label: '性能调优', count: 32 },\n    { value: 'innodb', label: 'InnoDB', count: 28 },\n    { value: 'backup', label: '数据备份', count: 20 },\n    { value: 'replication', label: '主从复制', count: 15 },\n    { value: 'transaction', label: '事务处理', count: 25 },\n    { value: 'lock', label: '锁机制', count: 18 },\n    { value: 'join', label: 'JOIN连接', count: 22 },\n    { value: 'trigger', label: '触发器', count: 12 },\n    { value: 'procedure', label: '存储过程', count: 14 },\n    { value: 'view', label: '视图', count: 16 },\n    { value: 'partition', label: '分区表', count: 8 },\n    { value: 'cluster', label: '集群', count: 6 }\n  ];\n\n  // 处理搜索结果选择\n  const handleResultSelect = (result: any) => {\n    console.log('选择了搜索结果:', result);\n    // 这里可以跳转到详情页面或执行其他操作\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 页面头部 */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              智能搜索系统测试\n            </h1>\n            <p className=\"text-gray-600\">\n              测试和演示MySQLAi.de的智能搜索功能\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 功能说明 */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8\">\n          <h2 className=\"text-lg font-semibold text-blue-900 mb-3\">\n            🚀 智能搜索功能特性\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-blue-800\">\n            <div>\n              <h3 className=\"font-medium mb-2\">🔍 实时搜索建议</h3>\n              <p>输入2个字符即可获得智能建议，支持文章标题和热门搜索词</p>\n            </div>\n            <div>\n              <h3 className=\"font-medium mb-2\">📊 智能排序算法</h3>\n              <p>基于TF-IDF的相关性计算，多维度权重排序</p>\n            </div>\n            <div>\n              <h3 className=\"font-medium mb-2\">🏷️ 高级筛选</h3>\n              <p>支持分类、难度、标签、排序等多维度筛选</p>\n            </div>\n            <div>\n              <h3 className=\"font-medium mb-2\">📝 搜索历史</h3>\n              <p>本地存储搜索历史，支持最近搜索和热门搜索</p>\n            </div>\n            <div>\n              <h3 className=\"font-medium mb-2\">⚡ 性能优化</h3>\n              <p>TTL缓存、防抖处理、请求取消等性能优化</p>\n            </div>\n            <div>\n              <h3 className=\"font-medium mb-2\">📱 响应式设计</h3>\n              <p>完美适配桌面端和移动端，触摸友好</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 智能搜索组件 */}\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <IntelligentSearch\n            placeholder=\"搜索MySQL知识点、教程、最佳实践...\"\n            autoFocus={true}\n            showAdvancedFilters={true}\n            showSearchHistory={true}\n            maxResults={20}\n            onResultSelect={handleResultSelect}\n            categories={categories}\n            availableTags={availableTags}\n            className=\"w-full\"\n          />\n        </div>\n\n        {/* 使用说明 */}\n        <div className=\"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            💡 使用说明\n          </h2>\n          <div className=\"space-y-3 text-sm text-gray-600\">\n            <div className=\"flex items-start gap-3\">\n              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\">1</span>\n              <p>在搜索框中输入关键词，系统会自动显示搜索建议和历史记录</p>\n            </div>\n            <div className=\"flex items-start gap-3\">\n              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\">2</span>\n              <p>点击\"高级筛选\"可以按分类、难度、标签等条件筛选结果</p>\n            </div>\n            <div className=\"flex items-start gap-3\">\n              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\">3</span>\n              <p>搜索历史会自动保存在本地，支持最近搜索和热门搜索两个标签页</p>\n            </div>\n            <div className=\"flex items-start gap-3\">\n              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\">4</span>\n              <p>搜索结果支持相关性排序、时间排序和标题排序</p>\n            </div>\n            <div className=\"flex items-start gap-3\">\n              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\">5</span>\n              <p>支持键盘导航：↑↓选择建议，回车确认，ESC关闭</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 技术特性 */}\n        <div className=\"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              🔧 技术实现\n            </h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li>• React 19 + TypeScript + Tailwind CSS</li>\n              <li>• 智能防抖和缓存机制</li>\n              <li>• TF-IDF相关性算法</li>\n              <li>• localStorage本地存储</li>\n              <li>• 响应式设计和无障碍支持</li>\n              <li>• 组件化架构，易于维护</li>\n            </ul>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              📈 性能优化\n            </h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li>• 搜索结果缓存（5分钟TTL）</li>\n              <li>• 搜索建议缓存（2分钟TTL）</li>\n              <li>• 300ms防抖延迟</li>\n              <li>• 请求取消和超时处理</li>\n              <li>• 虚拟滚动（大量结果时）</li>\n              <li>• 懒加载和预加载策略</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAMA;AANA;;;AAQe,SAAS;IACtB,SAAS;IACT,MAAM,aAAa;QACjB;YAAE,OAAO;YAAK,OAAO;YAAW,OAAO;QAAG;QAC1C;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAE;QACtC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;KACxC;IAED,SAAS;IACT,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAU,OAAO;YAAY,OAAO;QAAG;QAChD;YAAE,OAAO;YAAS,OAAO;YAAQ,OAAO;QAAG;QAC3C;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAU,OAAO;YAAU,OAAO;QAAG;QAC9C;YAAE,OAAO;YAAU,OAAO;YAAQ,OAAO;QAAG;QAC5C;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAQ,OAAO;YAAO,OAAO;QAAG;QACzC;YAAE,OAAO;YAAQ,OAAO;YAAU,OAAO;QAAG;QAC5C;YAAE,OAAO;YAAW,OAAO;YAAO,OAAO;QAAG;QAC5C;YAAE,OAAO;YAAa,OAAO;YAAQ,OAAO;QAAG;QAC/C;YAAE,OAAO;YAAQ,OAAO;YAAM,OAAO;QAAG;QACxC;YAAE,OAAO;YAAa,OAAO;YAAO,OAAO;QAAE;QAC7C;YAAE,OAAO;YAAW,OAAO;YAAM,OAAO;QAAE;KAC3C;IAED,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,YAAY;IACxB,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;0BAQnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qKAAA,CAAA,UAAiB;4BAChB,aAAY;4BACZ,WAAW;4BACX,qBAAqB;4BACrB,mBAAmB;4BACnB,YAAY;4BACZ,gBAAgB;4BAChB,YAAY;4BACZ,eAAe;4BACf,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;0DAClF,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;0DAClF,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;0DAClF,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;0DAClF,6LAAC;0DAAE;;;;;;;;;;;;kDAEL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAkE;;;;;;0DAClF,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;KApKwB", "debugId": null}}]}