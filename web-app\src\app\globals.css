@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #2d3748;

  /* MySQL Theme Colors */
  --mysql-primary: #00758f;
  --mysql-primary-dark: #003545;
  --mysql-primary-light: #e6f3f7;
  --mysql-accent: #0066cc;
  --mysql-text: #2d3748;
  --mysql-text-light: #718096;
  --mysql-border: #e2e8f0;
  --mysql-success: #38a169;
  --mysql-warning: #d69e2e;
  --mysql-error: #e53e3e;
  --mysql-info: #3182ce;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a202c;
    --foreground: #f7fafc;
    --mysql-text: #f7fafc;
    --mysql-text-light: #a0aec0;
    --mysql-border: #2d3748;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif;
  line-height: 1.6;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Hide scrollbar for all browsers while maintaining scroll functionality */

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  display: none;
  width: 0px;
  background: transparent;
}

/* Firefox */
html {
  scrollbar-width: none;
}

/* Internet Explorer and Edge Legacy */
body {
  -ms-overflow-style: none;
}

/* Ensure scrolling still works on all elements */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none; /* Webkit browsers */
}

/* ER图生成工具专用样式 - A/B区域左右分栏布局 */
.er-diagram-main-content {
  /* 左右分栏容器，占满可用高度 */
  height: calc(100vh - 12rem); /* 减去Header和面包屑的高度 */
  min-height: 600px; /* 最小高度确保可用性 */
}

/* A区域和B区域的基本样式 */
.er-diagram-main-content > div {
  /* 每个区域占满容器高度 */
  height: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .er-diagram-main-content {
    height: calc(100vh - 10rem);
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .er-diagram-main-content {
    /* 移动端改为上下布局 */
    flex-direction: column;
    height: auto;
    min-height: 80vh;
  }

  .er-diagram-main-content > div {
    height: 50vh; /* 移动端每个区域占一半高度 */
    min-height: 400px;
  }
}

/* SQL编辑器内联样式 */
.sql-editor-pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.sql-editor-textarea {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* SQL编辑器行号样式 */
.sql-editor-line-number {
  height: 24px;
  line-height: 24px;
}

/* SQL编辑器文本区域样式 */
.sql-editor-textarea-fixed {
  line-height: 24px !important;
}

.sql-editor-pre-fixed {
  line-height: 24px !important;
}

/* 工具页面特殊样式 - 隐藏Footer */
body:has(.tools-page-container) footer {
  display: none !important;
}

/* 备用选择器，确保Footer在工具页面完全隐藏 */
.tools-page-container ~ footer,
main:has(.tools-page-container) ~ footer {
  display: none !important;
}

/* 工具页面样式优化 - 自然布局 */
body:has(.tools-page-container) {
  min-height: 100vh;
}

/* 工具页面的main元素自然布局 */
body:has(.tools-page-container) main {
  min-height: calc(100vh - 4rem); /* Header高度 */
  padding-top: 4rem; /* Header高度 */
}

@media (min-width: 1024px) {
  body:has(.tools-page-container) main {
    min-height: calc(100vh - 5rem);
    padding-top: 5rem; /* 大屏幕Header高度 */
  }
}
