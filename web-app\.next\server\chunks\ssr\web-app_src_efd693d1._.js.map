{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/contexts/NavigationContext.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - NavigationContext导航状态管理\n// 管理知识库导航的全局状态，包含当前选中项、搜索状态、展开状态等\n\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';\nimport { KnowledgeCategory, KnowledgeItem } from '@/lib/types';\n\n// 导航状态接口\nexport interface NavigationState {\n  // 侧边栏状态\n  isSidebarOpen: boolean;\n  \n  // 当前选中状态\n  currentCategory: string | null;\n  currentItem: string | null;\n  \n  // 搜索状态\n  searchQuery: string;\n  isSearching: boolean;\n  \n  // 展开状态\n  expandedCategories: Set<string>;\n  \n  // 视图模式\n  viewMode: 'grid' | 'list';\n}\n\n// 导航操作接口\nexport interface NavigationActions {\n  // 侧边栏控制\n  toggleSidebar: () => void;\n  openSidebar: () => void;\n  closeSidebar: () => void;\n  \n  // 选中项控制\n  setCurrentCategory: (categoryId: string | null) => void;\n  setCurrentItem: (itemId: string | null) => void;\n  \n  // 搜索控制\n  setSearchQuery: (query: string) => void;\n  setIsSearching: (searching: boolean) => void;\n  clearSearch: () => void;\n  \n  // 展开状态控制\n  toggleCategory: (categoryId: string) => void;\n  expandCategory: (categoryId: string) => void;\n  collapseCategory: (categoryId: string) => void;\n  expandAll: () => void;\n  collapseAll: () => void;\n  \n  // 视图模式控制\n  setViewMode: (mode: 'grid' | 'list') => void;\n  \n  // 重置状态\n  resetNavigation: () => void;\n}\n\n// Context类型\nexport interface NavigationContextType extends NavigationState, NavigationActions {}\n\n// 初始状态\nconst initialState: NavigationState = {\n  isSidebarOpen: false,\n  currentCategory: null,\n  currentItem: null,\n  searchQuery: '',\n  isSearching: false,\n  expandedCategories: new Set(),\n  viewMode: 'grid',\n};\n\n// 创建Context\nconst NavigationContext = createContext<NavigationContextType | undefined>(undefined);\n\n// Provider组件Props\ninterface NavigationProviderProps {\n  children: ReactNode;\n  initialCategory?: string;\n  initialItem?: string;\n}\n\n// Provider组件\nexport function NavigationProvider({ \n  children, \n  initialCategory, \n  initialItem \n}: NavigationProviderProps) {\n  const [state, setState] = useState<NavigationState>({\n    ...initialState,\n    currentCategory: initialCategory || null,\n    currentItem: initialItem || null,\n    expandedCategories: initialCategory ? new Set([initialCategory]) : new Set(),\n  });\n\n  // 侧边栏控制\n  const toggleSidebar = useCallback(() => {\n    setState(prev => ({ ...prev, isSidebarOpen: !prev.isSidebarOpen }));\n  }, []);\n\n  const openSidebar = useCallback(() => {\n    setState(prev => ({ ...prev, isSidebarOpen: true }));\n  }, []);\n\n  const closeSidebar = useCallback(() => {\n    setState(prev => ({ ...prev, isSidebarOpen: false }));\n  }, []);\n\n  // 选中项控制\n  const setCurrentCategory = useCallback((categoryId: string | null) => {\n    setState(prev => ({ \n      ...prev, \n      currentCategory: categoryId,\n      currentItem: null // 切换分类时清空当前知识点\n    }));\n  }, []);\n\n  const setCurrentItem = useCallback((itemId: string | null) => {\n    setState(prev => ({ ...prev, currentItem: itemId }));\n  }, []);\n\n  // 搜索控制\n  const setSearchQuery = useCallback((query: string) => {\n    setState(prev => ({ ...prev, searchQuery: query }));\n  }, []);\n\n  const setIsSearching = useCallback((searching: boolean) => {\n    setState(prev => ({ ...prev, isSearching: searching }));\n  }, []);\n\n  const clearSearch = useCallback(() => {\n    setState(prev => ({ \n      ...prev, \n      searchQuery: '', \n      isSearching: false \n    }));\n  }, []);\n\n  // 展开状态控制\n  const toggleCategory = useCallback((categoryId: string) => {\n    setState(prev => {\n      const newExpanded = new Set(prev.expandedCategories);\n      if (newExpanded.has(categoryId)) {\n        newExpanded.delete(categoryId);\n      } else {\n        newExpanded.add(categoryId);\n      }\n      return { ...prev, expandedCategories: newExpanded };\n    });\n  }, []);\n\n  const expandCategory = useCallback((categoryId: string) => {\n    setState(prev => {\n      const newExpanded = new Set(prev.expandedCategories);\n      newExpanded.add(categoryId);\n      return { ...prev, expandedCategories: newExpanded };\n    });\n  }, []);\n\n  const collapseCategory = useCallback((categoryId: string) => {\n    setState(prev => {\n      const newExpanded = new Set(prev.expandedCategories);\n      newExpanded.delete(categoryId);\n      return { ...prev, expandedCategories: newExpanded };\n    });\n  }, []);\n\n  const expandAll = useCallback(() => {\n    // 这里需要获取所有分类ID，暂时使用空Set\n    // 在实际使用时会传入所有分类ID\n    setState(prev => ({ ...prev, expandedCategories: new Set() }));\n  }, []);\n\n  const collapseAll = useCallback(() => {\n    setState(prev => ({ ...prev, expandedCategories: new Set() }));\n  }, []);\n\n  // 视图模式控制\n  const setViewMode = useCallback((mode: 'grid' | 'list') => {\n    setState(prev => ({ ...prev, viewMode: mode }));\n  }, []);\n\n  // 重置状态\n  const resetNavigation = useCallback(() => {\n    setState(initialState);\n  }, []);\n\n  // Context值\n  const contextValue: NavigationContextType = {\n    // 状态\n    ...state,\n    \n    // 操作\n    toggleSidebar,\n    openSidebar,\n    closeSidebar,\n    setCurrentCategory,\n    setCurrentItem,\n    setSearchQuery,\n    setIsSearching,\n    clearSearch,\n    toggleCategory,\n    expandCategory,\n    collapseCategory,\n    expandAll,\n    collapseAll,\n    setViewMode,\n    resetNavigation,\n  };\n\n  return (\n    <NavigationContext.Provider value={contextValue}>\n      {children}\n    </NavigationContext.Provider>\n  );\n}\n\n// Hook for using navigation context\nexport function useNavigation(): NavigationContextType {\n  const context = useContext(NavigationContext);\n  if (context === undefined) {\n    throw new Error('useNavigation must be used within a NavigationProvider');\n  }\n  return context;\n}\n\n// 导出Context以供其他组件使用\nexport { NavigationContext };\n"], "names": [], "mappings": ";;;;;;AAEA,uCAAuC;AACvC,kCAAkC;AAElC;AALA;;;AA6DA,OAAO;AACP,MAAM,eAAgC;IACpC,eAAe;IACf,iBAAiB;IACjB,aAAa;IACb,aAAa;IACb,aAAa;IACb,oBAAoB,IAAI;IACxB,UAAU;AACZ;AAEA,YAAY;AACZ,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAUpE,SAAS,mBAAmB,EACjC,QAAQ,EACR,eAAe,EACf,WAAW,EACa;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,GAAG,YAAY;QACf,iBAAiB,mBAAmB;QACpC,aAAa,eAAe;QAC5B,oBAAoB,kBAAkB,IAAI,IAAI;YAAC;SAAgB,IAAI,IAAI;IACzE;IAEA,QAAQ;IACR,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe,CAAC,KAAK,aAAa;YAAC,CAAC;IACnE,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe;YAAK,CAAC;IACpD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe;YAAM,CAAC;IACrD,GAAG,EAAE;IAEL,QAAQ;IACR,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;gBACjB,aAAa,KAAK,eAAe;YACnC,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAO,CAAC;IACpD,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAM,CAAC;IACnD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAU,CAAC;IACvD,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;gBACb,aAAa;YACf,CAAC;IACH,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS,CAAA;YACP,MAAM,cAAc,IAAI,IAAI,KAAK,kBAAkB;YACnD,IAAI,YAAY,GAAG,CAAC,aAAa;gBAC/B,YAAY,MAAM,CAAC;YACrB,OAAO;gBACL,YAAY,GAAG,CAAC;YAClB;YACA,OAAO;gBAAE,GAAG,IAAI;gBAAE,oBAAoB;YAAY;QACpD;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS,CAAA;YACP,MAAM,cAAc,IAAI,IAAI,KAAK,kBAAkB;YACnD,YAAY,GAAG,CAAC;YAChB,OAAO;gBAAE,GAAG,IAAI;gBAAE,oBAAoB;YAAY;QACpD;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,SAAS,CAAA;YACP,MAAM,cAAc,IAAI,IAAI,KAAK,kBAAkB;YACnD,YAAY,MAAM,CAAC;YACnB,OAAO;gBAAE,GAAG,IAAI;gBAAE,oBAAoB;YAAY;QACpD;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,wBAAwB;QACxB,kBAAkB;QAClB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,oBAAoB,IAAI;YAAM,CAAC;IAC9D,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,oBAAoB,IAAI;YAAM,CAAC;IAC9D,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAK,CAAC;IAC/C,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,SAAS;IACX,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,eAAsC;QAC1C,KAAK;QACL,GAAG,KAAK;QAER,KAAK;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/api/knowledge.ts"], "sourcesContent": ["// MySQLAi.de - 知识库 API 客户端\n// 提供类型安全的 API 调用方法\n\nimport type { Database } from '@/lib/database.types';\n\ntype KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];\ntype KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];\ntype CodeExample = Database['public']['Tables']['code_examples']['Row'];\n\n// API 响应类型\ninterface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n  details?: string;\n}\n\ninterface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\ninterface SearchResponse extends PaginatedResponse<KnowledgeArticle> {\n  query?: {\n    text: string;\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    sortBy?: string;\n    sortOrder?: string;\n  };\n}\n\n// API缓存管理\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiry: number;\n}\n\nclass APICache {\n  private cache = new Map<string, CacheItem<any>>();\n  private readonly defaultTTL = 5 * 60 * 1000; // 5分钟默认缓存时间\n\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    if (!item) return null;\n\n    if (Date.now() > item.expiry) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return item.data;\n  }\n\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    this.cache.set(key, {\n      data,\n      timestamp: now,\n      expiry: now + ttl\n    });\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n\n  generateKey(endpoint: string, options?: RequestInit): string {\n    const method = options?.method || 'GET';\n    const body = options?.body || '';\n    return `${method}:${endpoint}:${btoa(body).slice(0, 20)}`;\n  }\n}\n\n// 全局API缓存实例\nconst apiCache = new APICache();\n\n// 防抖管理\nconst debounceMap = new Map<string, NodeJS.Timeout>();\n\nfunction debounce<T extends (...args: any[]) => any>(\n  func: T,\n  delay: number,\n  key: string\n): (...args: Parameters<T>) => Promise<ReturnType<T>> {\n  return (...args: Parameters<T>): Promise<ReturnType<T>> => {\n    return new Promise((resolve, reject) => {\n      // 清除之前的定时器\n      if (debounceMap.has(key)) {\n        clearTimeout(debounceMap.get(key)!);\n      }\n\n      // 设置新的定时器\n      const timeoutId = setTimeout(async () => {\n        try {\n          const result = await func(...args);\n          resolve(result);\n        } catch (error) {\n          reject(error);\n        } finally {\n          debounceMap.delete(key);\n        }\n      }, delay);\n\n      debounceMap.set(key, timeoutId);\n    });\n  };\n}\n\n// 基础 API 调用函数\nasync function apiCall<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {\n  try {\n    // 构建正确的API URL\n    let apiUrl: string;\n\n    if (typeof window !== 'undefined') {\n      // 客户端：使用绝对URL\n      apiUrl = `${window.location.origin}/api/knowledge${endpoint}`;\n    } else {\n      // 服务端：构建完整的URL\n      const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';\n      apiUrl = `${baseUrl}/api/knowledge${endpoint}`;\n    }\n\n    // 检查缓存（仅对GET请求）\n    const method = options?.method || 'GET';\n    if (method === 'GET') {\n      const cacheKey = apiCache.generateKey(endpoint, options);\n      const cachedData = apiCache.get<ApiResponse<T>>(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    // 创建带超时的fetch请求\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时\n\n    try {\n      const response = await fetch(apiUrl, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        signal: controller.signal,\n        ...options,\n      });\n\n      clearTimeout(timeoutId);\n\n      // 检查响应状态\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      // 检查响应内容类型\n      const contentType = response.headers.get('content-type');\n      if (!contentType || !contentType.includes('application/json')) {\n        throw new Error('响应不是有效的JSON格式');\n      }\n\n      const data = await response.json();\n\n      // 缓存成功的GET请求结果\n      if (method === 'GET' && data.success) {\n        const cacheKey = apiCache.generateKey(endpoint, options);\n        // 搜索结果缓存时间较短，其他数据缓存时间较长\n        const ttl = endpoint.includes('/search') ? 2 * 60 * 1000 : 5 * 60 * 1000;\n        apiCache.set(cacheKey, data, ttl);\n      }\n\n      return data;\n    } catch (fetchError) {\n      clearTimeout(timeoutId);\n      throw fetchError;\n    }\n  } catch (error) {\n    console.error('API调用失败:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '网络错误'\n    };\n  }\n}\n\n// 知识库分类 API\nexport const categoriesApi = {\n  // 获取所有分类\n  getAll: async (includeStats = false): Promise<ApiResponse<KnowledgeCategory[]>> => {\n    const params = includeStats ? '?includeStats=true' : '';\n    return apiCall<KnowledgeCategory[]>(`/categories${params}`);\n  },\n\n  // 获取单个分类\n  getById: async (id: string, includeArticles = false): Promise<ApiResponse<KnowledgeCategory>> => {\n    const params = includeArticles ? '?includeArticles=true' : '';\n    return apiCall<KnowledgeCategory>(`/categories/${id}${params}`);\n  },\n\n  // 创建分类\n  create: async (category: Omit<KnowledgeCategory, 'created_at'>): Promise<ApiResponse<KnowledgeCategory>> => {\n    return apiCall<KnowledgeCategory>('/categories', {\n      method: 'POST',\n      body: JSON.stringify(category),\n    });\n  },\n\n  // 更新分类\n  update: async (id: string, updates: Partial<KnowledgeCategory>): Promise<ApiResponse<KnowledgeCategory>> => {\n    return apiCall<KnowledgeCategory>(`/categories/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除分类\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/categories/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 批量更新排序\n  updateOrder: async (categories: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {\n    return apiCall<void>('/categories', {\n      method: 'PUT',\n      body: JSON.stringify({ categories }),\n    });\n  },\n};\n\n// 知识库文章 API\nexport const articlesApi = {\n  // 获取文章列表\n  getAll: async (params?: {\n    category?: string;\n    search?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n    includeCodeExamples?: boolean;\n    includeRelated?: boolean;\n  }): Promise<PaginatedResponse<KnowledgeArticle>> => {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.category) searchParams.set('category', params.category);\n    if (params?.search) searchParams.set('search', params.search);\n    if (params?.tags) searchParams.set('tags', params.tags);\n    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.includeCodeExamples) searchParams.set('includeCodeExamples', 'true');\n    if (params?.includeRelated) searchParams.set('includeRelated', 'true');\n\n    const query = searchParams.toString();\n    return apiCall<KnowledgeArticle[]>(`/articles${query ? `?${query}` : ''}`);\n  },\n\n  // 获取单个文章\n  getById: async (id: string, options?: {\n    includeCodeExamples?: boolean;\n    includeRelated?: boolean;\n  }): Promise<ApiResponse<KnowledgeArticle>> => {\n    const params = new URLSearchParams();\n    if (options?.includeCodeExamples === false) params.set('includeCodeExamples', 'false');\n    if (options?.includeRelated === false) params.set('includeRelated', 'false');\n    \n    const query = params.toString();\n    return apiCall<KnowledgeArticle>(`/articles/${id}${query ? `?${query}` : ''}`);\n  },\n\n  // 创建文章\n  create: async (article: Omit<KnowledgeArticle, 'created_at' | 'updated_at' | 'last_updated'>): Promise<ApiResponse<KnowledgeArticle>> => {\n    return apiCall<KnowledgeArticle>('/articles', {\n      method: 'POST',\n      body: JSON.stringify(article),\n    });\n  },\n\n  // 更新文章\n  update: async (id: string, updates: Partial<KnowledgeArticle>): Promise<ApiResponse<KnowledgeArticle>> => {\n    return apiCall<KnowledgeArticle>(`/articles/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除文章\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/articles/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 搜索文章（带防抖）\n  search: debounce(async (query: string, params?: {\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n  }): Promise<SearchResponse> => {\n    const searchParams = new URLSearchParams();\n    searchParams.set('search', query);\n\n    if (params?.category) searchParams.set('category', params.category);\n    if (params?.tags) searchParams.set('tags', params.tags);\n    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n\n    return apiCall<KnowledgeArticle[]>(`/articles?${searchParams.toString()}`);\n  }, 300, 'articles-search'),\n};\n\n// 代码示例 API\nexport const codeExamplesApi = {\n  // 获取代码示例列表\n  getAll: async (params?: {\n    articleId?: string;\n    language?: string;\n    search?: string;\n    page?: number;\n    limit?: number;\n  }): Promise<PaginatedResponse<CodeExample>> => {\n    const searchParams = new URLSearchParams();\n\n    if (params?.articleId) searchParams.set('articleId', params.articleId);\n    if (params?.language) searchParams.set('language', params.language);\n    if (params?.search) searchParams.set('search', params.search);\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n\n    const query = searchParams.toString();\n    return apiCall<CodeExample[]>(`/code-examples${query ? `?${query}` : ''}`);\n  },\n\n  // 创建代码示例\n  create: async (example: Omit<CodeExample, 'created_at'>): Promise<ApiResponse<CodeExample>> => {\n    return apiCall<CodeExample>('/code-examples', {\n      method: 'POST',\n      body: JSON.stringify(example),\n    });\n  },\n\n  // 更新代码示例\n  update: async (id: string, updates: Partial<CodeExample>): Promise<ApiResponse<CodeExample>> => {\n    return apiCall<CodeExample>(`/code-examples/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(updates),\n    });\n  },\n\n  // 删除代码示例\n  delete: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`/code-examples/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 批量更新排序\n  updateOrder: async (examples: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {\n    return apiCall<void>('/code-examples', {\n      method: 'PUT',\n      body: JSON.stringify({ examples }),\n    });\n  },\n};\n\n// 搜索 API\nexport const searchApi = {\n  // 搜索文章（带防抖和缓存）\n  search: debounce(async (params: {\n    query: string;\n    category?: string;\n    tags?: string;\n    difficulty?: string;\n    page?: number;\n    limit?: number;\n    sortBy?: string;\n    sortOrder?: string;\n  }): Promise<SearchResponse> => {\n    const searchParams = new URLSearchParams();\n\n    searchParams.set('q', params.query);\n    if (params.category) searchParams.set('category', params.category);\n    if (params.tags) searchParams.set('tags', params.tags);\n    if (params.difficulty) searchParams.set('difficulty', params.difficulty);\n    if (params.page) searchParams.set('page', params.page.toString());\n    if (params.limit) searchParams.set('limit', params.limit.toString());\n    if (params.sortBy) searchParams.set('sortBy', params.sortBy);\n    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);\n\n    return apiCall<KnowledgeArticle[]>(`/search?${searchParams.toString()}`);\n  }, 300, 'search-main'),\n\n  // 获取搜索建议（带防抖）\n  getSuggestions: debounce(async (query: string, limit = 5): Promise<ApiResponse<Array<{\n    type: 'article' | 'query';\n    text: string;\n    id?: string;\n    category?: string;\n  }>>> => {\n    // 查询长度小于2时直接返回空结果，避免无意义的API调用\n    if (!query || query.trim().length < 2) {\n      return {\n        success: true,\n        data: []\n      };\n    }\n\n    return apiCall('/search', {\n      method: 'POST',\n      body: JSON.stringify({ query: query.trim(), limit }),\n    });\n  }, 200, 'search-suggestions'),\n\n  // 清除搜索缓存\n  clearCache: (): void => {\n    apiCache.clear();\n  },\n\n  // 取消所有防抖请求\n  cancelPendingRequests: (): void => {\n    debounceMap.forEach((timeoutId) => {\n      clearTimeout(timeoutId);\n    });\n    debounceMap.clear();\n  }\n};\n\n// 统计 API\nexport const statsApi = {\n  // 获取统计数据\n  get: async (params?: {\n    period?: string;\n    includeSearchStats?: boolean;\n  }): Promise<ApiResponse<{\n    overview: {\n      totalCategories: number;\n      totalArticles: number;\n      totalCodeExamples: number;\n      totalRelations: number;\n    };\n    categoryStats: Array<{\n      id: string;\n      name: string;\n      articleCount: number;\n    }>;\n    difficultyStats: Record<string, number>;\n    languageStats: Record<string, number>;\n    recentArticles: Array<{\n      id: string;\n      title: string;\n      last_updated: string;\n      knowledge_categories: { name: string };\n    }>;\n    searchStats?: {\n      totalSearches: number;\n      popularQueries: Array<{ query: string; count: number }>;\n      searchTrends: Record<string, number>;\n    };\n  }>> => {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.period) searchParams.set('period', params.period);\n    if (params?.includeSearchStats) searchParams.set('includeSearchStats', 'true');\n\n    const query = searchParams.toString();\n    return apiCall(`/stats${query ? `?${query}` : ''}`);\n  },\n\n  // 导出统计数据\n  export: async (format = 'json', includeDetails = false): Promise<ApiResponse<any>> => {\n    return apiCall('/stats/export', {\n      method: 'POST',\n      body: JSON.stringify({ format, includeDetails }),\n    });\n  },\n};\n"], "names": [], "mappings": "AAAA,2BAA2B;AAC3B,mBAAmB;;;;;;;;AA4CnB,MAAM;IACI,QAAQ,IAAI,MAA8B;IACjC,aAAa,IAAI,KAAK,KAAK;IAE5C,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW;YACX,QAAQ,MAAM;QAChB;IACF;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,YAAY,QAAgB,EAAE,OAAqB,EAAU;QAC3D,MAAM,SAAS,SAAS,UAAU;QAClC,MAAM,OAAO,SAAS,QAAQ;QAC9B,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK;IAC3D;AACF;AAEA,YAAY;AACZ,MAAM,WAAW,IAAI;AAErB,OAAO;AACP,MAAM,cAAc,IAAI;AAExB,SAAS,SACP,IAAO,EACP,KAAa,EACb,GAAW;IAEX,OAAO,CAAC,GAAG;QACT,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,WAAW;YACX,IAAI,YAAY,GAAG,CAAC,MAAM;gBACxB,aAAa,YAAY,GAAG,CAAC;YAC/B;YAEA,UAAU;YACV,MAAM,YAAY,WAAW;gBAC3B,IAAI;oBACF,MAAM,SAAS,MAAM,QAAQ;oBAC7B,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT,SAAU;oBACR,YAAY,MAAM,CAAC;gBACrB;YACF,GAAG;YAEH,YAAY,GAAG,CAAC,KAAK;QACvB;IACF;AACF;AAEA,cAAc;AACd,eAAe,QAAW,QAAgB,EAAE,OAAqB;IAC/D,IAAI;QACF,eAAe;QACf,IAAI;QAEJ,uCAAmC;;QAGnC,OAAO;YACL,eAAe;YACf,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI;YACtE,SAAS,GAAG,QAAQ,cAAc,EAAE,UAAU;QAChD;QAEA,gBAAgB;QAChB,MAAM,SAAS,SAAS,UAAU;QAClC,IAAI,WAAW,OAAO;YACpB,MAAM,WAAW,SAAS,WAAW,CAAC,UAAU;YAChD,MAAM,aAAa,SAAS,GAAG,CAAiB;YAChD,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,QAAQ;QAEvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,QAAQ,WAAW,MAAM;gBACzB,GAAG,OAAO;YACZ;YAEA,aAAa;YAEb,SAAS;YACT,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,WAAW;YACX,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,qBAAqB;gBAC7D,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,eAAe;YACf,IAAI,WAAW,SAAS,KAAK,OAAO,EAAE;gBACpC,MAAM,WAAW,SAAS,WAAW,CAAC,UAAU;gBAChD,wBAAwB;gBACxB,MAAM,MAAM,SAAS,QAAQ,CAAC,aAAa,IAAI,KAAK,OAAO,IAAI,KAAK;gBACpE,SAAS,GAAG,CAAC,UAAU,MAAM;YAC/B;YAEA,OAAO;QACT,EAAE,OAAO,YAAY;YACnB,aAAa;YACb,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,QAAQ,OAAO,eAAe,KAAK;QACjC,MAAM,SAAS,eAAe,uBAAuB;QACrD,OAAO,QAA6B,CAAC,WAAW,EAAE,QAAQ;IAC5D;IAEA,SAAS;IACT,SAAS,OAAO,IAAY,kBAAkB,KAAK;QACjD,MAAM,SAAS,kBAAkB,0BAA0B;QAC3D,OAAO,QAA2B,CAAC,YAAY,EAAE,KAAK,QAAQ;IAChE;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAA2B,eAAe;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,OAAO,QAA2B,CAAC,YAAY,EAAE,IAAI,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,YAAY,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,OAAO,QAAc,eAAe;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;IACF;AACF;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,QAAQ,OAAO;QAUb,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACtD,IAAI,QAAQ,YAAY,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACxE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,qBAAqB,aAAa,GAAG,CAAC,uBAAuB;QACzE,IAAI,QAAQ,gBAAgB,aAAa,GAAG,CAAC,kBAAkB;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAA4B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC3E;IAEA,SAAS;IACT,SAAS,OAAO,IAAY;QAI1B,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,wBAAwB,OAAO,OAAO,GAAG,CAAC,uBAAuB;QAC9E,IAAI,SAAS,mBAAmB,OAAO,OAAO,GAAG,CAAC,kBAAkB;QAEpE,MAAM,QAAQ,OAAO,QAAQ;QAC7B,OAAO,QAA0B,CAAC,UAAU,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC/E;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAA0B,aAAa;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO,IAAY;QACzB,OAAO,QAA0B,CAAC,UAAU,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,UAAU,EAAE,IAAI,EAAE;YACtC,QAAQ;QACV;IACF;IAEA,YAAY;IACZ,QAAQ,SAAS,OAAO,OAAe;QAOrC,MAAM,eAAe,IAAI;QACzB,aAAa,GAAG,CAAC,UAAU;QAE3B,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACtD,IAAI,QAAQ,YAAY,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACxE,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAElE,OAAO,QAA4B,CAAC,UAAU,EAAE,aAAa,QAAQ,IAAI;IAC3E,GAAG,KAAK;AACV;AAGO,MAAM,kBAAkB;IAC7B,WAAW;IACX,QAAQ,OAAO;QAOb,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAAuB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC3E;IAEA,SAAS;IACT,QAAQ,OAAO;QACb,OAAO,QAAqB,kBAAkB;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,SAAS;IACT,QAAQ,OAAO,IAAY;QACzB,OAAO,QAAqB,CAAC,eAAe,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,SAAS;IACT,QAAQ,OAAO;QACb,OAAO,QAAc,CAAC,eAAe,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,OAAO,QAAc,kBAAkB;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;IACF;AACF;AAGO,MAAM,YAAY;IACvB,eAAe;IACf,QAAQ,SAAS,OAAO;QAUtB,MAAM,eAAe,IAAI;QAEzB,aAAa,GAAG,CAAC,KAAK,OAAO,KAAK;QAClC,IAAI,OAAO,QAAQ,EAAE,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QACjE,IAAI,OAAO,IAAI,EAAE,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QACrD,IAAI,OAAO,UAAU,EAAE,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;QACvE,IAAI,OAAO,IAAI,EAAE,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC9D,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC3D,IAAI,OAAO,SAAS,EAAE,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QAEpE,OAAO,QAA4B,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI;IACzE,GAAG,KAAK;IAER,cAAc;IACd,gBAAgB,SAAS,OAAO,OAAe,QAAQ,CAAC;QAMtD,8BAA8B;QAC9B,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;YACV;QACF;QAEA,OAAO,QAAQ,WAAW;YACxB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,OAAO,MAAM,IAAI;gBAAI;YAAM;QACpD;IACF,GAAG,KAAK;IAER,SAAS;IACT,YAAY;QACV,SAAS,KAAK;IAChB;IAEA,WAAW;IACX,uBAAuB;QACrB,YAAY,OAAO,CAAC,CAAC;YACnB,aAAa;QACf;QACA,YAAY,KAAK;IACnB;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;IACT,KAAK,OAAO;QA6BV,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,oBAAoB,aAAa,GAAG,CAAC,sBAAsB;QAEvE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACpD;IAEA,SAAS;IACT,QAAQ,OAAO,SAAS,MAAM,EAAE,iBAAiB,KAAK;QACpD,OAAO,QAAQ,iBAAiB;YAC9B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ;YAAe;QAChD;IACF;AACF", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/knowledge.ts"], "sourcesContent": ["// MySQLAi.de - 知识库数据管理\n// 提供知识库相关的数据结构、类型定义和数据访问函数\n// 所有数据完全来自数据库，不再使用任何硬编码数据\n\nimport { KnowledgeCategory, KnowledgeItem } from '@/lib/types';\nimport { articlesApi, categoriesApi } from '@/lib/api/knowledge';\nimport { mapDatabaseArticlesToFrontend } from '@/lib/utils';\n\n// 数据访问函数\n\n/**\n * 获取热门知识点（基于相关项目数量）\n * 完全使用API调用获取真实数据，不再使用硬编码降级\n */\nexport async function getPopularKnowledgeItems(maxItems: number = 6): Promise<KnowledgeItem[]> {\n  try {\n    const response = await articlesApi.getAll({\n      includeCodeExamples: true,\n      includeRelated: true\n    });\n\n    if (response.success && response.data) {\n      return response.data\n        .sort((a, b) => a.order_index - b.order_index)\n        .slice(0, maxItems);\n    }\n  } catch (error) {\n    console.error('获取热门知识点失败:', error);\n  }\n\n  // 如果API调用失败，返回空数组（不再使用硬编码降级）\n  return [];\n}\n\n/**\n * 获取最近更新的知识点\n * 完全使用API调用获取真实数据，不再使用硬编码降级\n */\nexport async function getRecentKnowledgeItems(maxItems: number = 6): Promise<KnowledgeItem[]> {\n  try {\n    const response = await articlesApi.getAll({\n      includeCodeExamples: true,\n      includeRelated: true\n    });\n\n    if (response.success && response.data) {\n      return response.data\n        .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())\n        .slice(0, maxItems);\n    }\n  } catch (error) {\n    console.error('获取最近更新知识点失败:', error);\n  }\n\n  // 如果API调用失败，返回空数组\n  return [];\n}\n\n/**\n * 获取知识库分类\n * 完全使用API调用获取真实数据，不再使用硬编码降级\n */\nexport async function getKnowledgeCategories(): Promise<KnowledgeCategory[]> {\n  try {\n    const response = await categoriesApi.getAll(true);\n\n    if (response.success && response.data) {\n      return response.data\n        .sort((a, b) => a.order_index - b.order_index);\n    }\n  } catch (error) {\n    console.error('获取知识分类失败:', error);\n  }\n\n  // 如果API调用失败，返回空数组（不再使用硬编码降级）\n  return [];\n}\n\n/**\n * 根据ID获取知识点\n * 完全使用API调用获取真实数据\n */\nexport async function getKnowledgeItem(itemId: string): Promise<KnowledgeItem | null> {\n  try {\n    const response = await articlesApi.getById(itemId, {\n      includeCodeExamples: true,\n      includeRelated: true\n    });\n\n    if (response.success && response.data) {\n      const frontendArticles = mapDatabaseArticlesToFrontend([response.data]);\n      return frontendArticles[0] || null;\n    }\n  } catch (error) {\n    console.error('获取知识点失败:', error);\n  }\n\n  return null;\n}\n\n/**\n * 根据分类ID获取知识点列表\n * 完全使用API调用获取真实数据\n */\nexport async function getKnowledgeItemsByCategory(categoryId: string): Promise<KnowledgeItem[]> {\n  try {\n    const response = await articlesApi.getAll({\n      category: categoryId,\n      includeCodeExamples: true,\n      includeRelated: true\n    });\n\n    if (response.success && response.data) {\n      return response.data.sort((a, b) => a.order_index - b.order_index);\n    }\n  } catch (error) {\n    console.error('获取分类知识点失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 搜索知识点\n * 完全使用API调用获取真实数据\n */\nexport async function searchKnowledgeItems(query: string): Promise<KnowledgeItem[]> {\n  try {\n    const response = await articlesApi.search(query);\n\n    if (response.success && response.data) {\n      return response.data;\n    }\n  } catch (error) {\n    console.error('搜索知识点失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 获取相关知识点\n * 基于当前知识点的标签和分类获取相关内容\n */\nexport async function getRelatedKnowledgeItems(currentItem: KnowledgeItem, maxItems: number = 5): Promise<KnowledgeItem[]> {\n  try {\n    // 基于分类获取相关文章\n    const response = await articlesApi.getAll({\n      category: currentItem.category_id || undefined,\n      includeCodeExamples: true,\n      includeRelated: true\n    });\n\n    if (response.success && response.data) {\n      return response.data\n        .filter(item => item.id !== currentItem.id) // 排除当前文章\n        .slice(0, maxItems);\n    }\n  } catch (error) {\n    console.error('获取相关知识点失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 获取所有标签\n * 从API获取所有文章并提取标签\n */\nexport async function getAllTags(): Promise<string[]> {\n  try {\n    const response = await articlesApi.getAll();\n\n    if (response.success && response.data) {\n      const allTags = response.data.flatMap(item => item.tags || []);\n      return Array.from(new Set(allTags)).sort();\n    }\n  } catch (error) {\n    console.error('获取标签失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 获取所有知识点\n * 完全使用API调用获取真实数据\n */\nexport async function getKnowledgeItems(): Promise<KnowledgeItem[]> {\n  try {\n    const response = await articlesApi.getAll();\n\n    if (response.success && response.data) {\n      return response.data.sort((a, b) => a.order_index - b.order_index);\n    }\n  } catch (error) {\n    console.error('获取知识点失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 根据标签获取知识点\n * 完全使用API调用获取真实数据\n */\nexport async function getKnowledgeItemsByTag(tag: string): Promise<KnowledgeItem[]> {\n  try {\n    // 使用搜索API查找包含特定标签的文章\n    const response = await articlesApi.search(tag);\n\n    if (response.success && response.data) {\n      // 过滤出真正包含该标签的文章\n      return response.data.filter(item =>\n        item.tags?.some(itemTag => itemTag.toLowerCase().includes(tag.toLowerCase()))\n      );\n    }\n  } catch (error) {\n    console.error('根据标签获取知识点失败:', error);\n  }\n\n  return [];\n}\n\n/**\n * 获取知识库统计信息\n * 完全使用API调用获取真实数据\n */\nexport async function getKnowledgeStats() {\n  try {\n    const [categoriesResponse, articlesResponse] = await Promise.all([\n      categoriesApi.getAll(true),\n      articlesApi.getAll({ includeCodeExamples: false, includeRelated: false })\n    ]);\n\n    const categoriesCount = categoriesResponse.success ? categoriesResponse.data?.length || 0 : 0;\n    const articlesCount = articlesResponse.success ? articlesResponse.data?.length || 0 : 0;\n\n    return {\n      categoriesCount,\n      articlesCount,\n      totalItems: articlesCount\n    };\n  } catch (error) {\n    console.error('获取知识库统计失败:', error);\n    return {\n      categoriesCount: 0,\n      articlesCount: 0,\n      totalItems: 0\n    };\n  }\n}\n\n// 兼容性函数 - 为了保持向后兼容\n\n/**\n * 根据ID获取知识点（兼容性函数）\n */\nexport async function getKnowledgeItemById(itemId: string): Promise<KnowledgeItem | null> {\n  return getKnowledgeItem(itemId);\n}\n\n/**\n * 获取知识库分类（兼容性函数）\n */\nexport async function getKnowledgeCategory(categoryId: string): Promise<KnowledgeCategory | null> {\n  try {\n    const categories = await getKnowledgeCategories();\n    return categories.find(category => category.id === categoryId) || null;\n  } catch (error) {\n    console.error('获取知识分类失败:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": "AAAA,uBAAuB;AACvB,2BAA2B;AAC3B,0BAA0B;;;;;;;;;;;;;;;;AAG1B;AACA;;;AAQO,eAAe,yBAAyB,WAAmB,CAAC;IACjE,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACxC,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CACjB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,KAAK,CAAC,GAAG;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;IAC9B;IAEA,6BAA6B;IAC7B,OAAO,EAAE;AACX;AAMO,eAAe,wBAAwB,WAAmB,CAAC;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACxC,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CACjB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,IACpF,KAAK,CAAC,GAAG;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;IAChC;IAEA,kBAAkB;IAClB,OAAO,EAAE;AACX;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAE5C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CACjB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QACjD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;IAC7B;IAEA,6BAA6B;IAC7B,OAAO,EAAE;AACX;AAMO,eAAe,iBAAiB,MAAc;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,QAAQ;YACjD,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,MAAM,mBAAmB,CAAA,GAAA,iIAAA,CAAA,gCAA6B,AAAD,EAAE;gBAAC,SAAS,IAAI;aAAC;YACtE,OAAO,gBAAgB,CAAC,EAAE,IAAI;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;IAC5B;IAEA,OAAO;AACT;AAMO,eAAe,4BAA4B,UAAkB;IAClE,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACxC,UAAU;YACV,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QACnE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;IAC9B;IAEA,OAAO,EAAE;AACX;AAMO,eAAe,qBAAqB,KAAa;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;QAE1C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;IAC5B;IAEA,OAAO,EAAE;AACX;AAMO,eAAe,yBAAyB,WAA0B,EAAE,WAAmB,CAAC;IAC7F,IAAI;QACF,aAAa;QACb,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACxC,UAAU,YAAY,WAAW,IAAI;YACrC,qBAAqB;YACrB,gBAAgB;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,EAAE,SAAS;aACpD,KAAK,CAAC,GAAG;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;IAC9B;IAEA,OAAO,EAAE;AACX;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM;QAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,EAAE;YAC7D,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI;QAC1C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;IAC3B;IAEA,OAAO,EAAE;AACX;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM;QAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QACnE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;IAC5B;IAEA,OAAO,EAAE;AACX;AAMO,eAAe,uBAAuB,GAAW;IACtD,IAAI;QACF,qBAAqB;QACrB,MAAM,WAAW,MAAM,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;QAE1C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,gBAAgB;YAChB,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,OAC1B,KAAK,IAAI,EAAE,KAAK,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,IAAI,WAAW;QAE7E;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;IAChC;IAEA,OAAO,EAAE;AACX;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,CAAC,oBAAoB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC/D,4IAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YACrB,4IAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBAAE,qBAAqB;gBAAO,gBAAgB;YAAM;SACxE;QAED,MAAM,kBAAkB,mBAAmB,OAAO,GAAG,mBAAmB,IAAI,EAAE,UAAU,IAAI;QAC5F,MAAM,gBAAgB,iBAAiB,OAAO,GAAG,iBAAiB,IAAI,EAAE,UAAU,IAAI;QAEtF,OAAO;YACL;YACA;YACA,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;YACL,iBAAiB;YACjB,eAAe;YACf,YAAY;QACd;IACF;AACF;AAOO,eAAe,qBAAqB,MAAc;IACvD,OAAO,iBAAiB;AAC1B;AAKO,eAAe,qBAAqB,UAAkB;IAC3D,IAAI;QACF,MAAM,aAAa,MAAM;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,eAAe;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/hooks/useKnowledgeData.ts"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 知识库统一数据管理Hook\n// 提供统一的数据获取、缓存、错误处理和加载状态管理\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { articlesApi, categoriesApi, codeExamplesApi } from '@/lib/api/knowledge';\nimport type { Database } from '@/lib/database.types';\n\ntype KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];\ntype KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];\ntype CodeExample = Database['public']['Tables']['code_examples']['Row'];\n\n// 创建和更新类型\ntype KnowledgeArticleInsert = Database['public']['Tables']['knowledge_articles']['Insert'];\ntype KnowledgeCategoryInsert = Database['public']['Tables']['knowledge_categories']['Insert'];\ntype CodeExampleInsert = Database['public']['Tables']['code_examples']['Insert'];\n\n// 移除联合类型，使用具体的泛型类型\n\n// 数据类型枚举\nexport type DataType = 'articles' | 'categories' | 'codeExamples';\n\n// API响应类型\ninterface ApiResponse<T = unknown> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n  details?: string;\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Hook配置选项\ninterface UseKnowledgeDataOptions {\n  // 自动获取数据\n  autoFetch?: boolean;\n  // 缓存时间（毫秒）\n  cacheTime?: number;\n  // 重试次数\n  retryCount?: number;\n  // 重试延迟（毫秒）\n  retryDelay?: number;\n  // 乐观更新\n  optimisticUpdates?: boolean;\n  // 后台刷新\n  backgroundRefresh?: boolean;\n  // 调试模式\n  debug?: boolean;\n  // 渐进加载模式\n  progressiveLoading?: boolean;\n  // 基础数据优先加载\n  prioritizeBasicData?: boolean;\n}\n\n// Hook状态类型\ninterface KnowledgeDataState<T> {\n  data: T[];\n  loading: boolean;\n  error: string | null;\n  lastFetch: Date | null;\n  isStale: boolean;\n  retryCount: number;\n}\n\n// Hook返回类型\ninterface UseKnowledgeDataReturn<T> {\n  // 数据状态\n  data: T[];\n  loading: boolean;\n  error: string | null;\n  isStale: boolean;\n  \n  // 操作方法\n  refetch: () => Promise<void>;\n  refresh: () => Promise<void>;\n  invalidate: () => void;\n  retry: () => Promise<void>;\n  \n  // CRUD操作\n  create: (item: Partial<T>) => Promise<boolean>;\n  update: (id: string, item: Partial<T>) => Promise<boolean>;\n  remove: (id: string) => Promise<boolean>;\n\n  // 批量操作\n  batchCreate: (items: Partial<T>[]) => Promise<boolean>;\n  batchUpdate: (items: { id: string; data: Partial<T> }[]) => Promise<boolean>;\n  batchRemove: (ids: string[]) => Promise<boolean>;\n\n  // 工具方法\n  findById: (id: string) => T | undefined;\n  findByField: (field: keyof T, value: unknown) => T[];\n  count: number;\n}\n\n// 默认配置（优化性能）\nconst DEFAULT_OPTIONS: UseKnowledgeDataOptions = {\n  autoFetch: true, // 启用自动获取\n  cacheTime: 5 * 60 * 1000, // 5分钟缓存\n  retryCount: 2, // 最多重试2次\n  retryDelay: 1000, // 1秒重试延迟\n  optimisticUpdates: false, // 禁用乐观更新\n  backgroundRefresh: false, // 禁用后台刷新\n  debug: false,\n  progressiveLoading: true, // 启用渐进加载\n  prioritizeBasicData: true // 优先加载基础数据\n};\n\n// API映射\nconst API_MAP = {\n  articles: articlesApi,\n  categories: categoriesApi,\n  codeExamples: codeExamplesApi\n};\n\nexport function useKnowledgeData<T = any>(\n  dataType: DataType,\n  params?: any,\n  options: UseKnowledgeDataOptions = {}\n): UseKnowledgeDataReturn<T> {\n  const opts = { ...DEFAULT_OPTIONS, ...options };\n  const api = API_MAP[dataType];\n\n  // 稳定化 params 引用，避免无限循环\n  const stableParams = useRef(params);\n  const [paramsKey, setParamsKey] = useState(JSON.stringify(params || {}));\n  \n  // 状态管理\n  const [state, setState] = useState<KnowledgeDataState<T>>({\n    data: [],\n    loading: false,\n    error: null,\n    lastFetch: null,\n    isStale: false,\n    retryCount: 0\n  });\n\n  // 引用管理\n  const abortControllerRef = useRef<AbortController | null>(null);\n  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const cacheTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  // 调试日志\n  const log = useCallback((message: string, data?: unknown) => {\n    if (opts.debug) {\n      console.log(`[useKnowledgeData:${dataType}] ${message}`, data);\n    }\n  }, [dataType, opts.debug]);\n\n  // 设置缓存过期（增加更长的缓存时间以减少请求频率）\n  const setCacheTimeout = useCallback(() => {\n    if (cacheTimeoutRef.current) {\n      clearTimeout(cacheTimeoutRef.current);\n    }\n\n    // 使用更长的缓存时间，最少15分钟\n    const cacheTime = Math.max(opts.cacheTime || 0, 15 * 60 * 1000);\n\n    if (cacheTime > 0) {\n      cacheTimeoutRef.current = setTimeout(() => {\n        setState(prev => ({ ...prev, isStale: true }));\n        log('Cache expired, data marked as stale');\n      }, cacheTime);\n    }\n  }, [opts.cacheTime, log]);\n\n  // 更新 params 引用\n  useEffect(() => {\n    const newParamsKey = JSON.stringify(params || {});\n    if (newParamsKey !== paramsKey) {\n      stableParams.current = params;\n      setParamsKey(newParamsKey);\n    }\n  }, [params, paramsKey]);\n\n  // 获取数据\n  const fetchData = useCallback(async (isRetry = false): Promise<void> => {\n    // 取消之前的请求\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n\n    abortControllerRef.current = new AbortController();\n\n    setState(prev => ({\n      ...prev,\n      loading: true,\n      error: isRetry ? prev.error : null\n    }));\n\n    const currentParams = stableParams.current;\n    log('Fetching data', { params: currentParams, isRetry });\n\n    try {\n      let response: any;\n\n      // 渐进加载逻辑\n      if (opts.progressiveLoading && dataType === 'articles' && opts.prioritizeBasicData) {\n        // 第一步：快速加载基础数据（不包含代码示例和相关文章）\n        const basicParams = {\n          ...currentParams,\n          includeCodeExamples: false,\n          includeRelated: false\n        };\n\n        log('Progressive loading: Fetching basic data first', basicParams);\n        const basicResponse = await api.getAll(basicParams);\n\n        console.log('=== useKnowledgeData 渐进加载调试 ===');\n        console.log('基础数据响应:', basicResponse);\n\n        if (basicResponse.success && basicResponse.data) {\n          console.log('基础数据加载成功，数据数量:', basicResponse.data.length);\n          // 立即显示基础数据\n          setState(prev => ({\n            ...prev,\n            data: basicResponse.data as T[],\n            loading: true, // 保持loading状态，因为还有详细数据要加载\n            error: null,\n            lastFetch: new Date(),\n            isStale: false,\n            retryCount: 0\n          }));\n\n          log('Basic data loaded, now fetching detailed data');\n          console.log('准备加载详细数据，参数:', currentParams);\n\n          // 第二步：后台加载详细数据\n          if (currentParams?.includeCodeExamples || currentParams?.includeRelated) {\n            setTimeout(async () => {\n              try {\n                console.log('开始加载详细数据...');\n                const detailedResponse = await api.getAll(currentParams);\n                console.log('详细数据响应:', detailedResponse);\n\n                if (detailedResponse.success && detailedResponse.data) {\n                  console.log('详细数据加载成功，数据数量:', detailedResponse.data.length);\n                  setState(prev => ({\n                    ...prev,\n                    data: detailedResponse.data as T[],\n                    loading: false,\n                    error: null,\n                    lastFetch: new Date(),\n                    isStale: false,\n                    retryCount: 0\n                  }));\n                  setCacheTimeout();\n                  log('Detailed data loaded');\n                } else {\n                  console.error('详细数据加载失败:', detailedResponse.error);\n                  // 保持基础数据，只关闭loading状态\n                  setState(prev => ({ ...prev, loading: false }));\n                }\n              } catch (error) {\n                console.error('详细数据加载异常:', error);\n                log('Failed to load detailed data', error);\n                // 保持基础数据，只关闭loading状态\n                setState(prev => ({ ...prev, loading: false }));\n              }\n            }, 100); // 100ms延迟，让基础数据先渲染\n          } else {\n            // 如果不需要详细数据，直接完成\n            setState(prev => ({ ...prev, loading: false }));\n          }\n        } else {\n          throw new Error(basicResponse.error || 'Failed to fetch basic data');\n        }\n      } else {\n        // 传统加载方式\n        switch (dataType) {\n          case 'articles':\n            response = await api.getAll(currentParams);\n            break;\n          case 'categories':\n            response = await api.getAll(currentParams?.includeStats);\n            break;\n          case 'codeExamples':\n            response = await api.getAll(currentParams);\n            break;\n          default:\n            throw new Error(`Unsupported data type: ${dataType}`);\n        }\n\n        if (response.success && response.data) {\n          setState(prev => ({\n            ...prev,\n            data: response.data as T[],\n            loading: false,\n            error: null,\n            lastFetch: new Date(),\n            isStale: false,\n            retryCount: 0\n          }));\n\n          setCacheTimeout();\n          log('Data fetched successfully', response.data);\n        } else {\n          throw new Error(response.error || 'Failed to fetch data');\n        }\n      }\n    } catch (error: unknown) {\n      const err = error as Error;\n      if (err.name === 'AbortError') {\n        log('Request aborted');\n        return;\n      }\n\n      const errorMessage = err.message || 'Unknown error occurred';\n\n      setState(prev => {\n        const newRetryCount = prev.retryCount + 1;\n        log('Fetch error', { error: errorMessage, retryCount: newRetryCount });\n\n        // 自动重试（使用最新的retryCount）\n        if (newRetryCount < opts.retryCount! && !isRetry) {\n          retryTimeoutRef.current = setTimeout(() => {\n            fetchData(true);\n          }, opts.retryDelay);\n        }\n\n        return {\n          ...prev,\n          loading: false,\n          error: errorMessage,\n          retryCount: newRetryCount\n        };\n      });\n    }\n  }, [dataType, api, log, setCacheTimeout, opts.retryCount, opts.retryDelay]);\n\n  // 刷新数据\n  const refresh = useCallback(async (): Promise<void> => {\n    setState(prev => ({ ...prev, isStale: false }));\n    await fetchData();\n  }, [fetchData]);\n\n  // 重新获取数据\n  const refetch = useCallback(async (): Promise<void> => {\n    await fetchData();\n  }, [fetchData]);\n\n  // 使缓存失效\n  const invalidate = useCallback(() => {\n    setState(prev => ({ ...prev, isStale: true }));\n    if (cacheTimeoutRef.current) {\n      clearTimeout(cacheTimeoutRef.current);\n    }\n    log('Cache invalidated');\n  }, [log]);\n\n  // 重试\n  const retry = useCallback(async (): Promise<void> => {\n    setState(prev => ({ ...prev, retryCount: 0 }));\n    await fetchData(true);\n  }, [fetchData]);\n\n  // 创建项目\n  const create = useCallback(async (item: Partial<T>): Promise<boolean> => {\n    try {\n      log('Creating item', item);\n      \n      // 乐观更新\n      if (opts.optimisticUpdates) {\n        const tempId = `temp_${Date.now()}`;\n        const tempItem = { ...item, id: tempId } as T;\n        setState(prev => ({\n          ...prev,\n          data: [...prev.data, tempItem]\n        }));\n      }\n\n      const response = await api.create(item as any);\n      \n      if (response.success) {\n        // 刷新数据以获取最新状态\n        await refetch();\n        log('Item created successfully');\n        return true;\n      } else {\n        throw new Error(response.error || 'Failed to create item');\n      }\n    } catch (error: unknown) {\n      const err = error as Error;\n      log('Create error', err.message);\n\n      // 回滚乐观更新\n      if (opts.optimisticUpdates) {\n        await refetch();\n      }\n\n      setState(prev => ({ ...prev, error: err.message }));\n      return false;\n    }\n  }, [api, opts.optimisticUpdates, refetch, log]);\n\n  // 更新项目\n  const update = useCallback(async (id: string, item: Partial<T>): Promise<boolean> => {\n    try {\n      log('Updating item', { id, item });\n      \n      // 乐观更新\n      if (opts.optimisticUpdates) {\n        setState(prev => ({\n          ...prev,\n          data: prev.data.map(d => \n            (d as any).id === id ? { ...d, ...item } : d\n          )\n        }));\n      }\n\n      const response = await api.update(id, item);\n      \n      if (response.success) {\n        // 刷新数据以获取最新状态\n        await refetch();\n        log('Item updated successfully');\n        return true;\n      } else {\n        throw new Error(response.error || 'Failed to update item');\n      }\n    } catch (error: any) {\n      log('Update error', error.message);\n      \n      // 回滚乐观更新\n      if (opts.optimisticUpdates) {\n        await refetch();\n      }\n      \n      setState(prev => ({ ...prev, error: error.message }));\n      return false;\n    }\n  }, [api, opts.optimisticUpdates, refetch, log]);\n\n  // 删除项目\n  const remove = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      log('Removing item', { id });\n      \n      // 乐观更新\n      if (opts.optimisticUpdates) {\n        setState(prev => ({\n          ...prev,\n          data: prev.data.filter(d => (d as any).id !== id)\n        }));\n      }\n\n      const response = await api.delete(id);\n      \n      if (response.success) {\n        // 刷新数据以获取最新状态\n        await refetch();\n        log('Item removed successfully');\n        return true;\n      } else {\n        throw new Error(response.error || 'Failed to remove item');\n      }\n    } catch (error: any) {\n      log('Remove error', error.message);\n      \n      // 回滚乐观更新\n      if (opts.optimisticUpdates) {\n        await refetch();\n      }\n      \n      setState(prev => ({ ...prev, error: error.message }));\n      return false;\n    }\n  }, [api, opts.optimisticUpdates, refetch, log]);\n\n  // 批量创建\n  const batchCreate = useCallback(async (items: any[]): Promise<boolean> => {\n    try {\n      log('Batch creating items', { count: items.length });\n      \n      const results = await Promise.all(\n        items.map(item => api.create(item))\n      );\n      \n      const allSuccess = results.every(r => r.success);\n      \n      if (allSuccess) {\n        await refetch();\n        log('Batch create successful');\n        return true;\n      } else {\n        throw new Error('Some items failed to create');\n      }\n    } catch (error: any) {\n      log('Batch create error', error.message);\n      setState(prev => ({ ...prev, error: error.message }));\n      return false;\n    }\n  }, [api, refetch, log]);\n\n  // 批量更新\n  const batchUpdate = useCallback(async (items: { id: string; data: any }[]): Promise<boolean> => {\n    try {\n      log('Batch updating items', { count: items.length });\n      \n      const results = await Promise.all(\n        items.map(item => api.update(item.id, item.data))\n      );\n      \n      const allSuccess = results.every(r => r.success);\n      \n      if (allSuccess) {\n        await refetch();\n        log('Batch update successful');\n        return true;\n      } else {\n        throw new Error('Some items failed to update');\n      }\n    } catch (error: any) {\n      log('Batch update error', error.message);\n      setState(prev => ({ ...prev, error: error.message }));\n      return false;\n    }\n  }, [api, refetch, log]);\n\n  // 批量删除\n  const batchRemove = useCallback(async (ids: string[]): Promise<boolean> => {\n    try {\n      log('Batch removing items', { count: ids.length });\n      \n      const results = await Promise.all(\n        ids.map(id => api.delete(id))\n      );\n      \n      const allSuccess = results.every(r => r.success);\n      \n      if (allSuccess) {\n        await refetch();\n        log('Batch remove successful');\n        return true;\n      } else {\n        throw new Error('Some items failed to remove');\n      }\n    } catch (error: any) {\n      log('Batch remove error', error.message);\n      setState(prev => ({ ...prev, error: error.message }));\n      return false;\n    }\n  }, [api, refetch, log]);\n\n  // 根据ID查找\n  const findById = useCallback((id: string): T | undefined => {\n    return state.data.find(item => (item as any).id === id);\n  }, [state.data]);\n\n  // 根据字段查找\n  const findByField = useCallback((field: keyof T, value: any): T[] => {\n    return state.data.filter(item => (item as any)[field] === value);\n  }, [state.data]);\n\n  // 自动获取数据\n  useEffect(() => {\n    if (opts.autoFetch && !state.loading && !state.data.length) {\n      fetchData();\n    }\n\n    // 清理函数\n    return () => {\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n      if (retryTimeoutRef.current) {\n        clearTimeout(retryTimeoutRef.current);\n      }\n      if (cacheTimeoutRef.current) {\n        clearTimeout(cacheTimeoutRef.current);\n      }\n    };\n  }, [opts.autoFetch, state.loading, state.data.length, fetchData]); // 重新添加依赖\n\n  // 后台刷新（禁用以避免无限循环）\n  useEffect(() => {\n    if (opts.backgroundRefresh && state.isStale && !state.loading) {\n      log('Background refresh triggered');\n      // 暂时禁用自动后台刷新以避免无限循环\n      // fetchData();\n    }\n  }, [opts.backgroundRefresh, state.isStale, state.loading, log]);\n\n  return {\n    // 数据状态\n    data: state.data,\n    loading: state.loading,\n    error: state.error,\n    isStale: state.isStale,\n    \n    // 操作方法\n    refetch,\n    refresh,\n    invalidate,\n    retry,\n    \n    // CRUD操作\n    create,\n    update,\n    remove,\n    \n    // 批量操作\n    batchCreate,\n    batchUpdate,\n    batchRemove,\n    \n    // 工具方法\n    findById,\n    findByField,\n    count: state.data.length\n  };\n}\n"], "names": [], "mappings": ";;;AAEA,6BAA6B;AAC7B,2BAA2B;AAE3B;AACA;AANA;;;AAoGA,aAAa;AACb,MAAM,kBAA2C;IAC/C,WAAW;IACX,WAAW,IAAI,KAAK;IACpB,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,mBAAmB;IACnB,OAAO;IACP,oBAAoB;IACpB,qBAAqB,KAAK,WAAW;AACvC;AAEA,QAAQ;AACR,MAAM,UAAU;IACd,UAAU,4IAAA,CAAA,cAAW;IACrB,YAAY,4IAAA,CAAA,gBAAa;IACzB,cAAc,4IAAA,CAAA,kBAAe;AAC/B;AAEO,SAAS,iBACd,QAAkB,EAClB,MAAY,EACZ,UAAmC,CAAC,CAAC;IAErC,MAAM,OAAO;QAAE,GAAG,eAAe;QAAE,GAAG,OAAO;IAAC;IAC9C,MAAM,MAAM,OAAO,CAAC,SAAS;IAE7B,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC;IAErE,OAAO;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QACxD,MAAM,EAAE;QACR,SAAS;QACT,OAAO;QACP,WAAW;QACX,SAAS;QACT,YAAY;IACd;IAEA,OAAO;IACP,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEtD,OAAO;IACP,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB;QACxC,IAAI,KAAK,KAAK,EAAE;YACd,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE;QAC3D;IACF,GAAG;QAAC;QAAU,KAAK,KAAK;KAAC;IAEzB,2BAA2B;IAC3B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,gBAAgB,OAAO,EAAE;YAC3B,aAAa,gBAAgB,OAAO;QACtC;QAEA,mBAAmB;QACnB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK;QAE1D,IAAI,YAAY,GAAG;YACjB,gBAAgB,OAAO,GAAG,WAAW;gBACnC,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;oBAAK,CAAC;gBAC5C,IAAI;YACN,GAAG;QACL;IACF,GAAG;QAAC,KAAK,SAAS;QAAE;KAAI;IAExB,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,KAAK,SAAS,CAAC,UAAU,CAAC;QAC/C,IAAI,iBAAiB,WAAW;YAC9B,aAAa,OAAO,GAAG;YACvB,aAAa;QACf;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,OAAO;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,UAAU,KAAK;QAClD,UAAU;QACV,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,KAAK;QAClC;QAEA,mBAAmB,OAAO,GAAG,IAAI;QAEjC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,SAAS;gBACT,OAAO,UAAU,KAAK,KAAK,GAAG;YAChC,CAAC;QAED,MAAM,gBAAgB,aAAa,OAAO;QAC1C,IAAI,iBAAiB;YAAE,QAAQ;YAAe;QAAQ;QAEtD,IAAI;YACF,IAAI;YAEJ,SAAS;YACT,IAAI,KAAK,kBAAkB,IAAI,aAAa,cAAc,KAAK,mBAAmB,EAAE;gBAClF,6BAA6B;gBAC7B,MAAM,cAAc;oBAClB,GAAG,aAAa;oBAChB,qBAAqB;oBACrB,gBAAgB;gBAClB;gBAEA,IAAI,kDAAkD;gBACtD,MAAM,gBAAgB,MAAM,IAAI,MAAM,CAAC;gBAEvC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,WAAW;gBAEvB,IAAI,cAAc,OAAO,IAAI,cAAc,IAAI,EAAE;oBAC/C,QAAQ,GAAG,CAAC,kBAAkB,cAAc,IAAI,CAAC,MAAM;oBACvD,WAAW;oBACX,SAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,MAAM,cAAc,IAAI;4BACxB,SAAS;4BACT,OAAO;4BACP,WAAW,IAAI;4BACf,SAAS;4BACT,YAAY;wBACd,CAAC;oBAED,IAAI;oBACJ,QAAQ,GAAG,CAAC,gBAAgB;oBAE5B,eAAe;oBACf,IAAI,eAAe,uBAAuB,eAAe,gBAAgB;wBACvE,WAAW;4BACT,IAAI;gCACF,QAAQ,GAAG,CAAC;gCACZ,MAAM,mBAAmB,MAAM,IAAI,MAAM,CAAC;gCAC1C,QAAQ,GAAG,CAAC,WAAW;gCAEvB,IAAI,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,EAAE;oCACrD,QAAQ,GAAG,CAAC,kBAAkB,iBAAiB,IAAI,CAAC,MAAM;oCAC1D,SAAS,CAAA,OAAQ,CAAC;4CAChB,GAAG,IAAI;4CACP,MAAM,iBAAiB,IAAI;4CAC3B,SAAS;4CACT,OAAO;4CACP,WAAW,IAAI;4CACf,SAAS;4CACT,YAAY;wCACd,CAAC;oCACD;oCACA,IAAI;gCACN,OAAO;oCACL,QAAQ,KAAK,CAAC,aAAa,iBAAiB,KAAK;oCACjD,sBAAsB;oCACtB,SAAS,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS;wCAAM,CAAC;gCAC/C;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,aAAa;gCAC3B,IAAI,gCAAgC;gCACpC,sBAAsB;gCACtB,SAAS,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,SAAS;oCAAM,CAAC;4BAC/C;wBACF,GAAG,MAAM,mBAAmB;oBAC9B,OAAO;wBACL,iBAAiB;wBACjB,SAAS,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,SAAS;4BAAM,CAAC;oBAC/C;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,cAAc,KAAK,IAAI;gBACzC;YACF,OAAO;gBACL,SAAS;gBACT,OAAQ;oBACN,KAAK;wBACH,WAAW,MAAM,IAAI,MAAM,CAAC;wBAC5B;oBACF,KAAK;wBACH,WAAW,MAAM,IAAI,MAAM,CAAC,eAAe;wBAC3C;oBACF,KAAK;wBACH,WAAW,MAAM,IAAI,MAAM,CAAC;wBAC5B;oBACF;wBACE,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,UAAU;gBACxD;gBAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,SAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,MAAM,SAAS,IAAI;4BACnB,SAAS;4BACT,OAAO;4BACP,WAAW,IAAI;4BACf,SAAS;4BACT,YAAY;wBACd,CAAC;oBAED;oBACA,IAAI,6BAA6B,SAAS,IAAI;gBAChD,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,MAAM;YACZ,IAAI,IAAI,IAAI,KAAK,cAAc;gBAC7B,IAAI;gBACJ;YACF;YAEA,MAAM,eAAe,IAAI,OAAO,IAAI;YAEpC,SAAS,CAAA;gBACP,MAAM,gBAAgB,KAAK,UAAU,GAAG;gBACxC,IAAI,eAAe;oBAAE,OAAO;oBAAc,YAAY;gBAAc;gBAEpE,wBAAwB;gBACxB,IAAI,gBAAgB,KAAK,UAAU,IAAK,CAAC,SAAS;oBAChD,gBAAgB,OAAO,GAAG,WAAW;wBACnC,UAAU;oBACZ,GAAG,KAAK,UAAU;gBACpB;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAK;QAAK;QAAiB,KAAK,UAAU;QAAE,KAAK,UAAU;KAAC;IAE1E,OAAO;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAM,CAAC;QAC7C,MAAM;IACR,GAAG;QAAC;KAAU;IAEd,SAAS;IACT,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,MAAM;IACR,GAAG;QAAC;KAAU;IAEd,QAAQ;IACR,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAK,CAAC;QAC5C,IAAI,gBAAgB,OAAO,EAAE;YAC3B,aAAa,gBAAgB,OAAO;QACtC;QACA,IAAI;IACN,GAAG;QAAC;KAAI;IAER,KAAK;IACL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY;YAAE,CAAC;QAC5C,MAAM,UAAU;IAClB,GAAG;QAAC;KAAU;IAEd,OAAO;IACP,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,IAAI;YACF,IAAI,iBAAiB;YAErB,OAAO;YACP,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACnC,MAAM,WAAW;oBAAE,GAAG,IAAI;oBAAE,IAAI;gBAAO;gBACvC,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM;+BAAI,KAAK,IAAI;4BAAE;yBAAS;oBAChC,CAAC;YACH;YAEA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;YAElC,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,MAAM;YACZ,IAAI,gBAAgB,IAAI,OAAO;YAE/B,SAAS;YACT,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,MAAM;YACR;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,IAAI,OAAO;gBAAC,CAAC;YACjD,OAAO;QACT;IACF,GAAG;QAAC;QAAK,KAAK,iBAAiB;QAAE;QAAS;KAAI;IAE9C,OAAO;IACP,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QAC5C,IAAI;YACF,IAAI,iBAAiB;gBAAE;gBAAI;YAAK;YAEhC,OAAO;YACP,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,IAClB,AAAC,EAAU,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,IAAI;4BAAC,IAAI;oBAE/C,CAAC;YACH;YAEA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,IAAI;YAEtC,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,gBAAgB,MAAM,OAAO;YAEjC,SAAS;YACT,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,MAAM;YACR;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC,CAAC;YACnD,OAAO;QACT;IACF,GAAG;QAAC;QAAK,KAAK,iBAAiB;QAAE;QAAS;KAAI;IAE9C,OAAO;IACP,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,IAAI;YACF,IAAI,iBAAiB;gBAAE;YAAG;YAE1B,OAAO;YACP,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,AAAC,EAAU,EAAE,KAAK;oBAChD,CAAC;YACH;YAEA,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC;YAElC,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,gBAAgB,MAAM,OAAO;YAEjC,SAAS;YACT,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,MAAM;YACR;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC,CAAC;YACnD,OAAO;QACT;IACF,GAAG;QAAC;QAAK,KAAK,iBAAiB;QAAE;QAAS;KAAI;IAE9C,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,IAAI,wBAAwB;gBAAE,OAAO,MAAM,MAAM;YAAC;YAElD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,MAAM,CAAC;YAG/B,MAAM,aAAa,QAAQ,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO;YAE/C,IAAI,YAAY;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,sBAAsB,MAAM,OAAO;YACvC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC,CAAC;YACnD,OAAO;QACT;IACF,GAAG;QAAC;QAAK;QAAS;KAAI;IAEtB,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,IAAI,wBAAwB;gBAAE,OAAO,MAAM,MAAM;YAAC;YAElD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI;YAGjD,MAAM,aAAa,QAAQ,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO;YAE/C,IAAI,YAAY;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,sBAAsB,MAAM,OAAO;YACvC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC,CAAC;YACnD,OAAO;QACT;IACF,GAAG;QAAC;QAAK;QAAS;KAAI;IAEtB,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,IAAI,wBAAwB;gBAAE,OAAO,IAAI,MAAM;YAAC;YAEhD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,MAAM,CAAC;YAG3B,MAAM,aAAa,QAAQ,KAAK,CAAC,CAAA,IAAK,EAAE,OAAO;YAE/C,IAAI,YAAY;gBACd,MAAM;gBACN,IAAI;gBACJ,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,sBAAsB,MAAM,OAAO;YACvC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC,CAAC;YACnD,OAAO;QACT;IACF,GAAG;QAAC;QAAK;QAAS;KAAI;IAEtB,SAAS;IACT,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,AAAC,KAAa,EAAE,KAAK;IACtD,GAAG;QAAC,MAAM,IAAI;KAAC;IAEf,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAgB;QAC/C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,OAAQ,AAAC,IAAY,CAAC,MAAM,KAAK;IAC5D,GAAG;QAAC,MAAM,IAAI;KAAC;IAEf,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;YAC1D;QACF;QAEA,OAAO;QACP,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,KAAK;YAClC;YACA,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,aAAa,gBAAgB,OAAO;YACtC;YACA,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,aAAa,gBAAgB,OAAO;YACtC;QACF;IACF,GAAG;QAAC,KAAK,SAAS;QAAE,MAAM,OAAO;QAAE,MAAM,IAAI,CAAC,MAAM;QAAE;KAAU,GAAG,SAAS;IAE5E,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,iBAAiB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;YAC7D,IAAI;QACJ,oBAAoB;QACpB,eAAe;QACjB;IACF,GAAG;QAAC,KAAK,iBAAiB;QAAE,MAAM,OAAO;QAAE,MAAM,OAAO;QAAE;KAAI;IAE9D,OAAO;QACL,OAAO;QACP,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,OAAO,MAAM,KAAK;QAClB,SAAS,MAAM,OAAO;QAEtB,OAAO;QACP;QACA;QACA;QACA;QAEA,SAAS;QACT;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA,OAAO,MAAM,IAAI,CAAC,MAAM;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/api/search-history.ts"], "sourcesContent": ["// MySQLAi.de - 搜索历史 API 客户端\n// 提供类型安全的搜索历史 API 调用方法\n\nimport { SearchHistoryItem } from '@/lib/types';\n\n// API 响应类型\ninterface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n  total?: number;\n}\n\n// API 基础配置\nconst API_BASE = '/api/knowledge/search-history';\n\n// 通用 API 调用函数\nasync function apiCall<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('API调用失败:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '网络错误',\n    };\n  }\n}\n\n// 搜索历史 API\nexport const searchHistoryApi = {\n  // 获取搜索历史\n  getHistory: async (options: {\n    limit?: number;\n    offset?: number;\n    query?: string;\n  } = {}): Promise<ApiResponse<SearchHistoryItem[]>> => {\n    const params = new URLSearchParams();\n    \n    if (options.limit) params.append('limit', options.limit.toString());\n    if (options.offset) params.append('offset', options.offset.toString());\n    if (options.query) params.append('query', options.query);\n\n    const queryString = params.toString();\n    const endpoint = queryString ? `?${queryString}` : '';\n    \n    return apiCall<SearchHistoryItem[]>(endpoint);\n  },\n\n  // 添加搜索记录\n  addSearchRecord: async (query: string, resultsCount: number = 0): Promise<ApiResponse<SearchHistoryItem>> => {\n    return apiCall<SearchHistoryItem>('', {\n      method: 'POST',\n      body: JSON.stringify({\n        query: query.trim(),\n        results_count: resultsCount\n      }),\n    });\n  },\n\n  // 删除特定搜索记录\n  deleteRecord: async (id: string): Promise<ApiResponse<void>> => {\n    return apiCall<void>(`?id=${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  // 清除所有搜索历史\n  clearAllHistory: async (): Promise<ApiResponse<void>> => {\n    return apiCall<void>('?clearAll=true', {\n      method: 'DELETE',\n    });\n  },\n\n  // 搜索历史记录（在历史中查找）\n  searchInHistory: async (query: string, limit: number = 10): Promise<ApiResponse<SearchHistoryItem[]>> => {\n    return apiCall<SearchHistoryItem[]>(`?query=${encodeURIComponent(query)}&limit=${limit}`);\n  },\n};\n\n// 本地存储管理（作为备用方案）\nexport const localSearchHistory = {\n  // 本地存储键名\n  STORAGE_KEY: 'mysqlai_search_history',\n  \n  // 获取本地搜索历史\n  getLocal: (): SearchHistoryItem[] => {\n    try {\n      const stored = localStorage.getItem(localSearchHistory.STORAGE_KEY);\n      return stored ? JSON.parse(stored) : [];\n    } catch (error) {\n      console.error('获取本地搜索历史失败:', error);\n      return [];\n    }\n  },\n\n  // 保存到本地存储\n  saveLocal: (items: SearchHistoryItem[]): void => {\n    try {\n      localStorage.setItem(localSearchHistory.STORAGE_KEY, JSON.stringify(items));\n    } catch (error) {\n      console.error('保存本地搜索历史失败:', error);\n    }\n  },\n\n  // 添加搜索记录到本地\n  addLocal: (query: string, resultsCount: number = 0): void => {\n    const items = localSearchHistory.getLocal();\n    const newItem: SearchHistoryItem = {\n      id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      query: query.trim(),\n      results_count: resultsCount,\n      created_at: new Date().toISOString()\n    };\n\n    // 检查是否已存在相同查询（最近10条）\n    const recentItems = items.slice(0, 10);\n    const existingIndex = recentItems.findIndex(item => \n      item.query.toLowerCase() === query.toLowerCase().trim()\n    );\n\n    if (existingIndex >= 0) {\n      // 如果存在，移动到最前面\n      const existingItem = items.splice(existingIndex, 1)[0];\n      items.unshift({\n        ...existingItem,\n        results_count: resultsCount,\n        created_at: new Date().toISOString()\n      });\n    } else {\n      // 如果不存在，添加到最前面\n      items.unshift(newItem);\n    }\n\n    // 限制最多保存50条记录\n    const limitedItems = items.slice(0, 50);\n    localSearchHistory.saveLocal(limitedItems);\n  },\n\n  // 删除本地搜索记录\n  deleteLocal: (id: string): void => {\n    const items = localSearchHistory.getLocal();\n    const filteredItems = items.filter(item => item.id !== id);\n    localSearchHistory.saveLocal(filteredItems);\n  },\n\n  // 清除所有本地搜索历史\n  clearLocal: (): void => {\n    try {\n      localStorage.removeItem(localSearchHistory.STORAGE_KEY);\n    } catch (error) {\n      console.error('清除本地搜索历史失败:', error);\n    }\n  },\n};\n\n// 混合搜索历史管理（优先使用API，降级到本地存储）\nexport const hybridSearchHistory = {\n  // 获取搜索历史（API优先，本地降级）\n  getHistory: async (limit: number = 10): Promise<SearchHistoryItem[]> => {\n    try {\n      const response = await searchHistoryApi.getHistory({ limit });\n      if (response.success && response.data) {\n        return response.data;\n      }\n    } catch (error) {\n      console.warn('API获取搜索历史失败，使用本地存储:', error);\n    }\n\n    // 降级到本地存储\n    return localSearchHistory.getLocal().slice(0, limit);\n  },\n\n  // 添加搜索记录（API优先，本地降级）\n  addRecord: async (query: string, resultsCount: number = 0): Promise<void> => {\n    try {\n      const response = await searchHistoryApi.addSearchRecord(query, resultsCount);\n      if (response.success) {\n        return;\n      }\n    } catch (error) {\n      console.warn('API保存搜索历史失败，使用本地存储:', error);\n    }\n\n    // 降级到本地存储\n    localSearchHistory.addLocal(query, resultsCount);\n  },\n\n  // 删除搜索记录（API优先，本地降级）\n  deleteRecord: async (id: string): Promise<void> => {\n    try {\n      if (id.startsWith('local_')) {\n        // 本地记录直接删除\n        localSearchHistory.deleteLocal(id);\n        return;\n      }\n\n      const response = await searchHistoryApi.deleteRecord(id);\n      if (response.success) {\n        return;\n      }\n    } catch (error) {\n      console.warn('API删除搜索记录失败:', error);\n    }\n  },\n\n  // 清除所有搜索历史（API优先，本地降级）\n  clearAll: async (): Promise<void> => {\n    try {\n      const response = await searchHistoryApi.clearAllHistory();\n      if (response.success) {\n        // API成功后也清除本地存储\n        localSearchHistory.clearLocal();\n        return;\n      }\n    } catch (error) {\n      console.warn('API清除搜索历史失败，使用本地存储:', error);\n    }\n\n    // 降级到本地存储\n    localSearchHistory.clearLocal();\n  },\n};\n"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,uBAAuB;;;;;;AAavB,WAAW;AACX,MAAM,WAAW;AAEjB,cAAc;AACd,eAAe,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,UAAU,EAAE;YACrD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,SAAS;IACT,YAAY,OAAO,UAIf,CAAC,CAAC;QACJ,MAAM,SAAS,IAAI;QAEnB,IAAI,QAAQ,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QAChE,IAAI,QAAQ,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ;QACnE,IAAI,QAAQ,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK;QAEvD,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;QAEnD,OAAO,QAA6B;IACtC;IAEA,SAAS;IACT,iBAAiB,OAAO,OAAe,eAAuB,CAAC;QAC7D,OAAO,QAA2B,IAAI;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,MAAM,IAAI;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,WAAW;IACX,cAAc,OAAO;QACnB,OAAO,QAAc,CAAC,IAAI,EAAE,IAAI,EAAE;YAChC,QAAQ;QACV;IACF;IAEA,WAAW;IACX,iBAAiB;QACf,OAAO,QAAc,kBAAkB;YACrC,QAAQ;QACV;IACF;IAEA,iBAAiB;IACjB,iBAAiB,OAAO,OAAe,QAAgB,EAAE;QACvD,OAAO,QAA6B,CAAC,OAAO,EAAE,mBAAmB,OAAO,OAAO,EAAE,OAAO;IAC1F;AACF;AAGO,MAAM,qBAAqB;IAChC,SAAS;IACT,aAAa;IAEb,WAAW;IACX,UAAU;QACR,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,mBAAmB,WAAW;YAClE,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO,EAAE;QACX;IACF;IAEA,UAAU;IACV,WAAW,CAAC;QACV,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,WAAW,EAAE,KAAK,SAAS,CAAC;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,YAAY;IACZ,UAAU,CAAC,OAAe,eAAuB,CAAC;QAChD,MAAM,QAAQ,mBAAmB,QAAQ;QACzC,MAAM,UAA6B;YACjC,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACpE,OAAO,MAAM,IAAI;YACjB,eAAe;YACf,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,qBAAqB;QACrB,MAAM,cAAc,MAAM,KAAK,CAAC,GAAG;QACnC,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,OAC1C,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW,GAAG,IAAI;QAGvD,IAAI,iBAAiB,GAAG;YACtB,cAAc;YACd,MAAM,eAAe,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;YACtD,MAAM,OAAO,CAAC;gBACZ,GAAG,YAAY;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;QACF,OAAO;YACL,eAAe;YACf,MAAM,OAAO,CAAC;QAChB;QAEA,cAAc;QACd,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG;QACpC,mBAAmB,SAAS,CAAC;IAC/B;IAEA,WAAW;IACX,aAAa,CAAC;QACZ,MAAM,QAAQ,mBAAmB,QAAQ;QACzC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACvD,mBAAmB,SAAS,CAAC;IAC/B;IAEA,aAAa;IACb,YAAY;QACV,IAAI;YACF,aAAa,UAAU,CAAC,mBAAmB,WAAW;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;AACF;AAGO,MAAM,sBAAsB;IACjC,qBAAqB;IACrB,YAAY,OAAO,QAAgB,EAAE;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,iBAAiB,UAAU,CAAC;gBAAE;YAAM;YAC3D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uBAAuB;QACtC;QAEA,UAAU;QACV,OAAO,mBAAmB,QAAQ,GAAG,KAAK,CAAC,GAAG;IAChD;IAEA,qBAAqB;IACrB,WAAW,OAAO,OAAe,eAAuB,CAAC;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,iBAAiB,eAAe,CAAC,OAAO;YAC/D,IAAI,SAAS,OAAO,EAAE;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uBAAuB;QACtC;QAEA,UAAU;QACV,mBAAmB,QAAQ,CAAC,OAAO;IACrC;IAEA,qBAAqB;IACrB,cAAc,OAAO;QACnB,IAAI;YACF,IAAI,GAAG,UAAU,CAAC,WAAW;gBAC3B,WAAW;gBACX,mBAAmB,WAAW,CAAC;gBAC/B;YACF;YAEA,MAAM,WAAW,MAAM,iBAAiB,YAAY,CAAC;YACrD,IAAI,SAAS,OAAO,EAAE;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gBAAgB;QAC/B;IACF;IAEA,uBAAuB;IACvB,UAAU;QACR,IAAI;YACF,MAAM,WAAW,MAAM,iBAAiB,eAAe;YACvD,IAAI,SAAS,OAAO,EAAE;gBACpB,gBAAgB;gBAChB,mBAAmB,UAAU;gBAC7B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,uBAAuB;QACtC;QAEA,UAAU;QACV,mBAAmB,UAAU;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Button按钮组件\n// 可复用的按钮组件，支持多种变体和尺寸\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/lib/types';\n\nconst Button = React.forwardRef<\n  HTMLButtonElement | HTMLAnchorElement,\n  ButtonProps\n>(({\n  className,\n  variant = 'primary',\n  size = 'md',\n  href,\n  onClick,\n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  type = 'button',\n  children,\n  ...props\n}, ref) => {\n  // 基础样式\n  const baseStyles = [\n    'inline-flex items-center justify-center font-semibold rounded-lg',\n    'transition-all duration-300 ease-out',\n    'focus:outline-none focus:ring-4',\n    'disabled:opacity-50 disabled:cursor-not-allowed',\n    'disabled:hover:scale-100 disabled:hover:shadow-none'\n  ].join(' ');\n\n  // 变体样式\n  const variantStyles = {\n    primary: [\n      'bg-mysql-primary text-white shadow-lg',\n      'hover:bg-mysql-primary-dark hover:shadow-xl hover:scale-105',\n      'focus:ring-mysql-primary/30',\n      'active:scale-95'\n    ].join(' '),\n    \n    secondary: [\n      'bg-white text-mysql-primary border-2 border-mysql-primary shadow-lg',\n      'hover:bg-mysql-primary hover:text-white hover:shadow-xl hover:scale-105',\n      'focus:ring-mysql-primary/30',\n      'active:scale-95'\n    ].join(' '),\n    \n    outline: [\n      'bg-transparent text-mysql-primary border-2 border-mysql-primary',\n      'hover:bg-mysql-primary hover:text-white hover:shadow-lg hover:scale-105',\n      'focus:ring-mysql-primary/30',\n      'active:scale-95'\n    ].join(' '),\n    \n    ghost: [\n      'bg-transparent text-mysql-primary',\n      'hover:bg-mysql-primary-light hover:scale-105',\n      'focus:ring-mysql-primary/30',\n      'active:scale-95'\n    ].join(' ')\n  };\n\n  // 尺寸样式\n  const sizeStyles = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg'\n  };\n\n  // 组合样式\n  const buttonStyles = cn(\n    baseStyles,\n    variantStyles[variant],\n    sizeStyles[size],\n    className\n  );\n\n  // 图标渲染\n  const renderIcon = () => {\n    if (loading) {\n      return <Loader2 className=\"w-4 h-4 animate-spin\" />;\n    }\n    return icon;\n  };\n\n  // 内容渲染\n  const renderContent = () => (\n    <>\n      {(icon || loading) && iconPosition === 'left' && (\n        <span className={cn('flex items-center', children && 'mr-2')}>\n          {renderIcon()}\n        </span>\n      )}\n      {children}\n      {icon && iconPosition === 'right' && (\n        <span className={cn('flex items-center', children && 'ml-2')}>\n          {renderIcon()}\n        </span>\n      )}\n    </>\n  );\n\n  // 如果有href，渲染为Link\n  if (href) {\n    return (\n      <motion.div\n        whileHover={{ scale: disabled ? 1 : 1.05, y: disabled ? 0 : -2 }}\n        whileTap={{ scale: disabled ? 1 : 0.95 }}\n      >\n        <Link\n          href={href}\n          className={buttonStyles}\n          {...(props as any)}\n          ref={ref as any}\n        >\n          {renderContent()}\n        </Link>\n      </motion.div>\n    );\n  }\n\n  // 渲染为button\n  return (\n    <motion.button\n      whileHover={{ scale: disabled ? 1 : 1.05, y: disabled ? 0 : -2 }}\n      whileTap={{ scale: disabled ? 1 : 0.95 }}\n      className={buttonStyles}\n      onClick={onClick}\n      disabled={disabled || loading}\n      type={type}\n      {...(props as any)}\n      ref={ref as any}\n    >\n      {renderContent()}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA,0BAA0B;AAC1B,qBAAqB;AAErB;AACA;AACA;AACA;AACA;AATA;;;;;;;AAYA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAG7B,CAAC,EACD,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,IAAI,EACJ,OAAO,EACP,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,OAAO,QAAQ,EACf,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,OAAO;IACP,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,OAAO;IACP,MAAM,gBAAgB;QACpB,SAAS;YACP;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,WAAW;YACT;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,SAAS;YACP;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,OAAO;YACL;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;IACT;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,OAAO;IACP,MAAM,eAAe,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACpB,YACA,aAAa,CAAC,QAAQ,EACtB,UAAU,CAAC,KAAK,EAChB;IAGF,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,qBAAO,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QACA,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAgB,kBACpB;;gBACG,CAAC,QAAQ,OAAO,KAAK,iBAAiB,wBACrC,8OAAC;oBAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,YAAY;8BAClD;;;;;;gBAGJ;gBACA,QAAQ,iBAAiB,yBACxB,8OAAC;oBAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,YAAY;8BAClD;;;;;;;;IAMT,kBAAkB;IAClB,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,YAAY;gBAAE,OAAO,WAAW,IAAI;gBAAM,GAAG,WAAW,IAAI,CAAC;YAAE;YAC/D,UAAU;gBAAE,OAAO,WAAW,IAAI;YAAK;sBAEvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM;gBACN,WAAW;gBACV,GAAI,KAAK;gBACV,KAAK;0BAEJ;;;;;;;;;;;IAIT;IAEA,YAAY;IACZ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO,WAAW,IAAI;YAAM,GAAG,WAAW,IAAI,CAAC;QAAE;QAC/D,UAAU;YAAE,OAAO,WAAW,IAAI;QAAK;QACvC,WAAW;QACX,SAAS;QACT,UAAU,YAAY;QACtB,MAAM;QACL,GAAI,KAAK;QACV,KAAK;kBAEJ;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/PageLoader.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 页面加载动画组件\n// 专业的页面加载指示器，提供流畅的用户体验\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Database, Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface PageLoaderProps {\n  isLoading?: boolean;\n  message?: string;\n  className?: string;\n}\n\nexport default function PageLoader({\n  isLoading = true,\n  message = '加载中...',\n  className,\n}: PageLoaderProps) {\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    if (!isLoading) return;\n\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 90) return prev;\n        return prev + Math.random() * 10;\n      });\n    }, 200);\n\n    return () => clearInterval(interval);\n  }, [isLoading]);\n\n  useEffect(() => {\n    if (!isLoading) {\n      setProgress(100);\n    }\n  }, [isLoading]);\n\n  return (\n    <AnimatePresence>\n      {isLoading && (\n        <motion.div\n          initial={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.5, ease: \"easeOut\" }}\n          className={cn(\n            'fixed inset-0 bg-white z-50 flex flex-col items-center justify-center',\n            className\n          )}\n        >\n          {/* 主要加载动画 */}\n          <div className=\"flex flex-col items-center space-y-8\">\n            {/* Logo动画 */}\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.6, ease: \"easeOut\" }}\n              className=\"relative\"\n            >\n              <div className=\"flex items-center justify-center w-20 h-20 bg-mysql-primary rounded-2xl shadow-lg\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                >\n                  <Database className=\"w-10 h-10 text-white\" />\n                </motion.div>\n              </div>\n              \n              {/* 脉冲效果 */}\n              <motion.div\n                animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute inset-0 bg-mysql-primary rounded-2xl\"\n              />\n            </motion.div>\n\n            {/* 品牌名称 */}\n            <motion.div\n              initial={{ y: 20, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.2, ease: \"easeOut\" }}\n              className=\"text-center\"\n            >\n              <h1 className=\"text-2xl font-bold text-mysql-text mb-2\">\n                MySQLAi.de\n              </h1>\n              <p className=\"text-mysql-text-light\">\n                MySQL智能分析专家\n              </p>\n            </motion.div>\n\n            {/* 加载进度条 */}\n            <motion.div\n              initial={{ width: 0, opacity: 0 }}\n              animate={{ width: 200, opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.4, ease: \"easeOut\" }}\n              className=\"relative\"\n            >\n              <div className=\"w-48 h-2 bg-mysql-primary-light rounded-full overflow-hidden\">\n                <motion.div\n                  initial={{ width: '0%' }}\n                  animate={{ width: `${progress}%` }}\n                  transition={{ duration: 0.3, ease: \"easeOut\" }}\n                  className=\"h-full bg-gradient-to-r from-mysql-primary to-mysql-accent rounded-full\"\n                />\n              </div>\n              <div className=\"text-center mt-3 text-sm text-mysql-text-light\">\n                {Math.round(progress)}%\n              </div>\n            </motion.div>\n\n            {/* 加载消息 */}\n            <motion.div\n              initial={{ y: 10, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.6, ease: \"easeOut\" }}\n              className=\"flex items-center space-x-2 text-mysql-text-light\"\n            >\n              <Loader2 className=\"w-4 h-4 animate-spin\" />\n              <span className=\"text-sm\">{message}</span>\n            </motion.div>\n          </div>\n\n          {/* 背景装饰 */}\n          <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n            {/* 浮动圆点 */}\n            {[...Array(6)].map((_, i) => (\n              <motion.div\n                key={i}\n                initial={{ \n                  x: Math.random() * window.innerWidth,\n                  y: Math.random() * window.innerHeight,\n                  opacity: 0 \n                }}\n                animate={{ \n                  x: Math.random() * window.innerWidth,\n                  y: Math.random() * window.innerHeight,\n                  opacity: [0, 0.3, 0] \n                }}\n                transition={{ \n                  duration: 4 + Math.random() * 2,\n                  repeat: Infinity,\n                  delay: i * 0.5,\n                  ease: \"easeInOut\" \n                }}\n                className=\"absolute w-2 h-2 bg-mysql-primary rounded-full\"\n              />\n            ))}\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n\n// 简化版加载器\ninterface SimpleLoaderProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: string;\n  className?: string;\n}\n\nexport function SimpleLoader({\n  size = 'md',\n  color = 'mysql-primary',\n  className,\n}: SimpleLoaderProps) {\n  const sizeConfig = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n  };\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <motion.div\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n        className={cn(\n          'border-2 border-transparent rounded-full',\n          `border-t-${color}`,\n          sizeConfig[size]\n        )}\n      />\n    </div>\n  );\n}\n\n// 骨架屏加载器\ninterface SkeletonLoaderProps {\n  lines?: number;\n  className?: string;\n}\n\nexport function SkeletonLoader({ lines = 3, className }: SkeletonLoaderProps) {\n  return (\n    <div className={cn('space-y-3', className)}>\n      {[...Array(lines)].map((_, i) => (\n        <motion.div\n          key={i}\n          initial={{ opacity: 0.3 }}\n          animate={{ opacity: [0.3, 0.7, 0.3] }}\n          transition={{ \n            duration: 1.5, \n            repeat: Infinity, \n            delay: i * 0.1,\n            ease: \"easeInOut\" \n          }}\n          className={cn(\n            'h-4 bg-mysql-primary-light rounded',\n            i === 0 && 'w-3/4',\n            i === 1 && 'w-full',\n            i === 2 && 'w-2/3'\n          )}\n        />\n      ))}\n    </div>\n  );\n}\n\n// 内容加载器\ninterface ContentLoaderProps {\n  isLoading: boolean;\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n  className?: string;\n}\n\nexport function ContentLoader({\n  isLoading,\n  children,\n  fallback,\n  className,\n}: ContentLoaderProps) {\n  return (\n    <div className={className}>\n      <AnimatePresence mode=\"wait\">\n        {isLoading ? (\n          <motion.div\n            key=\"loading\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {fallback || <SkeletonLoader />}\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"content\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, ease: \"easeOut\" }}\n          >\n            {children}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n\n// 页面过渡加载器\nexport function PageTransitionLoader() {\n  return (\n    <motion.div\n      initial={{ scaleX: 0 }}\n      animate={{ scaleX: 1 }}\n      exit={{ scaleX: 0 }}\n      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n      className=\"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-mysql-primary to-mysql-accent z-50 origin-left\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA,wBAAwB;AACxB,uBAAuB;AAEvB;AACA;AAAA;AACA;AAAA;AACA;AARA;;;;;;AAgBe,SAAS,WAAW,EACjC,YAAY,IAAI,EAChB,UAAU,QAAQ,EAClB,SAAS,EACO;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,WAAW,YAAY;YAC3B,YAAY,CAAA;gBACV,IAAI,QAAQ,IAAI,OAAO;gBACvB,OAAO,OAAO,KAAK,MAAM,KAAK;YAChC;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,YAAY;QACd;IACF,GAAG;QAAC;KAAU;IAEd,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yEACA;;8BAIF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;4BAC7C,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAS;kDAE5D,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAAE,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCAAC;oCACtD,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;oCAC/D,WAAU;;;;;;;;;;;;sCAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;4BACzD,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;4BACzD,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAK;wCACvB,SAAS;4CAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wCAAC;wCACjC,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAU;wCAC7C,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;4BACzD,WAAU;;8CAEV,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;8BAEZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;gCACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;gCACrC,SAAS;4BACX;4BACA,SAAS;gCACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;gCACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;gCACrC,SAAS;oCAAC;oCAAG;oCAAK;iCAAE;4BACtB;4BACA,YAAY;gCACV,UAAU,IAAI,KAAK,MAAM,KAAK;gCAC9B,QAAQ;gCACR,OAAO,IAAI;gCACX,MAAM;4BACR;4BACA,WAAU;2BAjBL;;;;;;;;;;;;;;;;;;;;;AAyBrB;AASO,SAAS,aAAa,EAC3B,OAAO,IAAI,EACX,QAAQ,eAAe,EACvB,SAAS,EACS;IAClB,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,QAAQ;YAAI;YACvB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAS;YAC5D,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,4CACA,CAAC,SAAS,EAAE,OAAO,EACnB,UAAU,CAAC,KAAK;;;;;;;;;;;AAK1B;AAQO,SAAS,eAAe,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAuB;IAC1E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;eAAI,MAAM;SAAO,CAAC,GAAG,CAAC,CAAC,GAAG,kBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;gBAAI;gBACxB,SAAS;oBAAE,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAAC;gBACpC,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,IAAI;oBACX,MAAM;gBACR;gBACA,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sCACA,MAAM,KAAK,SACX,MAAM,KAAK,UACX,MAAM,KAAK;eAbR;;;;;;;;;;AAmBf;AAUO,SAAS,cAAc,EAC5B,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACU;IACnB,qBACE,8OAAC;QAAI,WAAW;kBACd,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,MAAK;sBACnB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,YAAY;oBAAE,UAAU;gBAAI;0BAE3B,0BAAY,8OAAC;;;;;eANV;;;;qCASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE5C;eALG;;;;;;;;;;;;;;;AAWhB;AAGO,SAAS;IACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,QAAQ;QAAE;QACrB,SAAS;YAAE,QAAQ;QAAE;QACrB,MAAM;YAAE,QAAQ;QAAE;QAClB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;QAC/C,WAAU;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/StateComponents.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 统一状态组件\n// 提供加载、错误、空状态等统一的用户反馈组件\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  AlertCircle, \n  Loader2, \n  RefreshCw, \n  Database,\n  FileX,\n  Wifi,\n  WifiOff\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from './Button';\nimport { SkeletonLoader } from './PageLoader';\n\n// 基础状态组件Props\ninterface BaseStateProps {\n  className?: string;\n}\n\n// 加载状态组件Props\ninterface LoadingStateProps extends BaseStateProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'spinner' | 'skeleton' | 'pulse';\n  itemCount?: number;\n}\n\n// 错误状态组件Props\ninterface ErrorStateProps extends BaseStateProps {\n  error?: string;\n  title?: string;\n  onRetry?: () => void;\n  retryLabel?: string;\n  variant?: 'network' | 'server' | 'generic';\n}\n\n// 空状态组件Props\ninterface EmptyStateProps extends BaseStateProps {\n  title?: string;\n  message?: string;\n  icon?: React.ReactNode;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n}\n\n/**\n * 加载状态组件\n */\nexport function LoadingState({\n  message = '正在加载...',\n  size = 'md',\n  variant = 'spinner',\n  itemCount = 6,\n  className\n}: LoadingStateProps) {\n  const sizeConfig = {\n    sm: { spinner: 'w-4 h-4', text: 'text-sm', spacing: 'space-y-2' },\n    md: { spinner: 'w-6 h-6', text: 'text-base', spacing: 'space-y-4' },\n    lg: { spinner: 'w-8 h-8', text: 'text-lg', spacing: 'space-y-6' }\n  };\n\n  if (variant === 'skeleton') {\n    return (\n      <div className={cn('space-y-4', className)}>\n        <SkeletonLoader lines={1} className=\"h-8 w-48\" />\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {[...Array(itemCount)].map((_, i) => (\n            <SkeletonLoader key={i} lines={1} className=\"h-64 rounded-lg\" />\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex items-center justify-center py-12', className)}>\n        <motion.div\n          animate={{ scale: [1, 1.1, 1], opacity: [0.5, 1, 0.5] }}\n          transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n          className=\"flex flex-col items-center space-y-4\"\n        >\n          <div className=\"w-16 h-16 bg-mysql-primary rounded-full flex items-center justify-center\">\n            <Database className=\"w-8 h-8 text-white\" />\n          </div>\n          <p className={cn('text-mysql-text-light', sizeConfig[size].text)}>\n            {message}\n          </p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center py-12', className)}>\n      <div className={cn('flex items-center', sizeConfig[size].spacing)}>\n        <Loader2 className={cn('text-mysql-primary animate-spin', sizeConfig[size].spinner)} />\n        <span className={cn('text-mysql-text-light', sizeConfig[size].text)}>\n          {message}\n        </span>\n      </div>\n    </div>\n  );\n}\n\n/**\n * 错误状态组件\n */\nexport function ErrorState({\n  error = '加载失败',\n  title = '出现错误',\n  onRetry,\n  retryLabel = '重试',\n  variant = 'generic',\n  className\n}: ErrorStateProps) {\n  const getIcon = () => {\n    switch (variant) {\n      case 'network':\n        return <WifiOff className=\"w-12 h-12 text-red-500\" />;\n      case 'server':\n        return <Database className=\"w-12 h-12 text-red-500\" />;\n      default:\n        return <AlertCircle className=\"w-12 h-12 text-red-500\" />;\n    }\n  };\n\n  const getTitle = () => {\n    switch (variant) {\n      case 'network':\n        return '网络连接失败';\n      case 'server':\n        return '服务器错误';\n      default:\n        return title;\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={cn('flex flex-col items-center justify-center py-12', className)}\n    >\n      {getIcon()}\n      <h3 className=\"text-lg font-semibold text-mysql-text mb-2 mt-4\">\n        {getTitle()}\n      </h3>\n      <p className=\"text-mysql-text-light mb-6 text-center max-w-md\">\n        {error}\n      </p>\n      {onRetry && (\n        <Button\n          onClick={onRetry}\n          variant=\"primary\"\n          size=\"md\"\n          icon={<RefreshCw className=\"w-4 h-4\" />}\n        >\n          {retryLabel}\n        </Button>\n      )}\n    </motion.div>\n  );\n}\n\n/**\n * 空状态组件\n */\nexport function EmptyState({\n  title = '暂无数据',\n  message = '当前没有可显示的内容',\n  icon,\n  action,\n  className\n}: EmptyStateProps) {\n  const defaultIcon = <FileX className=\"w-12 h-12 text-mysql-text-light\" />;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={cn('flex flex-col items-center justify-center py-12', className)}\n    >\n      {icon || defaultIcon}\n      <h3 className=\"text-lg font-semibold text-mysql-text mb-2 mt-4\">\n        {title}\n      </h3>\n      <p className=\"text-mysql-text-light mb-6 text-center max-w-md\">\n        {message}\n      </p>\n      {action && (\n        <Button\n          onClick={action.onClick}\n          variant=\"primary\"\n          size=\"md\"\n        >\n          {action.label}\n        </Button>\n      )}\n    </motion.div>\n  );\n}\n\n/**\n * 网络状态指示器\n */\nexport function NetworkStatus({ isOnline = true }: { isOnline?: boolean }) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      className={cn(\n        'fixed bottom-4 right-4 flex items-center px-3 py-2 rounded-lg shadow-lg z-50',\n        isOnline\n          ? 'bg-green-500 text-white'\n          : 'bg-red-500 text-white'\n      )}\n    >\n      {isOnline ? (\n        <Wifi className=\"w-4 h-4 mr-2\" />\n      ) : (\n        <WifiOff className=\"w-4 h-4 mr-2\" />\n      )}\n      <span className=\"text-sm\">\n        {isOnline ? '已连接' : '网络断开'}\n      </span>\n    </motion.div>\n  );\n}\n\n// 组合状态组件Props\ninterface StateWrapperProps {\n  loading: boolean;\n  error?: string;\n  isEmpty?: boolean;\n  children: React.ReactNode;\n  loadingProps?: Partial<LoadingStateProps>;\n  errorProps?: Partial<ErrorStateProps>;\n  emptyProps?: Partial<EmptyStateProps>;\n  className?: string;\n}\n\n/**\n * 状态包装器组件 - 统一处理加载、错误、空状态\n */\nexport function StateWrapper({\n  loading,\n  error,\n  isEmpty = false,\n  children,\n  loadingProps = {},\n  errorProps = {},\n  emptyProps = {},\n  className\n}: StateWrapperProps) {\n  if (loading) {\n    return <LoadingState {...loadingProps} className={className} />;\n  }\n\n  if (error) {\n    return <ErrorState error={error} {...errorProps} className={className} />;\n  }\n\n  if (isEmpty) {\n    return <EmptyState {...emptyProps} className={className} />;\n  }\n\n  return <div className={className}>{children}</div>;\n}\n\n// 侧边栏骨架屏\nexport function SidebarSkeleton({ itemCount = 6 }: { itemCount?: number }) {\n  return (\n    <div className=\"p-4 space-y-2\">\n      {[...Array(itemCount)].map((_, i) => (\n        <div key={i} className=\"space-y-1\">\n          <SkeletonLoader lines={1} className=\"h-10 rounded-lg\" />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n// 卡片网格骨架屏\nexport function CardGridSkeleton({ itemCount = 6 }: { itemCount?: number }) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {[...Array(itemCount)].map((_, i) => (\n        <div key={i} className=\"space-y-3\">\n          <SkeletonLoader lines={1} className=\"h-48 rounded-lg\" />\n          <SkeletonLoader lines={2} />\n        </div>\n      ))}\n    </div>\n  );\n}\n\n// 列表骨架屏\nexport function ListSkeleton({ itemCount = 5 }: { itemCount?: number }) {\n  return (\n    <div className=\"space-y-4\">\n      {[...Array(itemCount)].map((_, i) => (\n        <div key={i} className=\"flex items-center space-x-4\">\n          <SkeletonLoader lines={1} className=\"w-12 h-12 rounded-full\" />\n          <div className=\"flex-1\">\n            <SkeletonLoader lines={2} />\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAlBA;;;;;;;AAwDO,SAAS,aAAa,EAC3B,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,CAAC,EACb,SAAS,EACS;IAClB,MAAM,aAAa;QACjB,IAAI;YAAE,SAAS;YAAW,MAAM;YAAW,SAAS;QAAY;QAChE,IAAI;YAAE,SAAS;YAAW,MAAM;YAAa,SAAS;QAAY;QAClE,IAAI;YAAE,SAAS;YAAW,MAAM;YAAW,SAAS;QAAY;IAClE;IAEA,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,8OAAC,oJAAA,CAAA,iBAAc;oBAAC,OAAO;oBAAG,WAAU;;;;;;8BACpC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAW,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7B,8OAAC,oJAAA,CAAA,iBAAc;4BAAS,OAAO;4BAAG,WAAU;2BAAvB;;;;;;;;;;;;;;;;IAK/B;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;sBAC3D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAAE,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBAAC;gBACtD,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;gBAC/D,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;wBAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,UAAU,CAAC,KAAK,CAAC,IAAI;kCAC5D;;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;kBAC3D,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB,UAAU,CAAC,KAAK,CAAC,OAAO;;8BAC9D,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC,UAAU,CAAC,KAAK,CAAC,OAAO;;;;;;8BAClF,8OAAC;oBAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,UAAU,CAAC,KAAK,CAAC,IAAI;8BAC/D;;;;;;;;;;;;;;;;;AAKX;AAKO,SAAS,WAAW,EACzB,QAAQ,MAAM,EACd,QAAQ,MAAM,EACd,OAAO,EACP,aAAa,IAAI,EACjB,UAAU,SAAS,EACnB,SAAS,EACO;IAChB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,WAAW;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;;YAEhE;0BACD,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAEF,yBACC,8OAAC,gJAAA,CAAA,UAAM;gBACL,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,oBAAM,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;0BAE1B;;;;;;;;;;;;AAKX;AAKO,SAAS,WAAW,EACzB,QAAQ,MAAM,EACd,UAAU,YAAY,EACtB,IAAI,EACJ,MAAM,EACN,SAAS,EACO;IAChB,MAAM,4BAAc,8OAAC,wMAAA,CAAA,QAAK;QAAC,WAAU;;;;;;IAErC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;;YAEhE,QAAQ;0BACT,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAEF,wBACC,8OAAC,gJAAA,CAAA,UAAM;gBACL,SAAS,OAAO,OAAO;gBACvB,SAAQ;gBACR,MAAK;0BAEJ,OAAO,KAAK;;;;;;;;;;;;AAKvB;AAKO,SAAS,cAAc,EAAE,WAAW,IAAI,EAA0B;IACvE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,4BACA;;YAGL,yBACC,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAErB,8OAAC;gBAAK,WAAU;0BACb,WAAW,QAAQ;;;;;;;;;;;;AAI5B;AAiBO,SAAS,aAAa,EAC3B,OAAO,EACP,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,EACR,eAAe,CAAC,CAAC,EACjB,aAAa,CAAC,CAAC,EACf,aAAa,CAAC,CAAC,EACf,SAAS,EACS;IAClB,IAAI,SAAS;QACX,qBAAO,8OAAC;YAAc,GAAG,YAAY;YAAE,WAAW;;;;;;IACpD;IAEA,IAAI,OAAO;QACT,qBAAO,8OAAC;YAAW,OAAO;YAAQ,GAAG,UAAU;YAAE,WAAW;;;;;;IAC9D;IAEA,IAAI,SAAS;QACX,qBAAO,8OAAC;YAAY,GAAG,UAAU;YAAE,WAAW;;;;;;IAChD;IAEA,qBAAO,8OAAC;QAAI,WAAW;kBAAY;;;;;;AACrC;AAGO,SAAS,gBAAgB,EAAE,YAAY,CAAC,EAA0B;IACvE,qBACE,8OAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAW,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7B,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC,oJAAA,CAAA,iBAAc;oBAAC,OAAO;oBAAG,WAAU;;;;;;eAD5B;;;;;;;;;;AAMlB;AAGO,SAAS,iBAAiB,EAAE,YAAY,CAAC,EAA0B;IACxE,qBACE,8OAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAW,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7B,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC,oJAAA,CAAA,iBAAc;wBAAC,OAAO;wBAAG,WAAU;;;;;;kCACpC,8OAAC,oJAAA,CAAA,iBAAc;wBAAC,OAAO;;;;;;;eAFf;;;;;;;;;;AAOlB;AAGO,SAAS,aAAa,EAAE,YAAY,CAAC,EAA0B;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAW,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7B,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC,oJAAA,CAAA,iBAAc;wBAAC,OAAO;wBAAG,WAAU;;;;;;kCACpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oJAAA,CAAA,iBAAc;4BAAC,OAAO;;;;;;;;;;;;eAHjB;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 2648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/hooks/useSearchHistory.ts"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索历史管理Hook\n// 提供本地搜索历史存储、去重、排序和清理功能\n\nimport { useState, useEffect, useCallback } from 'react';\n\n// 搜索历史项类型\ninterface SearchHistoryItem {\n  query: string;\n  timestamp: number;\n  count: number; // 搜索次数\n}\n\n// Hook配置选项\ninterface UseSearchHistoryOptions {\n  maxItems?: number; // 最大保存数量\n  storageKey?: string; // 本地存储键名\n  enableFrequencyTracking?: boolean; // 是否启用频率统计\n  autoCleanup?: boolean; // 是否自动清理过期项\n  maxAge?: number; // 最大保存时间（毫秒）\n}\n\n// Hook返回值类型\ninterface UseSearchHistoryReturn {\n  // 历史记录\n  history: string[];\n  recentHistory: string[];\n  popularHistory: string[];\n  \n  // 操作方法\n  addToHistory: (query: string) => void;\n  removeFromHistory: (query: string) => void;\n  clearHistory: () => void;\n  getSearchCount: (query: string) => number;\n  \n  // 统计信息\n  totalSearches: number;\n  uniqueSearches: number;\n  \n  // 工具方法\n  exportHistory: () => string;\n  importHistory: (data: string) => boolean;\n}\n\n// 默认配置\nconst DEFAULT_OPTIONS: UseSearchHistoryOptions = {\n  maxItems: 50,\n  storageKey: 'mysqlai_search_history',\n  enableFrequencyTracking: true,\n  autoCleanup: true,\n  maxAge: 30 * 24 * 60 * 60 * 1000 // 30天\n};\n\nexport function useSearchHistory(options: UseSearchHistoryOptions = {}): UseSearchHistoryReturn {\n  const opts = { ...DEFAULT_OPTIONS, ...options };\n  const [historyItems, setHistoryItems] = useState<SearchHistoryItem[]>([]);\n\n  // 从本地存储加载历史记录\n  const loadHistory = useCallback(() => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      const stored = localStorage.getItem(opts.storageKey!);\n      if (stored) {\n        const parsed: SearchHistoryItem[] = JSON.parse(stored);\n        \n        // 自动清理过期项\n        if (opts.autoCleanup) {\n          const now = Date.now();\n          const validItems = parsed.filter(item => \n            now - item.timestamp <= opts.maxAge!\n          );\n          setHistoryItems(validItems);\n          \n          // 如果清理了项目，更新存储\n          if (validItems.length !== parsed.length) {\n            localStorage.setItem(opts.storageKey!, JSON.stringify(validItems));\n          }\n        } else {\n          setHistoryItems(parsed);\n        }\n      }\n    } catch (error) {\n      console.error('加载搜索历史失败:', error);\n      setHistoryItems([]);\n    }\n  }, [opts.storageKey, opts.autoCleanup, opts.maxAge]);\n\n  // 保存历史记录到本地存储\n  const saveHistory = useCallback((items: SearchHistoryItem[]) => {\n    try {\n      if (typeof window === 'undefined') return;\n      \n      localStorage.setItem(opts.storageKey!, JSON.stringify(items));\n    } catch (error) {\n      console.error('保存搜索历史失败:', error);\n    }\n  }, [opts.storageKey]);\n\n  // 初始化加载\n  useEffect(() => {\n    loadHistory();\n  }, [loadHistory]);\n\n  // 添加搜索记录\n  const addToHistory = useCallback((query: string) => {\n    if (!query || query.trim().length < 2) return;\n    \n    const trimmedQuery = query.trim();\n    const now = Date.now();\n    \n    setHistoryItems(prevItems => {\n      // 查找是否已存在\n      const existingIndex = prevItems.findIndex(item => \n        item.query.toLowerCase() === trimmedQuery.toLowerCase()\n      );\n      \n      let newItems: SearchHistoryItem[];\n      \n      if (existingIndex >= 0) {\n        // 更新现有项目\n        newItems = [...prevItems];\n        newItems[existingIndex] = {\n          ...newItems[existingIndex],\n          timestamp: now,\n          count: opts.enableFrequencyTracking ? newItems[existingIndex].count + 1 : 1\n        };\n      } else {\n        // 添加新项目\n        const newItem: SearchHistoryItem = {\n          query: trimmedQuery,\n          timestamp: now,\n          count: 1\n        };\n        newItems = [newItem, ...prevItems];\n      }\n      \n      // 限制数量\n      if (newItems.length > opts.maxItems!) {\n        newItems = newItems.slice(0, opts.maxItems!);\n      }\n      \n      // 按时间戳排序（最新的在前）\n      newItems.sort((a, b) => b.timestamp - a.timestamp);\n      \n      // 保存到本地存储\n      saveHistory(newItems);\n      \n      return newItems;\n    });\n  }, [opts.maxItems, opts.enableFrequencyTracking, saveHistory]);\n\n  // 删除搜索记录\n  const removeFromHistory = useCallback((query: string) => {\n    setHistoryItems(prevItems => {\n      const newItems = prevItems.filter(item => \n        item.query.toLowerCase() !== query.toLowerCase()\n      );\n      saveHistory(newItems);\n      return newItems;\n    });\n  }, [saveHistory]);\n\n  // 清空搜索历史\n  const clearHistory = useCallback(() => {\n    setHistoryItems([]);\n    saveHistory([]);\n  }, [saveHistory]);\n\n  // 获取搜索次数\n  const getSearchCount = useCallback((query: string) => {\n    const item = historyItems.find(item => \n      item.query.toLowerCase() === query.toLowerCase()\n    );\n    return item?.count || 0;\n  }, [historyItems]);\n\n  // 导出历史记录\n  const exportHistory = useCallback(() => {\n    return JSON.stringify({\n      version: '1.0',\n      timestamp: Date.now(),\n      data: historyItems\n    });\n  }, [historyItems]);\n\n  // 导入历史记录\n  const importHistory = useCallback((data: string) => {\n    try {\n      const parsed = JSON.parse(data);\n      if (parsed.data && Array.isArray(parsed.data)) {\n        setHistoryItems(parsed.data);\n        saveHistory(parsed.data);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('导入搜索历史失败:', error);\n      return false;\n    }\n  }, [saveHistory]);\n\n  // 计算派生数据\n  const history = historyItems.map(item => item.query);\n  \n  const recentHistory = historyItems\n    .sort((a, b) => b.timestamp - a.timestamp)\n    .slice(0, 10)\n    .map(item => item.query);\n  \n  const popularHistory = opts.enableFrequencyTracking\n    ? historyItems\n        .filter(item => item.count > 1)\n        .sort((a, b) => b.count - a.count)\n        .slice(0, 10)\n        .map(item => item.query)\n    : [];\n\n  const totalSearches = historyItems.reduce((sum, item) => sum + item.count, 0);\n  const uniqueSearches = historyItems.length;\n\n  return {\n    // 历史记录\n    history,\n    recentHistory,\n    popularHistory,\n    \n    // 操作方法\n    addToHistory,\n    removeFromHistory,\n    clearHistory,\n    getSearchCount,\n    \n    // 统计信息\n    totalSearches,\n    uniqueSearches,\n    \n    // 工具方法\n    exportHistory,\n    importHistory\n  };\n}\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B,wBAAwB;AAExB;AALA;;AA6CA,OAAO;AACP,MAAM,kBAA2C;IAC/C,UAAU;IACV,YAAY;IACZ,yBAAyB;IACzB,aAAa;IACb,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACzC;AAEO,SAAS,iBAAiB,UAAmC,CAAC,CAAC;IACpE,MAAM,OAAO;QAAE,GAAG,eAAe;QAAE,GAAG,OAAO;IAAC;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAExE,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,wCAAmC;;YAEnC,MAAM;QAoBR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,gBAAgB,EAAE;QACpB;IACF,GAAG;QAAC,KAAK,UAAU;QAAE,KAAK,WAAW;QAAE,KAAK,MAAM;KAAC;IAEnD,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI;YACF,wCAAmC;;QAGrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF,GAAG;QAAC,KAAK,UAAU;KAAC;IAEpB,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;QAEvC,MAAM,eAAe,MAAM,IAAI;QAC/B,MAAM,MAAM,KAAK,GAAG;QAEpB,gBAAgB,CAAA;YACd,UAAU;YACV,MAAM,gBAAgB,UAAU,SAAS,CAAC,CAAA,OACxC,KAAK,KAAK,CAAC,WAAW,OAAO,aAAa,WAAW;YAGvD,IAAI;YAEJ,IAAI,iBAAiB,GAAG;gBACtB,SAAS;gBACT,WAAW;uBAAI;iBAAU;gBACzB,QAAQ,CAAC,cAAc,GAAG;oBACxB,GAAG,QAAQ,CAAC,cAAc;oBAC1B,WAAW;oBACX,OAAO,KAAK,uBAAuB,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI;gBAC5E;YACF,OAAO;gBACL,QAAQ;gBACR,MAAM,UAA6B;oBACjC,OAAO;oBACP,WAAW;oBACX,OAAO;gBACT;gBACA,WAAW;oBAAC;uBAAY;iBAAU;YACpC;YAEA,OAAO;YACP,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,EAAG;gBACpC,WAAW,SAAS,KAAK,CAAC,GAAG,KAAK,QAAQ;YAC5C;YAEA,gBAAgB;YAChB,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;YAEjD,UAAU;YACV,YAAY;YAEZ,OAAO;QACT;IACF,GAAG;QAAC,KAAK,QAAQ;QAAE,KAAK,uBAAuB;QAAE;KAAY;IAE7D,SAAS;IACT,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,gBAAgB,CAAA;YACd,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,OAChC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;YAEhD,YAAY;YACZ,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,gBAAgB,EAAE;QAClB,YAAY,EAAE;IAChB,GAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,MAAM,OAAO,aAAa,IAAI,CAAC,CAAA,OAC7B,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;QAEhD,OAAO,MAAM,SAAS;IACxB,GAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,OAAO,KAAK,SAAS,CAAC;YACpB,SAAS;YACT,WAAW,KAAK,GAAG;YACnB,MAAM;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;gBAC7C,gBAAgB,OAAO,IAAI;gBAC3B,YAAY,OAAO,IAAI;gBACvB,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,SAAS;IACT,MAAM,UAAU,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAEnD,MAAM,gBAAgB,aACnB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAEzB,MAAM,iBAAiB,KAAK,uBAAuB,GAC/C,aACG,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,GAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,IACT,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,IACzB,EAAE;IAEN,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3E,MAAM,iBAAiB,aAAa,MAAM;IAE1C,OAAO;QACL,OAAO;QACP;QACA;QACA;QAEA,OAAO;QACP;QACA;QACA;QACA;QAEA,OAAO;QACP;QACA;QAEA,OAAO;QACP;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchHistory.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索历史界面组件\n// 提供搜索历史显示、管理和快速重新搜索功能\n\nimport React, { useState } from 'react';\nimport { useSearchHistory } from '@/hooks/useSearchHistory';\nimport {\n  Clock,\n  TrendingUp,\n  X,\n  Trash2,\n  RotateCcw,\n  BarChart3\n} from 'lucide-react';\n\n// 组件属性\ninterface SearchHistoryProps {\n  onSearchSelect: (query: string) => void;\n  onClose?: () => void;\n  className?: string;\n  showStats?: boolean;\n  maxRecentItems?: number;\n  maxPopularItems?: number;\n}\n\n// 历史项组件属性\ninterface HistoryItemProps {\n  query: string;\n  count?: number;\n  timestamp?: number;\n  onSelect: (query: string) => void;\n  onRemove: (query: string) => void;\n  showCount?: boolean;\n}\n\n// 历史项组件\nfunction HistoryItem({\n  query,\n  count,\n  timestamp,\n  onSelect,\n  onRemove,\n  showCount = false\n}: HistoryItemProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const formatTimestamp = (ts?: number) => {\n    if (!ts) return '';\n    const date = new Date(ts);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) return '今天';\n    if (diffDays === 1) return '昨天';\n    if (diffDays < 7) return `${diffDays}天前`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;\n    return `${Math.floor(diffDays / 30)}月前`;\n  };\n\n  return (\n    <div\n      className=\"flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 group\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <button\n        type=\"button\"\n        onClick={() => onSelect(query)}\n        className=\"flex-1 flex items-center gap-3 text-left min-w-0\"\n      >\n        <Clock className=\"w-4 h-4 text-gray-400 flex-shrink-0\" />\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"font-medium text-gray-900 truncate\">\n            {query}\n          </div>\n          <div className=\"flex items-center gap-2 text-xs text-gray-500 mt-1\">\n            {timestamp && (\n              <span>{formatTimestamp(timestamp)}</span>\n            )}\n            {showCount && count && count > 1 && (\n              <>\n                <span>•</span>\n                <span>{count}次搜索</span>\n              </>\n            )}\n          </div>\n        </div>\n      </button>\n\n      {isHovered && (\n        <button\n          type=\"button\"\n          onClick={(e) => {\n            e.stopPropagation();\n            onRemove(query);\n          }}\n          className=\"p-1 text-gray-400 hover:text-red-500 transition-colors duration-150\"\n          title=\"删除此搜索记录\"\n        >\n          <X className=\"w-4 h-4\" />\n        </button>\n      )}\n    </div>\n  );\n}\n\nexport default function SearchHistory({\n  onSearchSelect,\n  onClose,\n  className = '',\n  showStats = true,\n  maxRecentItems = 8,\n  maxPopularItems = 5\n}: SearchHistoryProps) {\n  const {\n    recentHistory,\n    popularHistory,\n    totalSearches,\n    uniqueSearches,\n    removeFromHistory,\n    clearHistory,\n    getSearchCount\n  } = useSearchHistory();\n\n  const [activeTab, setActiveTab] = useState<'recent' | 'popular'>('recent');\n  const [showClearConfirm, setShowClearConfirm] = useState(false);\n\n  // 处理搜索选择\n  const handleSearchSelect = (query: string) => {\n    onSearchSelect(query);\n    onClose?.();\n  };\n\n  // 处理删除单个记录\n  const handleRemoveItem = (query: string) => {\n    removeFromHistory(query);\n  };\n\n  // 处理清空所有记录\n  const handleClearAll = () => {\n    if (showClearConfirm) {\n      clearHistory();\n      setShowClearConfirm(false);\n      onClose?.();\n    } else {\n      setShowClearConfirm(true);\n    }\n  };\n\n  // 取消清空确认\n  const handleCancelClear = () => {\n    setShowClearConfirm(false);\n  };\n\n  const hasHistory = recentHistory.length > 0 || popularHistory.length > 0;\n\n  return (\n    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg ${className}`}>\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">搜索历史</h3>\n        <div className=\"flex items-center gap-2\">\n          {hasHistory && (\n            <button\n              type=\"button\"\n              onClick={handleClearAll}\n              className={`px-3 py-1 text-sm rounded-md transition-colors duration-150 ${\n                showClearConfirm\n                  ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                  : 'text-gray-500 hover:text-red-600 hover:bg-red-50'\n              }`}\n            >\n              {showClearConfirm ? (\n                <div className=\"flex items-center gap-2\">\n                  <span>确认清空?</span>\n                  <button\n                    type=\"button\"\n                    onClick={handleCancelClear}\n                    className=\"text-gray-500 hover:text-gray-700\"\n                  >\n                    取消\n                  </button>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1\">\n                  <Trash2 className=\"w-4 h-4\" />\n                  <span>清空</span>\n                </div>\n              )}\n            </button>\n          )}\n          {onClose && (\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-150\"\n              title=\"关闭搜索历史\"\n              aria-label=\"关闭搜索历史\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      {showStats && hasHistory && (\n        <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n          <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n            <div className=\"flex items-center gap-1\">\n              <BarChart3 className=\"w-4 h-4\" />\n              <span>总搜索: {totalSearches}次</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <RotateCcw className=\"w-4 h-4\" />\n              <span>不同查询: {uniqueSearches}个</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 标签页 */}\n      {hasHistory && (\n        <div className=\"flex border-b border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={() => setActiveTab('recent')}\n            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${\n              activeTab === 'recent'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <div className=\"flex items-center justify-center gap-2\">\n              <Clock className=\"w-4 h-4\" />\n              <span>最近搜索</span>\n              {recentHistory.length > 0 && (\n                <span className=\"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full\">\n                  {recentHistory.length}\n                </span>\n              )}\n            </div>\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => setActiveTab('popular')}\n            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${\n              activeTab === 'popular'\n                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <div className=\"flex items-center justify-center gap-2\">\n              <TrendingUp className=\"w-4 h-4\" />\n              <span>热门搜索</span>\n              {popularHistory.length > 0 && (\n                <span className=\"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full\">\n                  {popularHistory.length}\n                </span>\n              )}\n            </div>\n          </button>\n        </div>\n      )}\n\n      {/* 内容区域 */}\n      <div className=\"p-4\">\n        {!hasHistory ? (\n          <div className=\"text-center py-8\">\n            <Clock className=\"w-12 h-12 text-gray-300 mx-auto mb-3\" />\n            <p className=\"text-gray-500 text-sm\">暂无搜索历史</p>\n            <p className=\"text-gray-400 text-xs mt-1\">开始搜索后，历史记录会显示在这里</p>\n          </div>\n        ) : (\n          <div className=\"space-y-1\">\n            {activeTab === 'recent' && (\n              <>\n                {recentHistory.slice(0, maxRecentItems).map((query, index) => (\n                  <HistoryItem\n                    key={`recent-${query}-${index}`}\n                    query={query}\n                    count={getSearchCount(query)}\n                    onSelect={handleSearchSelect}\n                    onRemove={handleRemoveItem}\n                    showCount={true}\n                  />\n                ))}\n                {recentHistory.length > maxRecentItems && (\n                  <div className=\"text-center py-2\">\n                    <span className=\"text-xs text-gray-500\">\n                      还有 {recentHistory.length - maxRecentItems} 条记录...\n                    </span>\n                  </div>\n                )}\n              </>\n            )}\n\n            {activeTab === 'popular' && (\n              <>\n                {popularHistory.length === 0 ? (\n                  <div className=\"text-center py-4\">\n                    <TrendingUp className=\"w-8 h-8 text-gray-300 mx-auto mb-2\" />\n                    <p className=\"text-gray-500 text-sm\">暂无热门搜索</p>\n                    <p className=\"text-gray-400 text-xs mt-1\">多次搜索相同内容后会显示在这里</p>\n                  </div>\n                ) : (\n                  popularHistory.slice(0, maxPopularItems).map((query, index) => (\n                    <HistoryItem\n                      key={`popular-${query}-${index}`}\n                      query={query}\n                      count={getSearchCount(query)}\n                      onSelect={handleSearchSelect}\n                      onRemove={handleRemoveItem}\n                      showCount={true}\n                    />\n                  ))\n                )}\n              </>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,uBAAuB;AAEvB;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;AAoCA,QAAQ;AACR,SAAS,YAAY,EACnB,KAAK,EACL,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EACA;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,OAAO;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;QACxC,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QACzD,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC;IACzC;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,SAAS;gBACxB,WAAU;;kCAEV,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,8OAAC;gCAAI,WAAU;;oCACZ,2BACC,8OAAC;kDAAM,gBAAgB;;;;;;oCAExB,aAAa,SAAS,QAAQ,mBAC7B;;0DACE,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtB,2BACC,8OAAC;gBACC,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,SAAS;gBACX;gBACA,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKvB;AAEe,SAAS,cAAc,EACpC,cAAc,EACd,OAAO,EACP,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,iBAAiB,CAAC,EAClB,kBAAkB,CAAC,EACA;IACnB,MAAM,EACJ,aAAa,EACb,cAAc,EACd,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;IACpB;IAEA,WAAW;IACX,MAAM,iBAAiB;QACrB,IAAI,kBAAkB;YACpB;YACA,oBAAoB;YACpB;QACF,OAAO;YACL,oBAAoB;QACtB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,oBAAoB;IACtB;IAEA,MAAM,aAAa,cAAc,MAAM,GAAG,KAAK,eAAe,MAAM,GAAG;IAEvE,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAEjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ,4BACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAW,CAAC,4DAA4D,EACtE,mBACI,6CACA,oDACJ;0CAED,iCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;4BAKb,yBACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;gCACN,cAAW;0CAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOpB,aAAa,4BACZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;;wCAAK;wCAAM;wCAAc;;;;;;;;;;;;;sCAE5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;;wCAAK;wCAAO;wCAAe;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,oEAAoE,EAC9E,cAAc,WACV,wDACA,qCACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;gCACL,cAAc,MAAM,GAAG,mBACtB,8OAAC;oCAAK,WAAU;8CACb,cAAc,MAAM;;;;;;;;;;;;;;;;;kCAK7B,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,oEAAoE,EAC9E,cAAc,YACV,wDACA,qCACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;8CAAK;;;;;;gCACL,eAAe,MAAM,GAAG,mBACvB,8OAAC;oCAAK,WAAU;8CACb,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCACrC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,0BACb;;gCACG,cAAc,KAAK,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAClD,8OAAC;wCAEC,OAAO;wCACP,OAAO,eAAe;wCACtB,UAAU;wCACV,UAAU;wCACV,WAAW;uCALN,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,OAAO;;;;;gCAQlC,cAAc,MAAM,GAAG,gCACtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;4CAAwB;4CAClC,cAAc,MAAM,GAAG;4CAAe;;;;;;;;;;;;;;wBAOnD,cAAc,2BACb;sCACG,eAAe,MAAM,KAAK,kBACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;uCAG5C,eAAe,KAAK,CAAC,GAAG,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBACnD,8OAAC;oCAEC,OAAO;oCACP,OAAO,eAAe;oCACtB,UAAU;oCACV,UAAU;oCACV,WAAW;mCALN,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAgBtD", "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - KnowledgeSidebarWrapper客户端组件包装器\n// 用于解决hydration mismatch问题，确保KnowledgeSidebar只在客户端渲染\n\nimport React, { useState, useMemo, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Search, Clock } from 'lucide-react';\nimport { useNavigation } from '@/contexts/NavigationContext';\nimport { getKnowledgeItemsByCategory } from '@/lib/knowledge';\nimport { useKnowledgeData } from '@/hooks/useKnowledgeData';\nimport { mapDatabaseCategoriesToFrontend, getIconEmoji } from '@/lib/utils';\nimport { hybridSearchHistory } from '@/lib/api/search-history';\nimport { SidebarSkeleton, ErrorState, LoadingState } from '@/components/ui/StateComponents';\nimport SearchHistory from './SearchHistory';\n\n\n// 直接实现KnowledgeSidebar，包含完整的交互功能\nfunction KnowledgeSidebar() {\n  const router = useRouter();\n  const navigation = useNavigation();\n\n  // 状态管理\n  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showSearchHistory, setShowSearchHistory] = useState(false);\n  const [searchFocused, setSearchFocused] = useState(false);\n  const [categoryArticles, setCategoryArticles] = useState<Record<string, any[]>>({});\n\n  // 使用 useKnowledgeData Hook 获取真实分类数据\n  const {\n    data: dbCategories,\n    loading,\n    error,\n    retry\n  } = useKnowledgeData('categories', {\n    includeStats: true\n  }, {\n    autoFetch: true, // 启用自动获取\n    cacheTime: 5 * 60 * 1000, // 5分钟缓存\n    debug: process.env.NODE_ENV === 'development'\n  });\n\n  // 转换数据格式并排序 - 确保类型正确\n  const categories = useMemo(() => {\n    if (!dbCategories || dbCategories.length === 0) return [];\n    return (dbCategories as any[])\n      .sort((a: any, b: any) => a.order_index - b.order_index);\n  }, [dbCategories]);\n\n\n\n  // 处理分类点击\n  const handleCategoryClick = async (categoryId: string) => {\n    // 切换展开状态\n    const newExpanded = new Set(expandedCategories);\n    const wasExpanded = newExpanded.has(categoryId);\n\n    if (wasExpanded) {\n      newExpanded.delete(categoryId);\n    } else {\n      newExpanded.add(categoryId);\n\n      // 如果是展开操作且还没有加载过该分类的文章，则获取文章数据\n      if (!categoryArticles[categoryId]) {\n        try {\n          const articles = await getKnowledgeItemsByCategory(categoryId);\n          setCategoryArticles(prev => ({\n            ...prev,\n            [categoryId]: articles.map(article => ({\n              id: article.id,\n              title: article.title\n            }))\n          }));\n        } catch (error) {\n          console.error(`获取分类 ${categoryId} 的文章失败:`, error);\n          // 即使获取文章失败，也要设置一个空数组，避免重复请求\n          setCategoryArticles(prev => ({\n            ...prev,\n            [categoryId]: []\n          }));\n        }\n      }\n    }\n\n    setExpandedCategories(newExpanded);\n\n    // 更新导航状态\n    navigation.setCurrentCategory(categoryId);\n\n    // 跳转到分类页面\n    router.push(`/knowledge/${categoryId}`);\n  };\n\n  // 处理知识点点击\n  const handleItemClick = (categoryId: string, itemId: string) => {\n    navigation.setCurrentItem(itemId);\n    router.push(`/knowledge/${categoryId}/${itemId}`);\n  };\n\n  // 处理搜索\n  const handleSearch = (query: string) => {\n    navigation.setSearchQuery(query);\n\n    // 如果查询不为空，保存到搜索历史\n    if (query.trim()) {\n      // 这里可以添加结果计数，暂时设为0\n      hybridSearchHistory.addRecord(query.trim(), 0);\n    }\n  };\n\n  // 处理搜索历史选择\n  const handleSearchHistorySelect = (query: string) => {\n    navigation.setSearchQuery(query);\n    setShowSearchHistory(false);\n  };\n\n  // 处理搜索框焦点\n  const handleSearchFocus = () => {\n    setSearchFocused(true);\n    setShowSearchHistory(true);\n  };\n\n  // 处理搜索框失焦\n  const handleSearchBlur = () => {\n    setSearchFocused(false);\n    // 延迟隐藏搜索历史，允许点击历史项\n    setTimeout(() => setShowSearchHistory(false), 200);\n  };\n\n  // 获取分类下的知识点\n  const getCategoryItems = (categoryId: string): { id: string; title: string }[] => {\n    return categoryArticles[categoryId] || [];\n  };\n\n  // 获取分类的文章数量（从API数据中获取）\n  const getCategoryArticleCount = (category: any): number => {\n    // 优先使用API返回的article_count\n    if (typeof category.article_count === 'number') {\n      return category.article_count;\n    }\n    // 如果没有，使用本地缓存的文章数据\n    return categoryArticles[category.id]?.length || 0;\n  };\n  return (\n    <div className=\"flex flex-col h-full bg-white\">\n      {/* 头部区域 */}\n      <div className=\"flex-shrink-0 p-4 border-b border-mysql-border\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-mysql-text\">知识库导航</h2>\n        </div>\n\n        {/* 搜索框 */}\n        <div className=\"relative\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-4 w-4 text-mysql-text-light\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"搜索知识点...\"\n              value={navigation.searchQuery}\n              onChange={(e) => handleSearch(e.target.value)}\n              onFocus={handleSearchFocus}\n              onBlur={handleSearchBlur}\n              className=\"w-full pl-10 pr-10 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary/30\"\n              data-testid=\"search-input\"\n            />\n            {navigation.searchQuery && (\n              <button\n                type=\"button\"\n                onClick={() => handleSearch('')}\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-mysql-text-light hover:text-mysql-text\"\n              >\n                ×\n              </button>\n            )}\n          </div>\n\n          {/* 搜索历史下拉 */}\n          {showSearchHistory && (\n            <div className=\"absolute top-full left-0 right-0 mt-1 z-50\">\n              <SearchHistory\n                onSelectQuery={handleSearchHistorySelect}\n                onClose={() => setShowSearchHistory(false)}\n                maxItems={5}\n                className=\"w-full\"\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {/* 加载状态 */}\n        {loading && <SidebarSkeleton itemCount={6} />}\n\n        {/* 错误状态 */}\n        {error && !loading && (\n          <div className=\"p-4\">\n            <ErrorState\n              error=\"分类数据加载失败\"\n              title=\"加载失败\"\n              onRetry={retry}\n              retryLabel=\"重试\"\n              variant=\"network\"\n              className=\"py-8\"\n            />\n          </div>\n        )}\n\n        {/* 正常内容 */}\n        {!loading && !error && (\n          <div className=\"p-4 space-y-2\" data-testid=\"knowledge-categories\">\n            {categories.map((category) => {\n              const isExpanded = expandedCategories.has(category.id);\n              const isActive = navigation.currentCategory === category.id;\n              const categoryItems = getCategoryItems(category.id);\n              const articleCount = getCategoryArticleCount(category);\n\n              return (\n                <div key={category.id} className=\"space-y-1\" data-testid=\"category-item\">\n                  {/* 分类标题 */}\n                  <button\n                    type=\"button\"\n                    onClick={() => handleCategoryClick(category.id)}\n                    className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-all duration-200 hover:bg-mysql-primary-light hover:text-mysql-primary ${\n                      isActive ? 'bg-mysql-primary text-white' : 'text-mysql-text'\n                    }`}\n                  >\n                    <span className=\"w-4 h-4 mr-2 flex-shrink-0\">{getIconEmoji(category.icon || 'FolderOpen')}</span>\n                    <span className=\"flex-1 font-medium\">{category.name}</span>\n                    {articleCount > 0 && (\n                      <span className=\"text-xs bg-mysql-primary-light text-mysql-primary px-2 py-1 rounded-full mr-2\">\n                        {articleCount}\n                      </span>\n                    )}\n                    <span className=\"w-4 h-4 flex-shrink-0\">\n                      {isExpanded ? '▼' : '▶'}\n                    </span>\n                  </button>\n\n                  {/* 分类下的知识点 */}\n                  {isExpanded && (\n                    <div className=\"ml-6 space-y-1\">\n                      {categoryItems.map((item) => (\n                        <button\n                          key={item.id}\n                          type=\"button\"\n                          onClick={() => handleItemClick(category.id, item.id)}\n                          className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors hover:bg-mysql-primary-light hover:text-mysql-primary ${\n                            navigation.currentItem === item.id\n                              ? 'bg-mysql-primary-light text-mysql-primary'\n                              : 'text-mysql-text-light'\n                          }`}\n                        >\n                          <span className=\"w-3 h-3 mr-2 flex-shrink-0\">📄</span>\n                          <span className=\"text-sm\">{item.title}</span>\n                        </button>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        )}\n      </div>\n\n      {/* 底部统计信息 */}\n      <div className=\"flex-shrink-0 p-4 border-t border-mysql-border bg-mysql-bg-light\">\n        {!loading && !error && (\n          <>\n            <div className=\"text-xs text-mysql-text-light text-center\">\n              共 {categories.length} 个分类，{categories.reduce((total, cat) => total + getCategoryArticleCount(cat), 0)} 个知识点\n            </div>\n            <div className=\"text-xs text-mysql-text-light text-center mt-1\">\n              MySQL知识库 v1.0\n            </div>\n          </>\n        )}\n        {loading && (\n          <LoadingState\n            message=\"加载中...\"\n            size=\"sm\"\n            variant=\"spinner\"\n            className=\"py-2\"\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default function KnowledgeSidebarWrapper() {\n  return <KnowledgeSidebar />;\n}\n"], "names": [], "mappings": ";;;;AAEA,+CAA+C;AAC/C,qDAAqD;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAiBA,iCAAiC;AACjC,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD;IAE/B,OAAO;IACP,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAEjF,oCAAoC;IACpC,MAAM,EACJ,MAAM,YAAY,EAClB,OAAO,EACP,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc;QACjC,cAAc;IAChB,GAAG;QACD,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,OAAO,oDAAyB;IAClC;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG,OAAO,EAAE;QACzD,OAAO,AAAC,aACL,IAAI,CAAC,CAAC,GAAQ,IAAW,EAAE,WAAW,GAAG,EAAE,WAAW;IAC3D,GAAG;QAAC;KAAa;IAIjB,SAAS;IACT,MAAM,sBAAsB,OAAO;QACjC,SAAS;QACT,MAAM,cAAc,IAAI,IAAI;QAC5B,MAAM,cAAc,YAAY,GAAG,CAAC;QAEpC,IAAI,aAAa;YACf,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;YAEhB,+BAA+B;YAC/B,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;gBACjC,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,qIAAA,CAAA,8BAA2B,AAAD,EAAE;oBACnD,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oCACrC,IAAI,QAAQ,EAAE;oCACd,OAAO,QAAQ,KAAK;gCACtB,CAAC;wBACH,CAAC;gBACH,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,EAAE;oBAC3C,4BAA4B;oBAC5B,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,WAAW,EAAE,EAAE;wBAClB,CAAC;gBACH;YACF;QACF;QAEA,sBAAsB;QAEtB,SAAS;QACT,WAAW,kBAAkB,CAAC;QAE9B,UAAU;QACV,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY;IACxC;IAEA,UAAU;IACV,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,WAAW,cAAc,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,QAAQ;IAClD;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,WAAW,cAAc,CAAC;QAE1B,kBAAkB;QAClB,IAAI,MAAM,IAAI,IAAI;YAChB,mBAAmB;YACnB,oJAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;QAC9C;IACF;IAEA,WAAW;IACX,MAAM,4BAA4B,CAAC;QACjC,WAAW,cAAc,CAAC;QAC1B,qBAAqB;IACvB;IAEA,UAAU;IACV,MAAM,oBAAoB;QACxB,iBAAiB;QACjB,qBAAqB;IACvB;IAEA,UAAU;IACV,MAAM,mBAAmB;QACvB,iBAAiB;QACjB,mBAAmB;QACnB,WAAW,IAAM,qBAAqB,QAAQ;IAChD;IAEA,YAAY;IACZ,MAAM,mBAAmB,CAAC;QACxB,OAAO,gBAAgB,CAAC,WAAW,IAAI,EAAE;IAC3C;IAEA,uBAAuB;IACvB,MAAM,0BAA0B,CAAC;QAC/B,0BAA0B;QAC1B,IAAI,OAAO,SAAS,aAAa,KAAK,UAAU;YAC9C,OAAO,SAAS,aAAa;QAC/B;QACA,mBAAmB;QACnB,OAAO,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,UAAU;IAClD;IACA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;;;;;;kCAIxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,WAAW,WAAW;wCAC7B,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,SAAS;wCACT,QAAQ;wCACR,WAAU;wCACV,eAAY;;;;;;oCAEb,WAAW,WAAW,kBACrB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;;4BAOJ,mCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,UAAa;oCACZ,eAAe;oCACf,SAAS,IAAM,qBAAqB;oCACpC,UAAU;oCACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;;oBAEZ,yBAAW,8OAAC,yJAAA,CAAA,kBAAe;wBAAC,WAAW;;;;;;oBAGvC,SAAS,CAAC,yBACT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAM;4BACN,SAAS;4BACT,YAAW;4BACX,SAAQ;4BACR,WAAU;;;;;;;;;;;oBAMf,CAAC,WAAW,CAAC,uBACZ,8OAAC;wBAAI,WAAU;wBAAgB,eAAY;kCACxC,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,aAAa,mBAAmB,GAAG,CAAC,SAAS,EAAE;4BACrD,MAAM,WAAW,WAAW,eAAe,KAAK,SAAS,EAAE;4BAC3D,MAAM,gBAAgB,iBAAiB,SAAS,EAAE;4BAClD,MAAM,eAAe,wBAAwB;4BAE7C,qBACE,8OAAC;gCAAsB,WAAU;gCAAY,eAAY;;kDAEvD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAW,CAAC,0IAA0I,EACpJ,WAAW,gCAAgC,mBAC3C;;0DAEF,8OAAC;gDAAK,WAAU;0DAA8B,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,SAAS,IAAI,IAAI;;;;;;0DAC5E,8OAAC;gDAAK,WAAU;0DAAsB,SAAS,IAAI;;;;;;4CAClD,eAAe,mBACd,8OAAC;gDAAK,WAAU;0DACb;;;;;;0DAGL,8OAAC;gDAAK,WAAU;0DACb,aAAa,MAAM;;;;;;;;;;;;oCAKvB,4BACC,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,gBAAgB,SAAS,EAAE,EAAE,KAAK,EAAE;gDACnD,WAAW,CAAC,gIAAgI,EAC1I,WAAW,WAAW,KAAK,KAAK,EAAE,GAC9B,8CACA,yBACJ;;kEAEF,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;kEAC7C,8OAAC;wDAAK,WAAU;kEAAW,KAAK,KAAK;;;;;;;+CAVhC,KAAK,EAAE;;;;;;;;;;;+BA1BZ,SAAS,EAAE;;;;;wBA2CzB;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,WAAW,CAAC,uBACZ;;0CACE,8OAAC;gCAAI,WAAU;;oCAA4C;oCACtD,WAAW,MAAM;oCAAC;oCAAM,WAAW,MAAM,CAAC,CAAC,OAAO,MAAQ,QAAQ,wBAAwB,MAAM;oCAAG;;;;;;;0CAExG,8OAAC;gCAAI,WAAU;0CAAiD;;;;;;;;oBAKnE,yBACC,8OAAC,yJAAA,CAAA,eAAY;wBACX,SAAQ;wBACR,MAAK;wBACL,SAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;;;AAMtB;AAEe,SAAS;IACtB,qBAAO,8OAAC;;;;;AACV", "debugId": null}}, {"offset": {"line": 3829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/knowledge/layout.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 知识库共享布局\n// 实现左右分割布局：左侧导航，右侧内容区域\n\nimport React from 'react';\nimport { NavigationProvider } from '@/contexts/NavigationContext';\nimport KnowledgeSidebarWrapper from '@/components/knowledge/KnowledgeSidebarWrapper';\n\ninterface KnowledgeLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function KnowledgeLayout({ children }: KnowledgeLayoutProps) {\n  return (\n    <NavigationProvider>\n      <div className=\"flex h-screen bg-white\">\n        {/* 左侧导航区域 - 固定不滚动 */}\n        <aside className=\"hidden lg:flex lg:flex-shrink-0\">\n          <div className=\"flex flex-col w-80 border-r border-mysql-border bg-white h-full\">\n            <KnowledgeSidebarWrapper />\n          </div>\n        </aside>\n\n        {/* 右侧内容区域 - 可滚动 */}\n        <main className=\"flex-1 flex flex-col min-w-0 h-full\">\n          <div className=\"flex-1 relative focus:outline-none overflow-y-auto\">\n            {children}\n          </div>\n        </main>\n\n        {/* 移动端侧边栏遮罩 */}\n        <div className=\"lg:hidden\">\n          {/* 移动端导航将在KnowledgeSidebar组件中实现 */}\n        </div>\n      </div>\n    </NavigationProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAMA;AACA;AAPA;;;;AAae,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,8OAAC,mJAAA,CAAA,qBAAkB;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAM,WAAU;8BACf,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wKAAA,CAAA,UAAuB;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}