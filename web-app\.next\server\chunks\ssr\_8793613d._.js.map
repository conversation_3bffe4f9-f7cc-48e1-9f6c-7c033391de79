{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/legal.ts"], "sourcesContent": ["// MySQLAi.de - 法律条款数据管理文件\n// 包含服务条款、隐私政策、免责声明、<PERSON>ie政策的完整内容和管理系统\n\n// 法律页面类型定义\nexport type LegalPageType = 'terms' | 'privacy' | 'disclaimer' | 'cookies';\n\n// 法律条款章节接口\nexport interface LegalSection {\n  id: string;\n  title: string;\n  content: string;\n  subsections?: LegalSection[];\n}\n\n// 法律页面内容接口\nexport interface LegalPageContent {\n  type: LegalPageType;\n  title: string;\n  description: string;\n  lastUpdated: string;\n  version: string;\n  sections: LegalSection[];\n}\n\n// 法律页面元数据接口\nexport interface LegalPageMeta {\n  type: LegalPageType;\n  title: string;\n  description: string;\n  keywords: string[];\n  lastUpdated: string;\n  version: string;\n}\n\n// 服务条款内容\nexport const TERMS_CONTENT: LegalPageContent = {\n  type: 'terms',\n  title: '服务条款',\n  description: 'MySQLAi.de平台服务使用条款和用户协议',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'acceptance',\n      title: '1. 条款接受',\n      content: '欢迎使用MySQLAi.de（以下简称\"本平台\"）提供的MySQL智能分析服务。通过访问或使用本平台的任何服务，您表示同意遵守本服务条款（以下简称\"本条款\"）。如果您不同意本条款的任何部分，请不要使用本平台的服务。'\n    },\n    {\n      id: 'service-description',\n      title: '2. 服务描述',\n      content: 'MySQLAi.de是一个专业的MySQL数据库智能分析平台，为用户提供以下服务：',\n      subsections: [\n        {\n          id: 'service-mysql-analysis',\n          title: '2.1 MySQL智能分析',\n          content: '基于AI技术的数据库性能分析、优化建议和智能诊断服务。'\n        },\n        {\n          id: 'service-project-management',\n          title: '2.2 项目管理',\n          content: '提供数据库项目的任务管理、进度跟踪和团队协作功能。'\n        },\n        {\n          id: 'service-report-display',\n          title: '2.3 报告展示',\n          content: '支持多媒体格式的项目报告生成、展示和分享功能。'\n        },\n        {\n          id: 'service-technical-support',\n          title: '2.4 技术支持',\n          content: '7×24小时专业技术支持服务，包括在线咨询、远程协助等。'\n        }\n      ]\n    },\n    {\n      id: 'user-obligations',\n      title: '3. 用户义务',\n      content: '使用本平台服务时，您需要遵守以下义务：',\n      subsections: [\n        {\n          id: 'legal-compliance',\n          title: '3.1 法律合规',\n          content: '遵守中华人民共和国相关法律法规，不得利用本平台从事违法活动。'\n        },\n        {\n          id: 'account-security',\n          title: '3.2 账户安全',\n          content: '妥善保管账户信息，对账户下的所有活动承担责任。'\n        },\n        {\n          id: 'data-accuracy',\n          title: '3.3 数据准确性',\n          content: '确保提供给本平台的数据信息真实、准确、完整。'\n        },\n        {\n          id: 'proper-usage',\n          title: '3.4 合理使用',\n          content: '合理使用平台资源，不得恶意攻击、滥用或干扰平台正常运行。'\n        }\n      ]\n    },\n    {\n      id: 'intellectual-property',\n      title: '4. 知识产权',\n      content: '本平台的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。未经授权，不得复制、传播、展示、镜像、上传、下载使用。'\n    },\n    {\n      id: 'privacy-protection',\n      title: '5. 隐私保护',\n      content: '我们重视用户隐私保护，具体的隐私保护措施请参阅《隐私政策》。我们承诺按照相关法律法规和本平台隐私政策处理用户个人信息。'\n    },\n    {\n      id: 'service-availability',\n      title: '6. 服务可用性',\n      content: '我们努力确保服务的连续性和稳定性，但不保证服务不会中断。因系统维护、升级或不可抗力等原因导致的服务中断，我们将尽快恢复服务。'\n    },\n    {\n      id: 'limitation-of-liability',\n      title: '7. 责任限制',\n      content: '在法律允许的最大范围内，本平台对因使用或无法使用本服务而导致的任何直接、间接、偶然、特殊或后果性损害不承担责任。'\n    },\n    {\n      id: 'terms-modification',\n      title: '8. 条款修改',\n      content: '我们保留随时修改本条款的权利。修改后的条款将在本平台公布，继续使用本服务即表示您接受修改后的条款。'\n    },\n    {\n      id: 'governing-law',\n      title: '9. 适用法律',\n      content: '本条款的解释和执行适用中华人民共和国法律。如发生争议，应通过友好协商解决；协商不成的，提交本平台所在地人民法院管辖。'\n    },\n    {\n      id: 'contact-information',\n      title: '10. 联系方式',\n      content: '如您对本条款有任何疑问，请通过以下方式联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 隐私政策内容\nexport const PRIVACY_CONTENT: LegalPageContent = {\n  type: 'privacy',\n  title: '隐私政策',\n  description: 'MySQLAi.de平台用户隐私保护政策和个人信息处理规则',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'introduction',\n      title: '1. 引言',\n      content: 'MySQLAi.de（以下简称\"我们\"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'\n    },\n    {\n      id: 'information-collection',\n      title: '2. 我们收集的信息',\n      content: '为了向您提供更好的服务，我们可能会收集以下类型的信息：',\n      subsections: [\n        {\n          id: 'account-information',\n          title: '2.1 账户信息',\n          content: '当您注册账户时，我们会收集您的用户名、邮箱地址、手机号码等基本信息。'\n        },\n        {\n          id: 'usage-information',\n          title: '2.2 使用信息',\n          content: '您使用我们服务时产生的信息，包括访问时间、使用功能、操作记录等。'\n        },\n        {\n          id: 'device-information',\n          title: '2.3 设备信息',\n          content: '您使用的设备信息，包括设备型号、操作系统、浏览器类型、IP地址等。'\n        },\n        {\n          id: 'database-information',\n          title: '2.4 数据库信息',\n          content: '为提供MySQL分析服务，我们可能需要访问您的数据库结构信息（不包含敏感业务数据）。'\n        }\n      ]\n    },\n    {\n      id: 'information-usage',\n      title: '3. 信息使用目的',\n      content: '我们收集和使用您的个人信息主要用于以下目的：',\n      subsections: [\n        {\n          id: 'service-provision',\n          title: '3.1 服务提供',\n          content: '为您提供MySQL智能分析、项目管理、报告展示等核心服务。'\n        },\n        {\n          id: 'service-improvement',\n          title: '3.2 服务改进',\n          content: '分析用户使用习惯，优化产品功能和用户体验。'\n        },\n        {\n          id: 'security-protection',\n          title: '3.3 安全保护',\n          content: '保护您的账户安全，防范欺诈、滥用等风险。'\n        },\n        {\n          id: 'customer-support',\n          title: '3.4 客户支持',\n          content: '为您提供技术支持和客户服务。'\n        }\n      ]\n    },\n    {\n      id: 'information-sharing',\n      title: '4. 信息共享',\n      content: '我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：',\n      subsections: [\n        {\n          id: 'legal-requirement',\n          title: '4.1 法律要求',\n          content: '根据法律法规、法律程序、政府要求或司法裁定需要披露。'\n        },\n        {\n          id: 'user-consent',\n          title: '4.2 用户同意',\n          content: '获得您的明确同意后，与第三方共享特定信息。'\n        },\n        {\n          id: 'service-providers',\n          title: '4.3 服务提供商',\n          content: '与我们的服务提供商共享必要信息，以便他们为我们提供服务。'\n        }\n      ]\n    },\n    {\n      id: 'information-security',\n      title: '5. 信息安全',\n      content: '我们采用行业标准的安全措施保护您的个人信息：',\n      subsections: [\n        {\n          id: 'encryption',\n          title: '5.1 数据加密',\n          content: '使用SSL/TLS加密技术保护数据传输安全。'\n        },\n        {\n          id: 'access-control',\n          title: '5.2 访问控制',\n          content: '严格限制员工对个人信息的访问权限。'\n        },\n        {\n          id: 'security-monitoring',\n          title: '5.3 安全监控',\n          content: '建立完善的安全监控和应急响应机制。'\n        }\n      ]\n    },\n    {\n      id: 'user-rights',\n      title: '6. 您的权利',\n      content: '您对自己的个人信息享有以下权利：',\n      subsections: [\n        {\n          id: 'access-right',\n          title: '6.1 知情权',\n          content: '您有权了解我们收集、使用您个人信息的情况。'\n        },\n        {\n          id: 'correction-right',\n          title: '6.2 更正权',\n          content: '您有权要求我们更正或补充您的个人信息。'\n        },\n        {\n          id: 'deletion-right',\n          title: '6.3 删除权',\n          content: '在特定情况下，您有权要求我们删除您的个人信息。'\n        }\n      ]\n    },\n    {\n      id: 'policy-updates',\n      title: '7. 政策更新',\n      content: '我们可能会不时更新本隐私政策。更新后的政策将在本平台公布，并通过适当方式通知您。'\n    },\n    {\n      id: 'contact-us',\n      title: '8. 联系我们',\n      content: '如您对本隐私政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 免责声明内容\nexport const DISCLAIMER_CONTENT: LegalPageContent = {\n  type: 'disclaimer',\n  title: '免责声明',\n  description: 'MySQLAi.de平台服务免责条款和责任限制说明',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'general-disclaimer',\n      title: '1. 一般免责',\n      content: 'MySQLAi.de平台（以下简称\"本平台\"）提供的所有信息、建议和服务仅供参考，不构成任何形式的保证。用户使用本平台服务的风险由用户自行承担。'\n    },\n    {\n      id: 'service-limitations',\n      title: '2. 服务限制',\n      content: '本平台的服务存在以下限制：',\n      subsections: [\n        {\n          id: 'analysis-accuracy',\n          title: '2.1 分析准确性',\n          content: 'MySQL智能分析结果基于算法和数据模型，可能存在误差，不保证100%准确。'\n        },\n        {\n          id: 'service-availability',\n          title: '2.2 服务可用性',\n          content: '服务可能因维护、升级、网络故障等原因暂时中断，我们不承担因此造成的损失。'\n        },\n        {\n          id: 'data-security',\n          title: '2.3 数据安全',\n          content: '虽然我们采取安全措施保护数据，但无法保证绝对安全，不承担因数据泄露造成的损失。'\n        }\n      ]\n    },\n    {\n      id: 'user-responsibility',\n      title: '3. 用户责任',\n      content: '用户在使用本平台服务时应承担以下责任：',\n      subsections: [\n        {\n          id: 'data-backup',\n          title: '3.1 数据备份',\n          content: '用户应自行备份重要数据，本平台不承担数据丢失的责任。'\n        },\n        {\n          id: 'decision-making',\n          title: '3.2 决策责任',\n          content: '基于本平台分析结果做出的业务决策，责任由用户自行承担。'\n        },\n        {\n          id: 'compliance',\n          title: '3.3 合规使用',\n          content: '用户应确保使用本平台服务符合相关法律法规要求。'\n        }\n      ]\n    },\n    {\n      id: 'third-party-services',\n      title: '4. 第三方服务',\n      content: '本平台可能包含第三方服务链接，对于第三方服务的内容、隐私政策或做法，我们不承担任何责任。'\n    },\n    {\n      id: 'liability-limitation',\n      title: '5. 责任限制',\n      content: '在法律允许的最大范围内，本平台对任何直接、间接、偶然、特殊、后果性或惩罚性损害不承担责任，包括但不限于利润损失、数据丢失、业务中断等。'\n    },\n    {\n      id: 'indemnification',\n      title: '6. 赔偿',\n      content: '用户同意就因违反本免责声明或使用本平台服务而产生的任何索赔、损失或费用，向本平台提供赔偿和保护。'\n    },\n    {\n      id: 'governing-law',\n      title: '7. 适用法律',\n      content: '本免责声明受中华人民共和国法律管辖。任何争议应通过友好协商解决，协商不成的提交本平台所在地人民法院管辖。'\n    }\n  ]\n};\n\n// Cookie政策内容\nexport const COOKIES_CONTENT: LegalPageContent = {\n  type: 'cookies',\n  title: 'Cookie政策',\n  description: 'MySQLAi.de平台Cookie使用说明和管理指南',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'what-are-cookies',\n      title: '1. 什么是Cookie',\n      content: 'Cookie是网站存储在您设备上的小型文本文件，用于记住您的偏好设置和改善您的浏览体验。Cookie不会损害您的设备或文件。'\n    },\n    {\n      id: 'cookie-types',\n      title: '2. Cookie类型',\n      content: '我们使用以下类型的Cookie：',\n      subsections: [\n        {\n          id: 'essential-cookies',\n          title: '2.1 必要Cookie',\n          content: '这些Cookie对网站正常运行是必需的，包括用户身份验证、安全防护等功能。'\n        },\n        {\n          id: 'functional-cookies',\n          title: '2.2 功能Cookie',\n          content: '这些Cookie用于记住您的偏好设置，如语言选择、主题设置等，以提供个性化体验。'\n        },\n        {\n          id: 'analytics-cookies',\n          title: '2.3 分析Cookie',\n          content: '这些Cookie帮助我们了解用户如何使用网站，以便改进网站性能和用户体验。'\n        },\n        {\n          id: 'marketing-cookies',\n          title: '2.4 营销Cookie',\n          content: '这些Cookie用于跟踪用户在网站上的活动，以便提供相关的广告和营销内容。'\n        }\n      ]\n    },\n    {\n      id: 'cookie-usage',\n      title: '3. Cookie使用目的',\n      content: '我们使用Cookie的主要目的包括：',\n      subsections: [\n        {\n          id: 'user-authentication',\n          title: '3.1 用户认证',\n          content: '保持您的登录状态，确保账户安全。'\n        },\n        {\n          id: 'preference-storage',\n          title: '3.2 偏好存储',\n          content: '记住您的设置和偏好，提供个性化服务。'\n        },\n        {\n          id: 'performance-analysis',\n          title: '3.3 性能分析',\n          content: '分析网站使用情况，优化网站性能。'\n        },\n        {\n          id: 'security-protection',\n          title: '3.4 安全保护',\n          content: '防范恶意攻击，保护网站和用户安全。'\n        }\n      ]\n    },\n    {\n      id: 'cookie-management',\n      title: '4. Cookie管理',\n      content: '您可以通过以下方式管理Cookie：',\n      subsections: [\n        {\n          id: 'browser-settings',\n          title: '4.1 浏览器设置',\n          content: '大多数浏览器允许您控制Cookie设置，包括接受、拒绝或删除Cookie。'\n        },\n        {\n          id: 'platform-settings',\n          title: '4.2 平台设置',\n          content: '您可以在账户设置中管理某些Cookie偏好。'\n        },\n        {\n          id: 'opt-out',\n          title: '4.3 退出选择',\n          content: '您可以选择退出某些非必要Cookie，但这可能影响网站功能。'\n        }\n      ]\n    },\n    {\n      id: 'third-party-cookies',\n      title: '5. 第三方Cookie',\n      content: '我们的网站可能包含第三方服务提供商设置的Cookie，如分析工具、社交媒体插件等。这些第三方Cookie受其各自隐私政策约束。'\n    },\n    {\n      id: 'policy-updates',\n      title: '6. 政策更新',\n      content: '我们可能会不时更新本Cookie政策。更新后的政策将在网站上公布，建议您定期查看。'\n    },\n    {\n      id: 'contact-information',\n      title: '7. 联系我们',\n      content: '如您对本Cookie政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 所有法律内容的集合\nexport const LEGAL_CONTENTS = {\n  terms: TERMS_CONTENT,\n  privacy: PRIVACY_CONTENT,\n  disclaimer: DISCLAIMER_CONTENT,\n  cookies: COOKIES_CONTENT,\n} as const;\n\n// 工具函数：获取指定类型的法律条款内容\nexport function getLegalContent(type: LegalPageType): LegalPageContent {\n  return LEGAL_CONTENTS[type];\n}\n\n// 工具函数：获取法律页面元数据\nexport function getLegalPageMeta(type: LegalPageType): LegalPageMeta {\n  const content = getLegalContent(type);\n  return {\n    type: content.type,\n    title: content.title,\n    description: content.description,\n    keywords: generateKeywords(type),\n    lastUpdated: content.lastUpdated,\n    version: content.version,\n  };\n}\n\n// 工具函数：生成法律页面关键词\nfunction generateKeywords(type: LegalPageType): string[] {\n  const baseKeywords = ['MySQLAi.de', 'MySQL', '数据库', '法律声明'];\n\n  const typeKeywords = {\n    terms: ['服务条款', '用户协议', '使用条款', '服务协议'],\n    privacy: ['隐私政策', '个人信息保护', '数据保护', '隐私保护'],\n    disclaimer: ['免责声明', '责任限制', '法律免责', '服务限制'],\n    cookies: ['Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理'],\n  };\n\n  return [...baseKeywords, ...typeKeywords[type]];\n}\n\n// 工具函数：获取所有法律页面类型\nexport function getAllLegalPageTypes(): LegalPageType[] {\n  return ['terms', 'privacy', 'disclaimer', 'cookies'];\n}\n\n// 工具函数：获取法律页面导航链接\nexport function getLegalNavigationLinks(currentType?: LegalPageType) {\n  return getAllLegalPageTypes()\n    .filter(type => type !== currentType)\n    .map(type => {\n      const meta = getLegalPageMeta(type);\n      return {\n        type,\n        title: meta.title,\n        href: `/${type}`,\n        description: meta.description,\n      };\n    });\n}\n\n// 工具函数：格式化最后更新时间\nexport function formatLastUpdated(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n// 工具函数：检查法律页面类型是否有效\nexport function isValidLegalPageType(type: string): type is LegalPageType {\n  return getAllLegalPageTypes().includes(type as LegalPageType);\n}\n"], "names": [], "mappings": "AAAA,0BAA0B;AAC1B,sCAAsC;AAEtC,WAAW;;;;;;;;;;;;;;AAgCJ,MAAM,gBAAkC;IAC7C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,kBAAoC;IAC/C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,qBAAuC;IAClD,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,kBAAoC;IAC/C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,iBAAiB;IAC5B,OAAO;IACP,SAAS;IACT,YAAY;IACZ,SAAS;AACX;AAGO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,cAAc,CAAC,KAAK;AAC7B;AAGO,SAAS,iBAAiB,IAAmB;IAClD,MAAM,UAAU,gBAAgB;IAChC,OAAO;QACL,MAAM,QAAQ,IAAI;QAClB,OAAO,QAAQ,KAAK;QACpB,aAAa,QAAQ,WAAW;QAChC,UAAU,iBAAiB;QAC3B,aAAa,QAAQ,WAAW;QAChC,SAAS,QAAQ,OAAO;IAC1B;AACF;AAEA,iBAAiB;AACjB,SAAS,iBAAiB,IAAmB;IAC3C,MAAM,eAAe;QAAC;QAAc;QAAS;QAAO;KAAO;IAE3D,MAAM,eAAe;QACnB,OAAO;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QACvC,SAAS;YAAC;YAAQ;YAAU;YAAQ;SAAO;QAC3C,YAAY;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC5C,SAAS;YAAC;YAAY;YAAY;YAAY;SAAW;IAC3D;IAEA,OAAO;WAAI;WAAiB,YAAY,CAAC,KAAK;KAAC;AACjD;AAGO,SAAS;IACd,OAAO;QAAC;QAAS;QAAW;QAAc;KAAU;AACtD;AAGO,SAAS,wBAAwB,WAA2B;IACjE,OAAO,uBACJ,MAAM,CAAC,CAAA,OAAQ,SAAS,aACxB,GAAG,CAAC,CAAA;QACH,MAAM,OAAO,iBAAiB;QAC9B,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,MAAM,CAAC,CAAC,EAAE,MAAM;YAChB,aAAa,KAAK,WAAW;QAC/B;IACF;AACJ;AAGO,SAAS,kBAAkB,UAAkB;IAClD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,uBAAuB,QAAQ,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/LegalPageLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/LegalPageLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/LegalPageLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/LegalPageLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/LegalPageLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/LegalPageLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/terms/page.tsx"], "sourcesContent": ["// MySQLAi.de - 服务条款页面\n// 展示平台服务使用条款和用户协议\n\nimport { Metadata } from 'next';\nimport { getLegalContent } from '@/lib/legal';\nimport { PAGE_METADATA } from '@/lib/constants';\nimport { generatePageMetadata } from '@/app/metadata';\nimport LegalPageLayout from '@/components/layout/LegalPageLayout';\n\n// 生成页面元数据\nexport function generateMetadata(): Metadata {\n  const pageData = PAGE_METADATA.terms;\n  return generatePageMetadata(\n    pageData.title,\n    pageData.description,\n    '/terms'\n  );\n}\n\nexport default function TermsPage() {\n  // 获取服务条款内容\n  const termsContent = getLegalContent('terms');\n\n  return (\n    <LegalPageLayout\n      type=\"terms\"\n      title={termsContent.title}\n      lastUpdated={termsContent.lastUpdated}\n      pathname=\"/terms\"\n    >\n      {/* 渲染服务条款内容 */}\n      {termsContent.sections.map((section) => (\n        <div key={section.id} className=\"mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n            {section.title}\n          </h2>\n          <p className=\"text-gray-700 leading-relaxed mb-4\">\n            {section.content}\n          </p>\n          \n          {/* 渲染子章节 */}\n          {section.subsections && section.subsections.length > 0 && (\n            <div className=\"ml-4 space-y-4\">\n              {section.subsections.map((subsection) => (\n                <div key={subsection.id}>\n                  <h3 className=\"text-lg font-medium text-gray-800 mb-2\">\n                    {subsection.title}\n                  </h3>\n                  <p className=\"text-gray-700 leading-relaxed\">\n                    {subsection.content}\n                  </p>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      ))}\n    </LegalPageLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,kBAAkB;;;;;;AAGlB;AACA;AACA;AACA;;;;;;AAGO,SAAS;IACd,MAAM,WAAW,qIAAA,CAAA,gBAAa,CAAC,KAAK;IACpC,OAAO,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EACxB,SAAS,KAAK,EACd,SAAS,WAAW,EACpB;AAEJ;AAEe,SAAS;IACtB,WAAW;IACX,MAAM,eAAe,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE;IAErC,qBACE,8OAAC,6JAAA,CAAA,UAAe;QACd,MAAK;QACL,OAAO,aAAa,KAAK;QACzB,aAAa,aAAa,WAAW;QACrC,UAAS;kBAGR,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC1B,8OAAC;gBAAqB,WAAU;;kCAC9B,8OAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,8OAAC;wBAAE,WAAU;kCACV,QAAQ,OAAO;;;;;;oBAIjB,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,WAAW,KAAK;;;;;;kDAEnB,8OAAC;wCAAE,WAAU;kDACV,WAAW,OAAO;;;;;;;+BALb,WAAW,EAAE;;;;;;;;;;;eAZrB,QAAQ,EAAE;;;;;;;;;;AA2B5B", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,yUAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,6UAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,yUAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}