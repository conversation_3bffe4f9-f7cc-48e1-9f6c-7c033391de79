{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/supabase.ts"], "sourcesContent": ["// MySQLAi.de - Supabase 客户端配置\n// 提供类型安全的 Supabase 客户端实例\n\nimport { createClient } from '@supabase/supabase-js';\nimport type { Database } from './database.types';\n\n// 环境变量验证 - 构建时使用占位符\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://example.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4YW1wbGUiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MjQ0NjQwMCwiZXhwIjoxOTU4MDIyNDAwfQ.example';\n\n// 仅在客户端检查环境变量\nif (typeof window !== 'undefined' && (supabaseUrl.includes('example') || supabaseAnonKey.includes('example'))) {\n  console.warn('请配置正确的 Supabase 环境变量');\n}\n\n// 创建 Supabase 客户端实例\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10\n    }\n  }\n});\n\n// 服务端客户端（用于服务端操作）\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY || supabaseAnonKey,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n);\n\n// 认证相关工具函数\nexport const auth = {\n  // 获取当前用户\n  getCurrentUser: async () => {\n    const { data: { user }, error } = await supabase.auth.getUser();\n    return { user, error };\n  },\n\n  // 登录\n  signIn: async (email: string, password: string) => {\n    return await supabase.auth.signInWithPassword({ email, password });\n  },\n\n  // 注册\n  signUp: async (email: string, password: string, metadata?: Record<string, any>) => {\n    return await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata\n      }\n    });\n  },\n\n  // 登出\n  signOut: async () => {\n    return await supabase.auth.signOut();\n  },\n\n  // 重置密码\n  resetPassword: async (email: string) => {\n    return await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`\n    });\n  }\n};\n\n// 数据库操作工具函数\nexport const db = {\n  // 知识库文章\n  articles: {\n    getAll: () => supabase.from('knowledge_articles').select('*'),\n    getById: (id: string) => supabase.from('knowledge_articles').select('*').eq('id', id).single(),\n    getByCategory: (categoryId: string) => supabase.from('knowledge_articles').select('*').eq('category_id', categoryId),\n    search: (query: string) => supabase.from('knowledge_articles').select('*').textSearch('title,content', query)\n  },\n\n  // ER图项目\n  erProjects: {\n    getAll: () => supabase.from('er_projects').select('*'),\n    getById: (id: string) => supabase.from('er_projects').select('*').eq('id', id).single(),\n    getByUser: (userId: string) => supabase.from('er_projects').select('*').eq('user_id', userId),\n    create: (project: any) => supabase.from('er_projects').insert(project),\n    update: (id: string, updates: any) => supabase.from('er_projects').update(updates).eq('id', id),\n    delete: (id: string) => supabase.from('er_projects').delete().eq('id', id)\n  },\n\n  // 用户收藏\n  favorites: {\n    getByUser: (userId: string) => supabase.from('user_favorites').select('*, knowledge_articles(*)').eq('user_id', userId),\n    add: (userId: string, articleId: string) => supabase.from('user_favorites').insert({ user_id: userId, article_id: articleId }),\n    remove: (userId: string, articleId: string) => supabase.from('user_favorites').delete().eq('user_id', userId).eq('article_id', articleId)\n  }\n};\n\nexport default supabase;\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,yBAAyB;;;;;;;;AAEzB;;AAGA,oBAAoB;AACpB,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,cAAc;AACd,IAAI,gBAAkB,eAAe,CAAC,YAAY,QAAQ,CAAC,cAAc,gBAAgB,QAAQ,CAAC,UAAU,GAAG;;AAE/G;AAGO,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa,iBAAiB;IAC3E,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB,IAAI,iBACzC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAIK,MAAM,OAAO;IAClB,SAAS;IACT,gBAAgB;QACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,KAAK;IACL,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;IAClE;IAEA,KAAK;IACL,QAAQ,OAAO,OAAe,UAAkB;QAC9C,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAChC;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;IACF;IAEA,KAAK;IACL,SAAS;QACP,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;IACpC;IAEA,OAAO;IACP,eAAe,OAAO;QACpB,OAAO,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACtD,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;IACF;AACF;AAGO,MAAM,KAAK;IAChB,QAAQ;IACR,UAAU;QACR,QAAQ,IAAM,SAAS,IAAI,CAAC,sBAAsB,MAAM,CAAC;QACzD,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,sBAAsB,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAC5F,eAAe,CAAC,aAAuB,SAAS,IAAI,CAAC,sBAAsB,MAAM,CAAC,KAAK,EAAE,CAAC,eAAe;QACzG,QAAQ,CAAC,QAAkB,SAAS,IAAI,CAAC,sBAAsB,MAAM,CAAC,KAAK,UAAU,CAAC,iBAAiB;IACzG;IAEA,QAAQ;IACR,YAAY;QACV,QAAQ,IAAM,SAAS,IAAI,CAAC,eAAe,MAAM,CAAC;QAClD,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,eAAe,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACrF,WAAW,CAAC,SAAmB,SAAS,IAAI,CAAC,eAAe,MAAM,CAAC,KAAK,EAAE,CAAC,WAAW;QACtF,QAAQ,CAAC,UAAiB,SAAS,IAAI,CAAC,eAAe,MAAM,CAAC;QAC9D,QAAQ,CAAC,IAAY,UAAiB,SAAS,IAAI,CAAC,eAAe,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM;QAC5F,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,MAAM;IACzE;IAEA,OAAO;IACP,WAAW;QACT,WAAW,CAAC,SAAmB,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,4BAA4B,EAAE,CAAC,WAAW;QAChH,KAAK,CAAC,QAAgB,YAAsB,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC;gBAAE,SAAS;gBAAQ,YAAY;YAAU;QAC5H,QAAQ,CAAC,QAAgB,YAAsB,SAAS,IAAI,CAAC,kBAAkB,MAAM,GAAG,EAAE,CAAC,WAAW,QAAQ,EAAE,CAAC,cAAc;IACjI;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/api/knowledge/search/route.ts"], "sourcesContent": ["// MySQLAi.de - 知识库搜索 API\n// 提供高级搜索功能和搜索统计\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { supabase } from '@/lib/supabase';\n\n// 高亮文本函数\nfunction highlightText(text: string, query: string): string {\n  if (!query || !text) return text;\n\n  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi');\n  return text.replace(regex, '<mark>$1</mark>');\n}\n\n// 计算文本相似度（简化版TF-IDF）\nfunction calculateRelevanceScore(article: any, query: string): number {\n  if (!query || !article) return 0;\n\n  const queryTerms = query.toLowerCase().split(/\\s+/).filter(term => term.length > 1);\n  if (queryTerms.length === 0) return 0;\n\n  let score = 0;\n  const title = (article.title || '').toLowerCase();\n  const content = (article.content || '').toLowerCase();\n  const description = (article.description || '').toLowerCase();\n\n  queryTerms.forEach(term => {\n    // 标题匹配权重最高\n    const titleMatches = (title.match(new RegExp(term, 'g')) || []).length;\n    score += titleMatches * 10;\n\n    // 描述匹配权重中等\n    const descMatches = (description.match(new RegExp(term, 'g')) || []).length;\n    score += descMatches * 5;\n\n    // 内容匹配权重较低\n    const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;\n    score += contentMatches * 2;\n\n    // 完全匹配加分\n    if (title.includes(term)) score += 20;\n    if (description.includes(term)) score += 10;\n  });\n\n  // 标签匹配加分\n  if (article.tags && Array.isArray(article.tags)) {\n    queryTerms.forEach(term => {\n      const tagMatches = article.tags.filter((tag: string) =>\n        tag.toLowerCase().includes(term)\n      ).length;\n      score += tagMatches * 15;\n    });\n  }\n\n  return score;\n}\n\n// 智能排序函数\nfunction intelligentSort(articles: any[], query: string, sortBy: string, sortOrder: string): any[] {\n  if (!articles || articles.length === 0) return articles;\n\n  // 为每篇文章计算相关性分数\n  const articlesWithScore = articles.map(article => ({\n    ...article,\n    relevanceScore: calculateRelevanceScore(article, query)\n  }));\n\n  // 根据排序方式进行排序\n  return articlesWithScore.sort((a, b) => {\n    switch (sortBy) {\n      case 'relevance':\n        // 相关性排序：分数高的在前，分数相同时按更新时间排序\n        if (a.relevanceScore !== b.relevanceScore) {\n          return sortOrder === 'asc' ? a.relevanceScore - b.relevanceScore : b.relevanceScore - a.relevanceScore;\n        }\n        return new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime();\n\n      case 'date':\n        const dateA = new Date(a.last_updated).getTime();\n        const dateB = new Date(b.last_updated).getTime();\n        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;\n\n      case 'title':\n        const titleA = (a.title || '').toLowerCase();\n        const titleB = (b.title || '').toLowerCase();\n        return sortOrder === 'asc' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);\n\n      default:\n        // 默认按相关性排序\n        return b.relevanceScore - a.relevanceScore;\n    }\n  });\n}\n\n// GET /api/knowledge/search - 高级搜索\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    \n    const query = searchParams.get('q') || searchParams.get('query');\n    const category = searchParams.get('category');\n    const tags = searchParams.get('tags');\n    const difficulty = searchParams.get('difficulty');\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const sortBy = searchParams.get('sortBy') || 'relevance'; // relevance, date, title\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n\n    if (!query) {\n      return NextResponse.json(\n        { success: false, error: '请提供搜索关键词' },\n        { status: 400 }\n      );\n    }\n\n    // 记录搜索历史\n    const userAgent = request.headers.get('user-agent');\n    const forwarded = request.headers.get('x-forwarded-for');\n    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip');\n\n    // 构建搜索查询\n    let searchQuery = supabase\n      .from('knowledge_articles')\n      .select(`\n        *,\n        knowledge_categories(id, name, icon, color)\n      `);\n\n    // 尝试全文搜索，如果失败则使用ilike搜索\n    console.log('执行textSearch查询:', 'title,content', query);\n\n    // 先尝试textSearch\n    const textSearchQuery = supabase\n      .from('knowledge_articles')\n      .select(`\n        *,\n        knowledge_categories(id, name, icon, color)\n      `)\n      .textSearch('title,content', query);\n\n    // 如果有其他筛选条件，也应用到textSearch\n    if (category) {\n      textSearchQuery.eq('category_id', category);\n    }\n    if (difficulty) {\n      textSearchQuery.eq('difficulty', difficulty);\n    }\n    if (tags) {\n      const tagArray = tags.split(',').map(tag => tag.trim());\n      textSearchQuery.overlaps('tags', tagArray);\n    }\n\n    const { data: textSearchResults, error: textSearchError } = await textSearchQuery;\n    console.log('textSearch结果数量:', textSearchResults?.length || 0);\n    console.log('textSearch错误:', textSearchError);\n\n    // 如果textSearch有结果，使用它；否则使用ilike搜索\n    let finalResults = textSearchResults;\n    let finalError = textSearchError;\n\n    if (!textSearchResults || textSearchResults.length === 0) {\n      console.log('textSearch无结果，尝试ilike搜索...');\n\n      // 使用ilike搜索作为备选方案\n      let iLikeQuery = supabase\n        .from('knowledge_articles')\n        .select(`\n          *,\n          knowledge_categories(id, name, icon, color)\n        `)\n        .or(`title.ilike.%${query}%,content.ilike.%${query}%`);\n\n      // 应用筛选条件\n      if (category) {\n        iLikeQuery = iLikeQuery.eq('category_id', category);\n      }\n      if (difficulty) {\n        iLikeQuery = iLikeQuery.eq('difficulty', difficulty);\n      }\n      if (tags) {\n        const tagArray = tags.split(',').map(tag => tag.trim());\n        iLikeQuery = iLikeQuery.overlaps('tags', tagArray);\n      }\n\n      const { data: iLikeResults, error: iLikeError } = await iLikeQuery;\n      console.log('ilike搜索结果数量:', iLikeResults?.length || 0);\n      console.log('ilike搜索错误:', iLikeError);\n\n      finalResults = iLikeResults;\n      finalError = iLikeError;\n    }\n\n    // 更新原来的变量\n    let allArticles = finalResults;\n    let error = finalError;\n    let count = finalResults?.length || 0;\n\n    // 查询已在上面执行完成，筛选条件也已应用\n\n    console.log('=== 搜索API调试信息 ===');\n    console.log('查询关键词:', query);\n    console.log('数据库查询结果数量:', allArticles?.length || 0);\n    console.log('数据库错误:', error);\n    console.log('第一条数据示例:', allArticles?.[0]);\n\n    // 测试数据库连接 - 查询所有文章\n    const { data: allData, error: allError } = await supabase\n      .from('knowledge_articles')\n      .select('*');\n    console.log('数据库连接测试 - 所有文章数量:', allData?.length || 0);\n    console.log('数据库连接测试 - 错误:', allError);\n    console.log('数据库中的文章标题:', allData?.map(article => article.title) || []);\n    console.log('第一篇文章完整数据:', allData?.[0]);\n\n    if (error) {\n      console.error('搜索失败:', error);\n      return NextResponse.json(\n        { success: false, error: '搜索失败', details: error.message },\n        { status: 500 }\n      );\n    }\n\n    // 使用智能排序算法对结果进行排序\n    const sortedArticles = intelligentSort(allArticles || [], query, sortBy, sortOrder);\n\n    console.log('排序后结果数量:', sortedArticles.length);\n    console.log('排序后第一条数据:', sortedArticles[0]);\n\n    // 手动分页\n    const offset = (page - 1) * limit;\n    const paginatedArticles = sortedArticles.slice(offset, offset + limit);\n\n    console.log('分页后结果数量:', paginatedArticles.length);\n    console.log('分页参数 - page:', page, 'limit:', limit, 'offset:', offset);\n\n    // 异步记录搜索历史\n    supabase\n      .from('search_history')\n      .insert({\n        query,\n        results_count: count || 0,\n        ip_address: ip,\n        user_agent: userAgent\n      })\n      .then(({ error }) => {\n        if (error) console.error('记录搜索历史失败:', error);\n      });\n\n    // 为每个结果添加搜索高亮信息和相关性分数\n    const resultsWithHighlight = paginatedArticles.map(article => ({\n      ...article,\n      highlight: {\n        title: highlightText(article.title, query),\n        description: highlightText(article.description || '', query)\n      },\n      // 添加相关性分数用于调试和展示\n      relevanceScore: article.relevanceScore || 0\n    }));\n\n    console.log('最终返回数据数量:', resultsWithHighlight.length);\n    console.log('最终返回数据示例:', resultsWithHighlight[0]);\n    console.log('=== 搜索API调试信息结束 ===');\n\n    return NextResponse.json({\n      success: true,\n      data: resultsWithHighlight,\n      query: {\n        text: query,\n        category,\n        tags,\n        difficulty,\n        sortBy,\n        sortOrder\n      },\n      pagination: {\n        page,\n        limit,\n        total: count || 0,\n        totalPages: Math.ceil((count || 0) / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('API错误:', error);\n    return NextResponse.json(\n      { success: false, error: '服务器内部错误' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/knowledge/search - 搜索建议\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { query, limit = 5 } = body;\n\n    if (!query || query.length < 2) {\n      return NextResponse.json({\n        success: true,\n        data: []\n      });\n    }\n\n    // 并行搜索多种建议类型\n    const [titleMatches, tagMatches, popularSearches] = await Promise.all([\n      // 1. 搜索文章标题匹配\n      supabase\n        .from('knowledge_articles')\n        .select('id, title, category_id, knowledge_categories(name)')\n        .ilike('title', `%${query}%`)\n        .limit(Math.ceil(limit * 0.6)), // 60%的建议来自标题匹配\n\n      // 2. 搜索标签匹配\n      supabase\n        .from('knowledge_articles')\n        .select('id, title, tags, category_id, knowledge_categories(name)')\n        .contains('tags', [query])\n        .limit(Math.ceil(limit * 0.3)), // 30%的建议来自标签匹配\n\n      // 3. 搜索热门搜索词\n      supabase\n        .from('search_history')\n        .select('query')\n        .ilike('query', `%${query}%`)\n        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // 最近30天\n        .limit(Math.ceil(limit * 0.2)) // 20%的建议来自热门搜索\n    ]);\n\n    // 处理错误\n    if (titleMatches.error) {\n      console.error('搜索标题建议失败:', titleMatches.error);\n    }\n    if (tagMatches.error) {\n      console.error('搜索标签建议失败:', tagMatches.error);\n    }\n    if (popularSearches.error) {\n      console.error('获取热门搜索失败:', popularSearches.error);\n    }\n\n    // 构建建议列表\n    const suggestions = [];\n\n    // 添加标题匹配建议（优先级最高）\n    if (titleMatches.data) {\n      suggestions.push(...titleMatches.data.map(article => ({\n        type: 'article' as const,\n        text: article.title,\n        id: article.id,\n        category: (article.knowledge_categories as any)?.name || '未分类',\n        priority: 3 // 最高优先级\n      })));\n    }\n\n    // 添加标签匹配建议\n    if (tagMatches.data) {\n      suggestions.push(...tagMatches.data\n        .filter(article => !suggestions.some(s => s.id === article.id)) // 去重\n        .map(article => ({\n          type: 'article' as const,\n          text: article.title,\n          id: article.id,\n          category: (article.knowledge_categories as any)?.name || '未分类',\n          priority: 2\n        })));\n    }\n\n    // 添加热门搜索建议\n    if (popularSearches.data) {\n      suggestions.push(...popularSearches.data.map(search => ({\n        type: 'query' as const,\n        text: search.query,\n        priority: 1\n      })));\n    }\n\n    // 按优先级和相关性排序\n    const sortedSuggestions = suggestions\n      .sort((a, b) => {\n        // 首先按优先级排序\n        if (a.priority !== b.priority) {\n          return b.priority - a.priority;\n        }\n        // 然后按文本相似度排序（简单的字符串匹配）\n        const aMatch = a.text.toLowerCase().indexOf(query.toLowerCase());\n        const bMatch = b.text.toLowerCase().indexOf(query.toLowerCase());\n        if (aMatch !== bMatch) {\n          return aMatch - bMatch; // 匹配位置越靠前越好\n        }\n        return a.text.length - b.text.length; // 长度越短越好\n      })\n      .slice(0, limit)\n      .map(({ priority, ...suggestion }) => suggestion); // 移除priority字段\n\n    return NextResponse.json({\n      success: true,\n      data: sortedSuggestions,\n      meta: {\n        query,\n        total: suggestions.length,\n        limit\n      }\n    });\n\n  } catch (error) {\n    console.error('API错误:', error);\n    return NextResponse.json(\n      { success: false, error: '服务器内部错误' },\n      { status: 500 }\n    );\n  }\n}\n\n\n"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,gBAAgB;;;;;AAEhB;AACA;;;AAEA,SAAS;AACT,SAAS,cAAc,IAAY,EAAE,KAAa;IAChD,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;IAE5B,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;IAC9E,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAEA,qBAAqB;AACrB,SAAS,wBAAwB,OAAY,EAAE,KAAa;IAC1D,IAAI,CAAC,SAAS,CAAC,SAAS,OAAO;IAE/B,MAAM,aAAa,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IACjF,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,IAAI,QAAQ;IACZ,MAAM,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE,EAAE,WAAW;IAC/C,MAAM,UAAU,CAAC,QAAQ,OAAO,IAAI,EAAE,EAAE,WAAW;IACnD,MAAM,cAAc,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,WAAW;IAE3D,WAAW,OAAO,CAAC,CAAA;QACjB,WAAW;QACX,MAAM,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,MAAM,SAAS,EAAE,EAAE,MAAM;QACtE,SAAS,eAAe;QAExB,WAAW;QACX,MAAM,cAAc,CAAC,YAAY,KAAK,CAAC,IAAI,OAAO,MAAM,SAAS,EAAE,EAAE,MAAM;QAC3E,SAAS,cAAc;QAEvB,WAAW;QACX,MAAM,iBAAiB,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,MAAM,SAAS,EAAE,EAAE,MAAM;QAC1E,SAAS,iBAAiB;QAE1B,SAAS;QACT,IAAI,MAAM,QAAQ,CAAC,OAAO,SAAS;QACnC,IAAI,YAAY,QAAQ,CAAC,OAAO,SAAS;IAC3C;IAEA,SAAS;IACT,IAAI,QAAQ,IAAI,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,GAAG;QAC/C,WAAW,OAAO,CAAC,CAAA;YACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,MACtC,IAAI,WAAW,GAAG,QAAQ,CAAC,OAC3B,MAAM;YACR,SAAS,aAAa;QACxB;IACF;IAEA,OAAO;AACT;AAEA,SAAS;AACT,SAAS,gBAAgB,QAAe,EAAE,KAAa,EAAE,MAAc,EAAE,SAAiB;IACxF,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,eAAe;IACf,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YACjD,GAAG,OAAO;YACV,gBAAgB,wBAAwB,SAAS;QACnD,CAAC;IAED,aAAa;IACb,OAAO,kBAAkB,IAAI,CAAC,CAAC,GAAG;QAChC,OAAQ;YACN,KAAK;gBACH,4BAA4B;gBAC5B,IAAI,EAAE,cAAc,KAAK,EAAE,cAAc,EAAE;oBACzC,OAAO,cAAc,QAAQ,EAAE,cAAc,GAAG,EAAE,cAAc,GAAG,EAAE,cAAc,GAAG,EAAE,cAAc;gBACxG;gBACA,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;YAE9E,KAAK;gBACH,MAAM,QAAQ,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;gBAC9C,MAAM,QAAQ,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;gBAC9C,OAAO,cAAc,QAAQ,QAAQ,QAAQ,QAAQ;YAEvD,KAAK;gBACH,MAAM,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,WAAW;gBAC1C,MAAM,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,WAAW;gBAC1C,OAAO,cAAc,QAAQ,OAAO,aAAa,CAAC,UAAU,OAAO,aAAa,CAAC;YAEnF;gBACE,WAAW;gBACX,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc;QAC9C;IACF;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ,aAAa,GAAG,CAAC;QACxD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa,aAAa,yBAAyB;QACnF,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,KAAK,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC;QAErE,SAAS;QACT,IAAI,cAAc,sIAAA,CAAA,WAAQ,CACvB,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC;QAEH,wBAAwB;QACxB,QAAQ,GAAG,CAAC,mBAAmB,iBAAiB;QAEhD,gBAAgB;QAChB,MAAM,kBAAkB,sIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,UAAU,CAAC,iBAAiB;QAE/B,2BAA2B;QAC3B,IAAI,UAAU;YACZ,gBAAgB,EAAE,CAAC,eAAe;QACpC;QACA,IAAI,YAAY;YACd,gBAAgB,EAAE,CAAC,cAAc;QACnC;QACA,IAAI,MAAM;YACR,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;YACpD,gBAAgB,QAAQ,CAAC,QAAQ;QACnC;QAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM;QAClE,QAAQ,GAAG,CAAC,mBAAmB,mBAAmB,UAAU;QAC5D,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,aAAa;QAEjB,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;YACxD,QAAQ,GAAG,CAAC;YAEZ,kBAAkB;YAClB,IAAI,aAAa,sIAAA,CAAA,WAAQ,CACtB,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,CAAC,aAAa,EAAE,MAAM,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAEvD,SAAS;YACT,IAAI,UAAU;gBACZ,aAAa,WAAW,EAAE,CAAC,eAAe;YAC5C;YACA,IAAI,YAAY;gBACd,aAAa,WAAW,EAAE,CAAC,cAAc;YAC3C;YACA,IAAI,MAAM;gBACR,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;gBACpD,aAAa,WAAW,QAAQ,CAAC,QAAQ;YAC3C;YAEA,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YACxD,QAAQ,GAAG,CAAC,gBAAgB,cAAc,UAAU;YACpD,QAAQ,GAAG,CAAC,cAAc;YAE1B,eAAe;YACf,aAAa;QACf;QAEA,UAAU;QACV,IAAI,cAAc;QAClB,IAAI,QAAQ;QACZ,IAAI,QAAQ,cAAc,UAAU;QAEpC,sBAAsB;QAEtB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,UAAU;QACtB,QAAQ,GAAG,CAAC,cAAc,aAAa,UAAU;QACjD,QAAQ,GAAG,CAAC,UAAU;QACtB,QAAQ,GAAG,CAAC,YAAY,aAAa,CAAC,EAAE;QAExC,mBAAmB;QACnB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,sIAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,sBACL,MAAM,CAAC;QACV,QAAQ,GAAG,CAAC,qBAAqB,SAAS,UAAU;QACpD,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,QAAQ,GAAG,CAAC,cAAc,SAAS,IAAI,CAAA,UAAW,QAAQ,KAAK,KAAK,EAAE;QACtE,QAAQ,GAAG,CAAC,cAAc,SAAS,CAAC,EAAE;QAEtC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;gBAAQ,SAAS,MAAM,OAAO;YAAC,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,gBAAgB,eAAe,EAAE,EAAE,OAAO,QAAQ;QAEzE,QAAQ,GAAG,CAAC,YAAY,eAAe,MAAM;QAC7C,QAAQ,GAAG,CAAC,aAAa,cAAc,CAAC,EAAE;QAE1C,OAAO;QACP,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAC5B,MAAM,oBAAoB,eAAe,KAAK,CAAC,QAAQ,SAAS;QAEhE,QAAQ,GAAG,CAAC,YAAY,kBAAkB,MAAM;QAChD,QAAQ,GAAG,CAAC,gBAAgB,MAAM,UAAU,OAAO,WAAW;QAE9D,WAAW;QACX,sIAAA,CAAA,WAAQ,CACL,IAAI,CAAC,kBACL,MAAM,CAAC;YACN;YACA,eAAe,SAAS;YACxB,YAAY;YACZ,YAAY;QACd,GACC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE;YACd,IAAI,OAAO,QAAQ,KAAK,CAAC,aAAa;QACxC;QAEF,sBAAsB;QACtB,MAAM,uBAAuB,kBAAkB,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC7D,GAAG,OAAO;gBACV,WAAW;oBACT,OAAO,cAAc,QAAQ,KAAK,EAAE;oBACpC,aAAa,cAAc,QAAQ,WAAW,IAAI,IAAI;gBACxD;gBACA,iBAAiB;gBACjB,gBAAgB,QAAQ,cAAc,IAAI;YAC5C,CAAC;QAED,QAAQ,GAAG,CAAC,aAAa,qBAAqB,MAAM;QACpD,QAAQ,GAAG,CAAC,aAAa,oBAAoB,CAAC,EAAE;QAChD,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,OAAO;gBACL,MAAM;gBACN;gBACA;gBACA;gBACA;gBACA;YACF;YACA,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS;gBAChB,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;YACvC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAU,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,GAAG;QAE7B,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM,EAAE;YACV;QACF;QAEA,aAAa;QACb,MAAM,CAAC,cAAc,YAAY,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpE,cAAc;YACd,sIAAA,CAAA,WAAQ,CACL,IAAI,CAAC,sBACL,MAAM,CAAC,sDACP,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAC3B,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ;YAE3B,YAAY;YACZ,sIAAA,CAAA,WAAQ,CACL,IAAI,CAAC,sBACL,MAAM,CAAC,4DACP,QAAQ,CAAC,QAAQ;gBAAC;aAAM,EACxB,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ;YAE3B,aAAa;YACb,sIAAA,CAAA,WAAQ,CACL,IAAI,CAAC,kBACL,MAAM,CAAC,SACP,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAC3B,GAAG,CAAC,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,IAAI,QAAQ;aACzF,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,MAAM,eAAe;SACjD;QAED,OAAO;QACP,IAAI,aAAa,KAAK,EAAE;YACtB,QAAQ,KAAK,CAAC,aAAa,aAAa,KAAK;QAC/C;QACA,IAAI,WAAW,KAAK,EAAE;YACpB,QAAQ,KAAK,CAAC,aAAa,WAAW,KAAK;QAC7C;QACA,IAAI,gBAAgB,KAAK,EAAE;YACzB,QAAQ,KAAK,CAAC,aAAa,gBAAgB,KAAK;QAClD;QAEA,SAAS;QACT,MAAM,cAAc,EAAE;QAEtB,kBAAkB;QAClB,IAAI,aAAa,IAAI,EAAE;YACrB,YAAY,IAAI,IAAI,aAAa,IAAI,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACpD,MAAM;oBACN,MAAM,QAAQ,KAAK;oBACnB,IAAI,QAAQ,EAAE;oBACd,UAAU,AAAC,QAAQ,oBAAoB,EAAU,QAAQ;oBACzD,UAAU,EAAE,QAAQ;gBACtB,CAAC;QACH;QAEA,WAAW;QACX,IAAI,WAAW,IAAI,EAAE;YACnB,YAAY,IAAI,IAAI,WAAW,IAAI,CAChC,MAAM,CAAC,CAAA,UAAW,CAAC,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE,GAAG,KAAK;aACpE,GAAG,CAAC,CAAA,UAAW,CAAC;oBACf,MAAM;oBACN,MAAM,QAAQ,KAAK;oBACnB,IAAI,QAAQ,EAAE;oBACd,UAAU,AAAC,QAAQ,oBAAoB,EAAU,QAAQ;oBACzD,UAAU;gBACZ,CAAC;QACL;QAEA,WAAW;QACX,IAAI,gBAAgB,IAAI,EAAE;YACxB,YAAY,IAAI,IAAI,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;oBACtD,MAAM;oBACN,MAAM,OAAO,KAAK;oBAClB,UAAU;gBACZ,CAAC;QACH;QAEA,aAAa;QACb,MAAM,oBAAoB,YACvB,IAAI,CAAC,CAAC,GAAG;YACR,WAAW;YACX,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,uBAAuB;YACvB,MAAM,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,WAAW;YAC7D,MAAM,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,WAAW;YAC7D,IAAI,WAAW,QAAQ;gBACrB,OAAO,SAAS,QAAQ,YAAY;YACtC;YACA,OAAO,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS;QACjD,GACC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,YAAY,GAAK,aAAa,eAAe;QAEpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,MAAM;gBACJ;gBACA,OAAO,YAAY,MAAM;gBACzB;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAU,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}