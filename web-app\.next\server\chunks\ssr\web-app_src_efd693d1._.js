module.exports = {

"[project]/web-app/src/contexts/NavigationContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NavigationContext": (()=>NavigationContext),
    "NavigationProvider": (()=>NavigationProvider),
    "useNavigation": (()=>useNavigation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - NavigationContext导航状态管理
// 管理知识库导航的全局状态，包含当前选中项、搜索状态、展开状态等
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
// 初始状态
const initialState = {
    isSidebarOpen: false,
    currentCategory: null,
    currentItem: null,
    searchQuery: '',
    isSearching: false,
    expandedCategories: new Set(),
    viewMode: 'grid'
};
// 创建Context
const NavigationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function NavigationProvider({ children, initialCategory, initialItem }) {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        ...initialState,
        currentCategory: initialCategory || null,
        currentItem: initialItem || null,
        expandedCategories: initialCategory ? new Set([
            initialCategory
        ]) : new Set()
    });
    // 侧边栏控制
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                isSidebarOpen: !prev.isSidebarOpen
            }));
    }, []);
    const openSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                isSidebarOpen: true
            }));
    }, []);
    const closeSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                isSidebarOpen: false
            }));
    }, []);
    // 选中项控制
    const setCurrentCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((categoryId)=>{
        setState((prev)=>({
                ...prev,
                currentCategory: categoryId,
                currentItem: null // 切换分类时清空当前知识点
            }));
    }, []);
    const setCurrentItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((itemId)=>{
        setState((prev)=>({
                ...prev,
                currentItem: itemId
            }));
    }, []);
    // 搜索控制
    const setSearchQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((query)=>{
        setState((prev)=>({
                ...prev,
                searchQuery: query
            }));
    }, []);
    const setIsSearching = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((searching)=>{
        setState((prev)=>({
                ...prev,
                isSearching: searching
            }));
    }, []);
    const clearSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                searchQuery: '',
                isSearching: false
            }));
    }, []);
    // 展开状态控制
    const toggleCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((categoryId)=>{
        setState((prev)=>{
            const newExpanded = new Set(prev.expandedCategories);
            if (newExpanded.has(categoryId)) {
                newExpanded.delete(categoryId);
            } else {
                newExpanded.add(categoryId);
            }
            return {
                ...prev,
                expandedCategories: newExpanded
            };
        });
    }, []);
    const expandCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((categoryId)=>{
        setState((prev)=>{
            const newExpanded = new Set(prev.expandedCategories);
            newExpanded.add(categoryId);
            return {
                ...prev,
                expandedCategories: newExpanded
            };
        });
    }, []);
    const collapseCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((categoryId)=>{
        setState((prev)=>{
            const newExpanded = new Set(prev.expandedCategories);
            newExpanded.delete(categoryId);
            return {
                ...prev,
                expandedCategories: newExpanded
            };
        });
    }, []);
    const expandAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        // 这里需要获取所有分类ID，暂时使用空Set
        // 在实际使用时会传入所有分类ID
        setState((prev)=>({
                ...prev,
                expandedCategories: new Set()
            }));
    }, []);
    const collapseAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                expandedCategories: new Set()
            }));
    }, []);
    // 视图模式控制
    const setViewMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((mode)=>{
        setState((prev)=>({
                ...prev,
                viewMode: mode
            }));
    }, []);
    // 重置状态
    const resetNavigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState(initialState);
    }, []);
    // Context值
    const contextValue = {
        // 状态
        ...state,
        // 操作
        toggleSidebar,
        openSidebar,
        closeSidebar,
        setCurrentCategory,
        setCurrentItem,
        setSearchQuery,
        setIsSearching,
        clearSearch,
        toggleCategory,
        expandCategory,
        collapseCategory,
        expandAll,
        collapseAll,
        setViewMode,
        resetNavigation
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(NavigationContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/web-app/src/contexts/NavigationContext.tsx",
        lineNumber: 212,
        columnNumber: 5
    }, this);
}
function useNavigation() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(NavigationContext);
    if (context === undefined) {
        throw new Error('useNavigation must be used within a NavigationProvider');
    }
    return context;
}
;
}}),
"[project]/web-app/src/lib/api/knowledge.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 知识库 API 客户端
// 提供类型安全的 API 调用方法
__turbopack_context__.s({
    "articlesApi": (()=>articlesApi),
    "categoriesApi": (()=>categoriesApi),
    "codeExamplesApi": (()=>codeExamplesApi),
    "searchApi": (()=>searchApi),
    "statsApi": (()=>statsApi)
});
class APICache {
    cache = new Map();
    defaultTTL = 5 * 60 * 1000;
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        return item.data;
    }
    set(key, data, ttl = this.defaultTTL) {
        const now = Date.now();
        this.cache.set(key, {
            data,
            timestamp: now,
            expiry: now + ttl
        });
    }
    clear() {
        this.cache.clear();
    }
    generateKey(endpoint, options) {
        const method = options?.method || 'GET';
        const body = options?.body || '';
        return `${method}:${endpoint}:${btoa(body).slice(0, 20)}`;
    }
}
// 全局API缓存实例
const apiCache = new APICache();
// 防抖管理
const debounceMap = new Map();
function debounce(func, delay, key) {
    return (...args)=>{
        return new Promise((resolve, reject)=>{
            // 清除之前的定时器
            if (debounceMap.has(key)) {
                clearTimeout(debounceMap.get(key));
            }
            // 设置新的定时器
            const timeoutId = setTimeout(async ()=>{
                try {
                    const result = await func(...args);
                    resolve(result);
                } catch (error) {
                    reject(error);
                } finally{
                    debounceMap.delete(key);
                }
            }, delay);
            debounceMap.set(key, timeoutId);
        });
    };
}
// 基础 API 调用函数
async function apiCall(endpoint, options) {
    try {
        // 构建正确的API URL
        let apiUrl;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // 服务端：构建完整的URL
            const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
            apiUrl = `${baseUrl}/api/knowledge${endpoint}`;
        }
        // 检查缓存（仅对GET请求）
        const method = options?.method || 'GET';
        if (method === 'GET') {
            const cacheKey = apiCache.generateKey(endpoint, options);
            const cachedData = apiCache.get(cacheKey);
            if (cachedData) {
                return cachedData;
            }
        }
        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30秒超时
        try {
            const response = await fetch(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options?.headers
                },
                signal: controller.signal,
                ...options
            });
            clearTimeout(timeoutId);
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('响应不是有效的JSON格式');
            }
            const data = await response.json();
            // 缓存成功的GET请求结果
            if (method === 'GET' && data.success) {
                const cacheKey = apiCache.generateKey(endpoint, options);
                // 搜索结果缓存时间较短，其他数据缓存时间较长
                const ttl = endpoint.includes('/search') ? 2 * 60 * 1000 : 5 * 60 * 1000;
                apiCache.set(cacheKey, data, ttl);
            }
            return data;
        } catch (fetchError) {
            clearTimeout(timeoutId);
            throw fetchError;
        }
    } catch (error) {
        console.error('API调用失败:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '网络错误'
        };
    }
}
const categoriesApi = {
    // 获取所有分类
    getAll: async (includeStats = false)=>{
        const params = includeStats ? '?includeStats=true' : '';
        return apiCall(`/categories${params}`);
    },
    // 获取单个分类
    getById: async (id, includeArticles = false)=>{
        const params = includeArticles ? '?includeArticles=true' : '';
        return apiCall(`/categories/${id}${params}`);
    },
    // 创建分类
    create: async (category)=>{
        return apiCall('/categories', {
            method: 'POST',
            body: JSON.stringify(category)
        });
    },
    // 更新分类
    update: async (id, updates)=>{
        return apiCall(`/categories/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除分类
    delete: async (id)=>{
        return apiCall(`/categories/${id}`, {
            method: 'DELETE'
        });
    },
    // 批量更新排序
    updateOrder: async (categories)=>{
        return apiCall('/categories', {
            method: 'PUT',
            body: JSON.stringify({
                categories
            })
        });
    }
};
const articlesApi = {
    // 获取文章列表
    getAll: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.category) searchParams.set('category', params.category);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.tags) searchParams.set('tags', params.tags);
        if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        if (params?.includeCodeExamples) searchParams.set('includeCodeExamples', 'true');
        if (params?.includeRelated) searchParams.set('includeRelated', 'true');
        const query = searchParams.toString();
        return apiCall(`/articles${query ? `?${query}` : ''}`);
    },
    // 获取单个文章
    getById: async (id, options)=>{
        const params = new URLSearchParams();
        if (options?.includeCodeExamples === false) params.set('includeCodeExamples', 'false');
        if (options?.includeRelated === false) params.set('includeRelated', 'false');
        const query = params.toString();
        return apiCall(`/articles/${id}${query ? `?${query}` : ''}`);
    },
    // 创建文章
    create: async (article)=>{
        return apiCall('/articles', {
            method: 'POST',
            body: JSON.stringify(article)
        });
    },
    // 更新文章
    update: async (id, updates)=>{
        return apiCall(`/articles/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除文章
    delete: async (id)=>{
        return apiCall(`/articles/${id}`, {
            method: 'DELETE'
        });
    },
    // 搜索文章（带防抖）
    search: debounce(async (query, params)=>{
        const searchParams = new URLSearchParams();
        searchParams.set('search', query);
        if (params?.category) searchParams.set('category', params.category);
        if (params?.tags) searchParams.set('tags', params.tags);
        if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        return apiCall(`/articles?${searchParams.toString()}`);
    }, 300, 'articles-search')
};
const codeExamplesApi = {
    // 获取代码示例列表
    getAll: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.articleId) searchParams.set('articleId', params.articleId);
        if (params?.language) searchParams.set('language', params.language);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        const query = searchParams.toString();
        return apiCall(`/code-examples${query ? `?${query}` : ''}`);
    },
    // 创建代码示例
    create: async (example)=>{
        return apiCall('/code-examples', {
            method: 'POST',
            body: JSON.stringify(example)
        });
    },
    // 更新代码示例
    update: async (id, updates)=>{
        return apiCall(`/code-examples/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除代码示例
    delete: async (id)=>{
        return apiCall(`/code-examples/${id}`, {
            method: 'DELETE'
        });
    },
    // 批量更新排序
    updateOrder: async (examples)=>{
        return apiCall('/code-examples', {
            method: 'PUT',
            body: JSON.stringify({
                examples
            })
        });
    }
};
const searchApi = {
    // 搜索文章（带防抖和缓存）
    search: debounce(async (params)=>{
        const searchParams = new URLSearchParams();
        searchParams.set('q', params.query);
        if (params.category) searchParams.set('category', params.category);
        if (params.tags) searchParams.set('tags', params.tags);
        if (params.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params.page) searchParams.set('page', params.page.toString());
        if (params.limit) searchParams.set('limit', params.limit.toString());
        if (params.sortBy) searchParams.set('sortBy', params.sortBy);
        if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);
        return apiCall(`/search?${searchParams.toString()}`);
    }, 300, 'search-main'),
    // 获取搜索建议（带防抖）
    getSuggestions: debounce(async (query, limit = 5)=>{
        // 查询长度小于2时直接返回空结果，避免无意义的API调用
        if (!query || query.trim().length < 2) {
            return {
                success: true,
                data: []
            };
        }
        return apiCall('/search', {
            method: 'POST',
            body: JSON.stringify({
                query: query.trim(),
                limit
            })
        });
    }, 200, 'search-suggestions'),
    // 清除搜索缓存
    clearCache: ()=>{
        apiCache.clear();
    },
    // 取消所有防抖请求
    cancelPendingRequests: ()=>{
        debounceMap.forEach((timeoutId)=>{
            clearTimeout(timeoutId);
        });
        debounceMap.clear();
    }
};
const statsApi = {
    // 获取统计数据
    get: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.period) searchParams.set('period', params.period);
        if (params?.includeSearchStats) searchParams.set('includeSearchStats', 'true');
        const query = searchParams.toString();
        return apiCall(`/stats${query ? `?${query}` : ''}`);
    },
    // 导出统计数据
    export: async (format = 'json', includeDetails = false)=>{
        return apiCall('/stats/export', {
            method: 'POST',
            body: JSON.stringify({
                format,
                includeDetails
            })
        });
    }
};
}}),
"[project]/web-app/src/lib/knowledge.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 知识库数据管理
// 提供知识库相关的数据结构、类型定义和数据访问函数
// 所有数据完全来自数据库，不再使用任何硬编码数据
__turbopack_context__.s({
    "getAllTags": (()=>getAllTags),
    "getKnowledgeCategories": (()=>getKnowledgeCategories),
    "getKnowledgeCategory": (()=>getKnowledgeCategory),
    "getKnowledgeItem": (()=>getKnowledgeItem),
    "getKnowledgeItemById": (()=>getKnowledgeItemById),
    "getKnowledgeItems": (()=>getKnowledgeItems),
    "getKnowledgeItemsByCategory": (()=>getKnowledgeItemsByCategory),
    "getKnowledgeItemsByTag": (()=>getKnowledgeItemsByTag),
    "getKnowledgeStats": (()=>getKnowledgeStats),
    "getPopularKnowledgeItems": (()=>getPopularKnowledgeItems),
    "getRecentKnowledgeItems": (()=>getRecentKnowledgeItems),
    "getRelatedKnowledgeItems": (()=>getRelatedKnowledgeItems),
    "searchKnowledgeItems": (()=>searchKnowledgeItems)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/api/knowledge.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
async function getPopularKnowledgeItems(maxItems = 6) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll({
            includeCodeExamples: true,
            includeRelated: true
        });
        if (response.success && response.data) {
            return response.data.sort((a, b)=>a.order_index - b.order_index).slice(0, maxItems);
        }
    } catch (error) {
        console.error('获取热门知识点失败:', error);
    }
    // 如果API调用失败，返回空数组（不再使用硬编码降级）
    return [];
}
async function getRecentKnowledgeItems(maxItems = 6) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll({
            includeCodeExamples: true,
            includeRelated: true
        });
        if (response.success && response.data) {
            return response.data.sort((a, b)=>new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime()).slice(0, maxItems);
        }
    } catch (error) {
        console.error('获取最近更新知识点失败:', error);
    }
    // 如果API调用失败，返回空数组
    return [];
}
async function getKnowledgeCategories() {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["categoriesApi"].getAll(true);
        if (response.success && response.data) {
            return response.data.sort((a, b)=>a.order_index - b.order_index);
        }
    } catch (error) {
        console.error('获取知识分类失败:', error);
    }
    // 如果API调用失败，返回空数组（不再使用硬编码降级）
    return [];
}
async function getKnowledgeItem(itemId) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getById(itemId, {
            includeCodeExamples: true,
            includeRelated: true
        });
        if (response.success && response.data) {
            const frontendArticles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mapDatabaseArticlesToFrontend"])([
                response.data
            ]);
            return frontendArticles[0] || null;
        }
    } catch (error) {
        console.error('获取知识点失败:', error);
    }
    return null;
}
async function getKnowledgeItemsByCategory(categoryId) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll({
            category: categoryId,
            includeCodeExamples: true,
            includeRelated: true
        });
        if (response.success && response.data) {
            return response.data.sort((a, b)=>a.order_index - b.order_index);
        }
    } catch (error) {
        console.error('获取分类知识点失败:', error);
    }
    return [];
}
async function searchKnowledgeItems(query) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].search(query);
        if (response.success && response.data) {
            return response.data;
        }
    } catch (error) {
        console.error('搜索知识点失败:', error);
    }
    return [];
}
async function getRelatedKnowledgeItems(currentItem, maxItems = 5) {
    try {
        // 基于分类获取相关文章
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll({
            category: currentItem.category_id || undefined,
            includeCodeExamples: true,
            includeRelated: true
        });
        if (response.success && response.data) {
            return response.data.filter((item)=>item.id !== currentItem.id) // 排除当前文章
            .slice(0, maxItems);
        }
    } catch (error) {
        console.error('获取相关知识点失败:', error);
    }
    return [];
}
async function getAllTags() {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll();
        if (response.success && response.data) {
            const allTags = response.data.flatMap((item)=>item.tags || []);
            return Array.from(new Set(allTags)).sort();
        }
    } catch (error) {
        console.error('获取标签失败:', error);
    }
    return [];
}
async function getKnowledgeItems() {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll();
        if (response.success && response.data) {
            return response.data.sort((a, b)=>a.order_index - b.order_index);
        }
    } catch (error) {
        console.error('获取知识点失败:', error);
    }
    return [];
}
async function getKnowledgeItemsByTag(tag) {
    try {
        // 使用搜索API查找包含特定标签的文章
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].search(tag);
        if (response.success && response.data) {
            // 过滤出真正包含该标签的文章
            return response.data.filter((item)=>item.tags?.some((itemTag)=>itemTag.toLowerCase().includes(tag.toLowerCase())));
        }
    } catch (error) {
        console.error('根据标签获取知识点失败:', error);
    }
    return [];
}
async function getKnowledgeStats() {
    try {
        const [categoriesResponse, articlesResponse] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["categoriesApi"].getAll(true),
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"].getAll({
                includeCodeExamples: false,
                includeRelated: false
            })
        ]);
        const categoriesCount = categoriesResponse.success ? categoriesResponse.data?.length || 0 : 0;
        const articlesCount = articlesResponse.success ? articlesResponse.data?.length || 0 : 0;
        return {
            categoriesCount,
            articlesCount,
            totalItems: articlesCount
        };
    } catch (error) {
        console.error('获取知识库统计失败:', error);
        return {
            categoriesCount: 0,
            articlesCount: 0,
            totalItems: 0
        };
    }
}
async function getKnowledgeItemById(itemId) {
    return getKnowledgeItem(itemId);
}
async function getKnowledgeCategory(categoryId) {
    try {
        const categories = await getKnowledgeCategories();
        return categories.find((category)=>category.id === categoryId) || null;
    } catch (error) {
        console.error('获取知识分类失败:', error);
        return null;
    }
}
}}),
"[project]/web-app/src/hooks/useKnowledgeData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useKnowledgeData": (()=>useKnowledgeData)
});
// MySQLAi.de - 知识库统一数据管理Hook
// 提供统一的数据获取、缓存、错误处理和加载状态管理
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/api/knowledge.ts [app-ssr] (ecmascript)");
'use client';
;
;
// 默认配置（优化性能）
const DEFAULT_OPTIONS = {
    autoFetch: true,
    cacheTime: 5 * 60 * 1000,
    retryCount: 2,
    retryDelay: 1000,
    optimisticUpdates: false,
    backgroundRefresh: false,
    debug: false,
    progressiveLoading: true,
    prioritizeBasicData: true // 优先加载基础数据
};
// API映射
const API_MAP = {
    articles: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["articlesApi"],
    categories: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["categoriesApi"],
    codeExamples: __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["codeExamplesApi"]
};
function useKnowledgeData(dataType, params, options = {}) {
    const opts = {
        ...DEFAULT_OPTIONS,
        ...options
    };
    const api = API_MAP[dataType];
    // 稳定化 params 引用，避免无限循环
    const stableParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(params);
    const [paramsKey, setParamsKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(JSON.stringify(params || {}));
    // 状态管理
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        data: [],
        loading: false,
        error: null,
        lastFetch: null,
        isStale: false,
        retryCount: 0
    });
    // 引用管理
    const abortControllerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const retryTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const cacheTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // 调试日志
    const log = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, data)=>{
        if (opts.debug) {
            console.log(`[useKnowledgeData:${dataType}] ${message}`, data);
        }
    }, [
        dataType,
        opts.debug
    ]);
    // 设置缓存过期（增加更长的缓存时间以减少请求频率）
    const setCacheTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (cacheTimeoutRef.current) {
            clearTimeout(cacheTimeoutRef.current);
        }
        // 使用更长的缓存时间，最少15分钟
        const cacheTime = Math.max(opts.cacheTime || 0, 15 * 60 * 1000);
        if (cacheTime > 0) {
            cacheTimeoutRef.current = setTimeout(()=>{
                setState((prev)=>({
                        ...prev,
                        isStale: true
                    }));
                log('Cache expired, data marked as stale');
            }, cacheTime);
        }
    }, [
        opts.cacheTime,
        log
    ]);
    // 更新 params 引用
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const newParamsKey = JSON.stringify(params || {});
        if (newParamsKey !== paramsKey) {
            stableParams.current = params;
            setParamsKey(newParamsKey);
        }
    }, [
        params,
        paramsKey
    ]);
    // 获取数据
    const fetchData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (isRetry = false)=>{
        // 取消之前的请求
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();
        setState((prev)=>({
                ...prev,
                loading: true,
                error: isRetry ? prev.error : null
            }));
        const currentParams = stableParams.current;
        log('Fetching data', {
            params: currentParams,
            isRetry
        });
        try {
            let response;
            // 渐进加载逻辑
            if (opts.progressiveLoading && dataType === 'articles' && opts.prioritizeBasicData) {
                // 第一步：快速加载基础数据（不包含代码示例和相关文章）
                const basicParams = {
                    ...currentParams,
                    includeCodeExamples: false,
                    includeRelated: false
                };
                log('Progressive loading: Fetching basic data first', basicParams);
                const basicResponse = await api.getAll(basicParams);
                console.log('=== useKnowledgeData 渐进加载调试 ===');
                console.log('基础数据响应:', basicResponse);
                if (basicResponse.success && basicResponse.data) {
                    console.log('基础数据加载成功，数据数量:', basicResponse.data.length);
                    // 立即显示基础数据
                    setState((prev)=>({
                            ...prev,
                            data: basicResponse.data,
                            loading: true,
                            error: null,
                            lastFetch: new Date(),
                            isStale: false,
                            retryCount: 0
                        }));
                    log('Basic data loaded, now fetching detailed data');
                    console.log('准备加载详细数据，参数:', currentParams);
                    // 第二步：后台加载详细数据
                    if (currentParams?.includeCodeExamples || currentParams?.includeRelated) {
                        setTimeout(async ()=>{
                            try {
                                console.log('开始加载详细数据...');
                                const detailedResponse = await api.getAll(currentParams);
                                console.log('详细数据响应:', detailedResponse);
                                if (detailedResponse.success && detailedResponse.data) {
                                    console.log('详细数据加载成功，数据数量:', detailedResponse.data.length);
                                    setState((prev)=>({
                                            ...prev,
                                            data: detailedResponse.data,
                                            loading: false,
                                            error: null,
                                            lastFetch: new Date(),
                                            isStale: false,
                                            retryCount: 0
                                        }));
                                    setCacheTimeout();
                                    log('Detailed data loaded');
                                } else {
                                    console.error('详细数据加载失败:', detailedResponse.error);
                                    // 保持基础数据，只关闭loading状态
                                    setState((prev)=>({
                                            ...prev,
                                            loading: false
                                        }));
                                }
                            } catch (error) {
                                console.error('详细数据加载异常:', error);
                                log('Failed to load detailed data', error);
                                // 保持基础数据，只关闭loading状态
                                setState((prev)=>({
                                        ...prev,
                                        loading: false
                                    }));
                            }
                        }, 100); // 100ms延迟，让基础数据先渲染
                    } else {
                        // 如果不需要详细数据，直接完成
                        setState((prev)=>({
                                ...prev,
                                loading: false
                            }));
                    }
                } else {
                    throw new Error(basicResponse.error || 'Failed to fetch basic data');
                }
            } else {
                // 传统加载方式
                switch(dataType){
                    case 'articles':
                        response = await api.getAll(currentParams);
                        break;
                    case 'categories':
                        response = await api.getAll(currentParams?.includeStats);
                        break;
                    case 'codeExamples':
                        response = await api.getAll(currentParams);
                        break;
                    default:
                        throw new Error(`Unsupported data type: ${dataType}`);
                }
                if (response.success && response.data) {
                    setState((prev)=>({
                            ...prev,
                            data: response.data,
                            loading: false,
                            error: null,
                            lastFetch: new Date(),
                            isStale: false,
                            retryCount: 0
                        }));
                    setCacheTimeout();
                    log('Data fetched successfully', response.data);
                } else {
                    throw new Error(response.error || 'Failed to fetch data');
                }
            }
        } catch (error) {
            const err = error;
            if (err.name === 'AbortError') {
                log('Request aborted');
                return;
            }
            const errorMessage = err.message || 'Unknown error occurred';
            setState((prev)=>{
                const newRetryCount = prev.retryCount + 1;
                log('Fetch error', {
                    error: errorMessage,
                    retryCount: newRetryCount
                });
                // 自动重试（使用最新的retryCount）
                if (newRetryCount < opts.retryCount && !isRetry) {
                    retryTimeoutRef.current = setTimeout(()=>{
                        fetchData(true);
                    }, opts.retryDelay);
                }
                return {
                    ...prev,
                    loading: false,
                    error: errorMessage,
                    retryCount: newRetryCount
                };
            });
        }
    }, [
        dataType,
        api,
        log,
        setCacheTimeout,
        opts.retryCount,
        opts.retryDelay
    ]);
    // 刷新数据
    const refresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        setState((prev)=>({
                ...prev,
                isStale: false
            }));
        await fetchData();
    }, [
        fetchData
    ]);
    // 重新获取数据
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        await fetchData();
    }, [
        fetchData
    ]);
    // 使缓存失效
    const invalidate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                isStale: true
            }));
        if (cacheTimeoutRef.current) {
            clearTimeout(cacheTimeoutRef.current);
        }
        log('Cache invalidated');
    }, [
        log
    ]);
    // 重试
    const retry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        setState((prev)=>({
                ...prev,
                retryCount: 0
            }));
        await fetchData(true);
    }, [
        fetchData
    ]);
    // 创建项目
    const create = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (item)=>{
        try {
            log('Creating item', item);
            // 乐观更新
            if (opts.optimisticUpdates) {
                const tempId = `temp_${Date.now()}`;
                const tempItem = {
                    ...item,
                    id: tempId
                };
                setState((prev)=>({
                        ...prev,
                        data: [
                            ...prev.data,
                            tempItem
                        ]
                    }));
            }
            const response = await api.create(item);
            if (response.success) {
                // 刷新数据以获取最新状态
                await refetch();
                log('Item created successfully');
                return true;
            } else {
                throw new Error(response.error || 'Failed to create item');
            }
        } catch (error) {
            const err = error;
            log('Create error', err.message);
            // 回滚乐观更新
            if (opts.optimisticUpdates) {
                await refetch();
            }
            setState((prev)=>({
                    ...prev,
                    error: err.message
                }));
            return false;
        }
    }, [
        api,
        opts.optimisticUpdates,
        refetch,
        log
    ]);
    // 更新项目
    const update = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (id, item)=>{
        try {
            log('Updating item', {
                id,
                item
            });
            // 乐观更新
            if (opts.optimisticUpdates) {
                setState((prev)=>({
                        ...prev,
                        data: prev.data.map((d)=>d.id === id ? {
                                ...d,
                                ...item
                            } : d)
                    }));
            }
            const response = await api.update(id, item);
            if (response.success) {
                // 刷新数据以获取最新状态
                await refetch();
                log('Item updated successfully');
                return true;
            } else {
                throw new Error(response.error || 'Failed to update item');
            }
        } catch (error) {
            log('Update error', error.message);
            // 回滚乐观更新
            if (opts.optimisticUpdates) {
                await refetch();
            }
            setState((prev)=>({
                    ...prev,
                    error: error.message
                }));
            return false;
        }
    }, [
        api,
        opts.optimisticUpdates,
        refetch,
        log
    ]);
    // 删除项目
    const remove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (id)=>{
        try {
            log('Removing item', {
                id
            });
            // 乐观更新
            if (opts.optimisticUpdates) {
                setState((prev)=>({
                        ...prev,
                        data: prev.data.filter((d)=>d.id !== id)
                    }));
            }
            const response = await api.delete(id);
            if (response.success) {
                // 刷新数据以获取最新状态
                await refetch();
                log('Item removed successfully');
                return true;
            } else {
                throw new Error(response.error || 'Failed to remove item');
            }
        } catch (error) {
            log('Remove error', error.message);
            // 回滚乐观更新
            if (opts.optimisticUpdates) {
                await refetch();
            }
            setState((prev)=>({
                    ...prev,
                    error: error.message
                }));
            return false;
        }
    }, [
        api,
        opts.optimisticUpdates,
        refetch,
        log
    ]);
    // 批量创建
    const batchCreate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (items)=>{
        try {
            log('Batch creating items', {
                count: items.length
            });
            const results = await Promise.all(items.map((item)=>api.create(item)));
            const allSuccess = results.every((r)=>r.success);
            if (allSuccess) {
                await refetch();
                log('Batch create successful');
                return true;
            } else {
                throw new Error('Some items failed to create');
            }
        } catch (error) {
            log('Batch create error', error.message);
            setState((prev)=>({
                    ...prev,
                    error: error.message
                }));
            return false;
        }
    }, [
        api,
        refetch,
        log
    ]);
    // 批量更新
    const batchUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (items)=>{
        try {
            log('Batch updating items', {
                count: items.length
            });
            const results = await Promise.all(items.map((item)=>api.update(item.id, item.data)));
            const allSuccess = results.every((r)=>r.success);
            if (allSuccess) {
                await refetch();
                log('Batch update successful');
                return true;
            } else {
                throw new Error('Some items failed to update');
            }
        } catch (error) {
            log('Batch update error', error.message);
            setState((prev)=>({
                    ...prev,
                    error: error.message
                }));
            return false;
        }
    }, [
        api,
        refetch,
        log
    ]);
    // 批量删除
    const batchRemove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (ids)=>{
        try {
            log('Batch removing items', {
                count: ids.length
            });
            const results = await Promise.all(ids.map((id)=>api.delete(id)));
            const allSuccess = results.every((r)=>r.success);
            if (allSuccess) {
                await refetch();
                log('Batch remove successful');
                return true;
            } else {
                throw new Error('Some items failed to remove');
            }
        } catch (error) {
            log('Batch remove error', error.message);
            setState((prev)=>({
                    ...prev,
                    error: error.message
                }));
            return false;
        }
    }, [
        api,
        refetch,
        log
    ]);
    // 根据ID查找
    const findById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        return state.data.find((item)=>item.id === id);
    }, [
        state.data
    ]);
    // 根据字段查找
    const findByField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field, value)=>{
        return state.data.filter((item)=>item[field] === value);
    }, [
        state.data
    ]);
    // 自动获取数据
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (opts.autoFetch && !state.loading && !state.data.length) {
            fetchData();
        }
        // 清理函数
        return ()=>{
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
            }
            if (cacheTimeoutRef.current) {
                clearTimeout(cacheTimeoutRef.current);
            }
        };
    }, [
        opts.autoFetch,
        state.loading,
        state.data.length,
        fetchData
    ]); // 重新添加依赖
    // 后台刷新（禁用以避免无限循环）
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (opts.backgroundRefresh && state.isStale && !state.loading) {
            log('Background refresh triggered');
        // 暂时禁用自动后台刷新以避免无限循环
        // fetchData();
        }
    }, [
        opts.backgroundRefresh,
        state.isStale,
        state.loading,
        log
    ]);
    return {
        // 数据状态
        data: state.data,
        loading: state.loading,
        error: state.error,
        isStale: state.isStale,
        // 操作方法
        refetch,
        refresh,
        invalidate,
        retry,
        // CRUD操作
        create,
        update,
        remove,
        // 批量操作
        batchCreate,
        batchUpdate,
        batchRemove,
        // 工具方法
        findById,
        findByField,
        count: state.data.length
    };
}
}}),
"[project]/web-app/src/lib/api/search-history.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 搜索历史 API 客户端
// 提供类型安全的搜索历史 API 调用方法
__turbopack_context__.s({
    "hybridSearchHistory": (()=>hybridSearchHistory),
    "localSearchHistory": (()=>localSearchHistory),
    "searchHistoryApi": (()=>searchHistoryApi)
});
// API 基础配置
const API_BASE = '/api/knowledge/search-history';
// 通用 API 调用函数
async function apiCall(endpoint, options = {}) {
    try {
        const response = await fetch(`${API_BASE}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('API调用失败:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '网络错误'
        };
    }
}
const searchHistoryApi = {
    // 获取搜索历史
    getHistory: async (options = {})=>{
        const params = new URLSearchParams();
        if (options.limit) params.append('limit', options.limit.toString());
        if (options.offset) params.append('offset', options.offset.toString());
        if (options.query) params.append('query', options.query);
        const queryString = params.toString();
        const endpoint = queryString ? `?${queryString}` : '';
        return apiCall(endpoint);
    },
    // 添加搜索记录
    addSearchRecord: async (query, resultsCount = 0)=>{
        return apiCall('', {
            method: 'POST',
            body: JSON.stringify({
                query: query.trim(),
                results_count: resultsCount
            })
        });
    },
    // 删除特定搜索记录
    deleteRecord: async (id)=>{
        return apiCall(`?id=${id}`, {
            method: 'DELETE'
        });
    },
    // 清除所有搜索历史
    clearAllHistory: async ()=>{
        return apiCall('?clearAll=true', {
            method: 'DELETE'
        });
    },
    // 搜索历史记录（在历史中查找）
    searchInHistory: async (query, limit = 10)=>{
        return apiCall(`?query=${encodeURIComponent(query)}&limit=${limit}`);
    }
};
const localSearchHistory = {
    // 本地存储键名
    STORAGE_KEY: 'mysqlai_search_history',
    // 获取本地搜索历史
    getLocal: ()=>{
        try {
            const stored = localStorage.getItem(localSearchHistory.STORAGE_KEY);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('获取本地搜索历史失败:', error);
            return [];
        }
    },
    // 保存到本地存储
    saveLocal: (items)=>{
        try {
            localStorage.setItem(localSearchHistory.STORAGE_KEY, JSON.stringify(items));
        } catch (error) {
            console.error('保存本地搜索历史失败:', error);
        }
    },
    // 添加搜索记录到本地
    addLocal: (query, resultsCount = 0)=>{
        const items = localSearchHistory.getLocal();
        const newItem = {
            id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            query: query.trim(),
            results_count: resultsCount,
            created_at: new Date().toISOString()
        };
        // 检查是否已存在相同查询（最近10条）
        const recentItems = items.slice(0, 10);
        const existingIndex = recentItems.findIndex((item)=>item.query.toLowerCase() === query.toLowerCase().trim());
        if (existingIndex >= 0) {
            // 如果存在，移动到最前面
            const existingItem = items.splice(existingIndex, 1)[0];
            items.unshift({
                ...existingItem,
                results_count: resultsCount,
                created_at: new Date().toISOString()
            });
        } else {
            // 如果不存在，添加到最前面
            items.unshift(newItem);
        }
        // 限制最多保存50条记录
        const limitedItems = items.slice(0, 50);
        localSearchHistory.saveLocal(limitedItems);
    },
    // 删除本地搜索记录
    deleteLocal: (id)=>{
        const items = localSearchHistory.getLocal();
        const filteredItems = items.filter((item)=>item.id !== id);
        localSearchHistory.saveLocal(filteredItems);
    },
    // 清除所有本地搜索历史
    clearLocal: ()=>{
        try {
            localStorage.removeItem(localSearchHistory.STORAGE_KEY);
        } catch (error) {
            console.error('清除本地搜索历史失败:', error);
        }
    }
};
const hybridSearchHistory = {
    // 获取搜索历史（API优先，本地降级）
    getHistory: async (limit = 10)=>{
        try {
            const response = await searchHistoryApi.getHistory({
                limit
            });
            if (response.success && response.data) {
                return response.data;
            }
        } catch (error) {
            console.warn('API获取搜索历史失败，使用本地存储:', error);
        }
        // 降级到本地存储
        return localSearchHistory.getLocal().slice(0, limit);
    },
    // 添加搜索记录（API优先，本地降级）
    addRecord: async (query, resultsCount = 0)=>{
        try {
            const response = await searchHistoryApi.addSearchRecord(query, resultsCount);
            if (response.success) {
                return;
            }
        } catch (error) {
            console.warn('API保存搜索历史失败，使用本地存储:', error);
        }
        // 降级到本地存储
        localSearchHistory.addLocal(query, resultsCount);
    },
    // 删除搜索记录（API优先，本地降级）
    deleteRecord: async (id)=>{
        try {
            if (id.startsWith('local_')) {
                // 本地记录直接删除
                localSearchHistory.deleteLocal(id);
                return;
            }
            const response = await searchHistoryApi.deleteRecord(id);
            if (response.success) {
                return;
            }
        } catch (error) {
            console.warn('API删除搜索记录失败:', error);
        }
    },
    // 清除所有搜索历史（API优先，本地降级）
    clearAll: async ()=>{
        try {
            const response = await searchHistoryApi.clearAllHistory();
            if (response.success) {
                // API成功后也清除本地存储
                localSearchHistory.clearLocal();
                return;
            }
        } catch (error) {
            console.warn('API清除搜索历史失败，使用本地存储:', error);
        }
        // 降级到本地存储
        localSearchHistory.clearLocal();
    }
};
}}),
"[project]/web-app/src/components/ui/Button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - Button按钮组件
// 可复用的按钮组件，支持多种变体和尺寸
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const Button = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef(({ className, variant = 'primary', size = 'md', href, onClick, disabled = false, loading = false, icon, iconPosition = 'left', type = 'button', children, ...props }, ref)=>{
    // 基础样式
    const baseStyles = [
        'inline-flex items-center justify-center font-semibold rounded-lg',
        'transition-all duration-300 ease-out',
        'focus:outline-none focus:ring-4',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'disabled:hover:scale-100 disabled:hover:shadow-none'
    ].join(' ');
    // 变体样式
    const variantStyles = {
        primary: [
            'bg-mysql-primary text-white shadow-lg',
            'hover:bg-mysql-primary-dark hover:shadow-xl hover:scale-105',
            'focus:ring-mysql-primary/30',
            'active:scale-95'
        ].join(' '),
        secondary: [
            'bg-white text-mysql-primary border-2 border-mysql-primary shadow-lg',
            'hover:bg-mysql-primary hover:text-white hover:shadow-xl hover:scale-105',
            'focus:ring-mysql-primary/30',
            'active:scale-95'
        ].join(' '),
        outline: [
            'bg-transparent text-mysql-primary border-2 border-mysql-primary',
            'hover:bg-mysql-primary hover:text-white hover:shadow-lg hover:scale-105',
            'focus:ring-mysql-primary/30',
            'active:scale-95'
        ].join(' '),
        ghost: [
            'bg-transparent text-mysql-primary',
            'hover:bg-mysql-primary-light hover:scale-105',
            'focus:ring-mysql-primary/30',
            'active:scale-95'
        ].join(' ')
    };
    // 尺寸样式
    const sizeStyles = {
        sm: 'px-4 py-2 text-sm',
        md: 'px-6 py-3 text-base',
        lg: 'px-8 py-4 text-lg'
    };
    // 组合样式
    const buttonStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(baseStyles, variantStyles[variant], sizeStyles[size], className);
    // 图标渲染
    const renderIcon = ()=>{
        if (loading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                className: "w-4 h-4 animate-spin"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/Button.tsx",
                lineNumber: 88,
                columnNumber: 14
            }, this);
        }
        return icon;
    };
    // 内容渲染
    const renderContent = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                (icon || loading) && iconPosition === 'left' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center', children && 'mr-2'),
                    children: renderIcon()
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/Button.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                children,
                icon && iconPosition === 'right' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center', children && 'ml-2'),
                    children: renderIcon()
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/Button.tsx",
                    lineNumber: 103,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    // 如果有href，渲染为Link
    if (href) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
            whileHover: {
                scale: disabled ? 1 : 1.05,
                y: disabled ? 0 : -2
            },
            whileTap: {
                scale: disabled ? 1 : 0.95
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                href: href,
                className: buttonStyles,
                ...props,
                ref: ref,
                children: renderContent()
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/Button.tsx",
                lineNumber: 117,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/Button.tsx",
            lineNumber: 113,
            columnNumber: 7
        }, this);
    }
    // 渲染为button
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].button, {
        whileHover: {
            scale: disabled ? 1 : 1.05,
            y: disabled ? 0 : -2
        },
        whileTap: {
            scale: disabled ? 1 : 0.95
        },
        className: buttonStyles,
        onClick: onClick,
        disabled: disabled || loading,
        type: type,
        ...props,
        ref: ref,
        children: renderContent()
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/Button.tsx",
        lineNumber: 131,
        columnNumber: 5
    }, this);
});
Button.displayName = 'Button';
const __TURBOPACK__default__export__ = Button;
}}),
"[project]/web-app/src/components/ui/PageLoader.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContentLoader": (()=>ContentLoader),
    "PageTransitionLoader": (()=>PageTransitionLoader),
    "SimpleLoader": (()=>SimpleLoader),
    "SkeletonLoader": (()=>SkeletonLoader),
    "default": (()=>PageLoader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - 页面加载动画组件
// 专业的页面加载指示器，提供流畅的用户体验
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-ssr] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function PageLoader({ isLoading = true, message = '加载中...', className }) {
    const [progress, setProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading) return;
        const interval = setInterval(()=>{
            setProgress((prev)=>{
                if (prev >= 90) return prev;
                return prev + Math.random() * 10;
            });
        }, 200);
        return ()=>clearInterval(interval);
    }, [
        isLoading
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading) {
            setProgress(100);
        }
    }, [
        isLoading
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 1
            },
            exit: {
                opacity: 0
            },
            transition: {
                duration: 0.5,
                ease: "easeOut"
            },
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('fixed inset-0 bg-white z-50 flex flex-col items-center justify-center', className),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center space-y-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                scale: 0.8,
                                opacity: 0
                            },
                            animate: {
                                scale: 1,
                                opacity: 1
                            },
                            transition: {
                                duration: 0.6,
                                ease: "easeOut"
                            },
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-center w-20 h-20 bg-mysql-primary rounded-2xl shadow-lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        animate: {
                                            rotate: 360
                                        },
                                        transition: {
                                            duration: 2,
                                            repeat: Infinity,
                                            ease: "linear"
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                                            className: "w-10 h-10 text-white"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                            lineNumber: 69,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                        lineNumber: 65,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 64,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    animate: {
                                        scale: [
                                            1,
                                            1.2,
                                            1
                                        ],
                                        opacity: [
                                            0.5,
                                            0,
                                            0.5
                                        ]
                                    },
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: "easeInOut"
                                    },
                                    className: "absolute inset-0 bg-mysql-primary rounded-2xl"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 74,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                            lineNumber: 58,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                y: 20,
                                opacity: 0
                            },
                            animate: {
                                y: 0,
                                opacity: 1
                            },
                            transition: {
                                duration: 0.6,
                                delay: 0.2,
                                ease: "easeOut"
                            },
                            className: "text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold text-mysql-text mb-2",
                                    children: "MySQLAi.de"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 88,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-mysql-text-light",
                                    children: "MySQL智能分析专家"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 91,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                            lineNumber: 82,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                width: 0,
                                opacity: 0
                            },
                            animate: {
                                width: 200,
                                opacity: 1
                            },
                            transition: {
                                duration: 0.6,
                                delay: 0.4,
                                ease: "easeOut"
                            },
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-48 h-2 bg-mysql-primary-light rounded-full overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            width: '0%'
                                        },
                                        animate: {
                                            width: `${progress}%`
                                        },
                                        transition: {
                                            duration: 0.3,
                                            ease: "easeOut"
                                        },
                                        className: "h-full bg-gradient-to-r from-mysql-primary to-mysql-accent rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                        lineNumber: 104,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 103,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center mt-3 text-sm text-mysql-text-light",
                                    children: [
                                        Math.round(progress),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 111,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                y: 10,
                                opacity: 0
                            },
                            animate: {
                                y: 0,
                                opacity: 1
                            },
                            transition: {
                                duration: 0.6,
                                delay: 0.6,
                                ease: "easeOut"
                            },
                            className: "flex items-center space-x-2 text-mysql-text-light",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                    className: "w-4 h-4 animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm",
                                    children: message
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                                    lineNumber: 124,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                    lineNumber: 56,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute inset-0 overflow-hidden pointer-events-none",
                    children: [
                        ...Array(6)
                    ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                x: Math.random() * window.innerWidth,
                                y: Math.random() * window.innerHeight,
                                opacity: 0
                            },
                            animate: {
                                x: Math.random() * window.innerWidth,
                                y: Math.random() * window.innerHeight,
                                opacity: [
                                    0,
                                    0.3,
                                    0
                                ]
                            },
                            transition: {
                                duration: 4 + Math.random() * 2,
                                repeat: Infinity,
                                delay: i * 0.5,
                                ease: "easeInOut"
                            },
                            className: "absolute w-2 h-2 bg-mysql-primary rounded-full"
                        }, i, false, {
                            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                            lineNumber: 132,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
            lineNumber: 46,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
function SimpleLoader({ size = 'md', color = 'mysql-primary', className }) {
    const sizeConfig = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-8 h-8'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center justify-center', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
            animate: {
                rotate: 360
            },
            transition: {
                duration: 1,
                repeat: Infinity,
                ease: "linear"
            },
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('border-2 border-transparent rounded-full', `border-t-${color}`, sizeConfig[size])
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
            lineNumber: 180,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
        lineNumber: 179,
        columnNumber: 5
    }, this);
}
function SkeletonLoader({ lines = 3, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('space-y-3', className),
        children: [
            ...Array(lines)
        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0.3
                },
                animate: {
                    opacity: [
                        0.3,
                        0.7,
                        0.3
                    ]
                },
                transition: {
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.1,
                    ease: "easeInOut"
                },
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('h-4 bg-mysql-primary-light rounded', i === 0 && 'w-3/4', i === 1 && 'w-full', i === 2 && 'w-2/3')
            }, i, false, {
                fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                lineNumber: 203,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
        lineNumber: 201,
        columnNumber: 5
    }, this);
}
function ContentLoader({ isLoading, children, fallback, className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
            mode: "wait",
            children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: 1
                },
                exit: {
                    opacity: 0
                },
                transition: {
                    duration: 0.3
                },
                children: fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SkeletonLoader, {}, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                    lineNumber: 250,
                    columnNumber: 26
                }, this)
            }, "loading", false, {
                fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                lineNumber: 243,
                columnNumber: 11
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    duration: 0.5,
                    ease: "easeOut"
                },
                children: children
            }, "content", false, {
                fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
                lineNumber: 253,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
            lineNumber: 241,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
        lineNumber: 240,
        columnNumber: 5
    }, this);
}
function PageTransitionLoader() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            scaleX: 0
        },
        animate: {
            scaleX: 1
        },
        exit: {
            scaleX: 0
        },
        transition: {
            duration: 0.3,
            ease: "easeInOut"
        },
        className: "fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-mysql-primary to-mysql-accent z-50 origin-left"
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/PageLoader.tsx",
        lineNumber: 270,
        columnNumber: 5
    }, this);
}
}}),
"[project]/web-app/src/components/ui/StateComponents.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CardGridSkeleton": (()=>CardGridSkeleton),
    "EmptyState": (()=>EmptyState),
    "ErrorState": (()=>ErrorState),
    "ListSkeleton": (()=>ListSkeleton),
    "LoadingState": (()=>LoadingState),
    "NetworkStatus": (()=>NetworkStatus),
    "SidebarSkeleton": (()=>SidebarSkeleton),
    "StateWrapper": (()=>StateWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-ssr] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-ssr] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileX$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-x.js [app-ssr] (ecmascript) <export default as FileX>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi.js [app-ssr] (ecmascript) <export default as Wifi>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wifi-off.js [app-ssr] (ecmascript) <export default as WifiOff>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/ui/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/ui/PageLoader.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function LoadingState({ message = '正在加载...', size = 'md', variant = 'spinner', itemCount = 6, className }) {
    const sizeConfig = {
        sm: {
            spinner: 'w-4 h-4',
            text: 'text-sm',
            spacing: 'space-y-2'
        },
        md: {
            spinner: 'w-6 h-6',
            text: 'text-base',
            spacing: 'space-y-4'
        },
        lg: {
            spinner: 'w-8 h-8',
            text: 'text-lg',
            spacing: 'space-y-6'
        }
    };
    if (variant === 'skeleton') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('space-y-4', className),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                    lines: 1,
                    className: "h-8 w-48"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                    children: [
                        ...Array(itemCount)
                    ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                            lines: 1,
                            className: "h-64 rounded-lg"
                        }, i, false, {
                            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                            lineNumber: 76,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 74,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this);
    }
    if (variant === 'pulse') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center justify-center py-12', className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                animate: {
                    scale: [
                        1,
                        1.1,
                        1
                    ],
                    opacity: [
                        0.5,
                        1,
                        0.5
                    ]
                },
                transition: {
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                },
                className: "flex flex-col items-center space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-mysql-primary rounded-full flex items-center justify-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                            className: "w-8 h-8 text-white"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 91,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-mysql-text-light', sizeConfig[size].text),
                        children: message
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 94,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 86,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 85,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center justify-center py-12', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center', sizeConfig[size].spacing),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-mysql-primary animate-spin', sizeConfig[size].spinner)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 105,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-mysql-text-light', sizeConfig[size].text),
                    children: message
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 106,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 104,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
function ErrorState({ error = '加载失败', title = '出现错误', onRetry, retryLabel = '重试', variant = 'generic', className }) {
    const getIcon = ()=>{
        switch(variant){
            case 'network':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
                    className: "w-12 h-12 text-red-500"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 128,
                    columnNumber: 16
                }, this);
            case 'server':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                    className: "w-12 h-12 text-red-500"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 130,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "w-12 h-12 text-red-500"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 132,
                    columnNumber: 16
                }, this);
        }
    };
    const getTitle = ()=>{
        switch(variant){
            case 'network':
                return '网络连接失败';
            case 'server':
                return '服务器错误';
            default:
                return title;
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex flex-col items-center justify-center py-12', className),
        children: [
            getIcon(),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-semibold text-mysql-text mb-2 mt-4",
                children: getTitle()
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 155,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-mysql-text-light mb-6 text-center max-w-md",
                children: error
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            onRetry && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClick: onRetry,
                variant: "primary",
                size: "md",
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 166,
                    columnNumber: 17
                }, void 0),
                children: retryLabel
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 162,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 148,
        columnNumber: 5
    }, this);
}
function EmptyState({ title = '暂无数据', message = '当前没有可显示的内容', icon, action, className }) {
    const defaultIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileX$3e$__["FileX"], {
        className: "w-12 h-12 text-mysql-text-light"
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 185,
        columnNumber: 23
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex flex-col items-center justify-center py-12', className),
        children: [
            icon || defaultIcon,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-semibold text-mysql-text mb-2 mt-4",
                children: title
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 195,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-mysql-text-light mb-6 text-center max-w-md",
                children: message
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 198,
                columnNumber: 7
            }, this),
            action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClick: action.onClick,
                variant: "primary",
                size: "md",
                children: action.label
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 202,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 188,
        columnNumber: 5
    }, this);
}
function NetworkStatus({ isOnline = true }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            scale: 0.8
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('fixed bottom-4 right-4 flex items-center px-3 py-2 rounded-lg shadow-lg z-50', isOnline ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),
        children: [
            isOnline ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wifi$3e$__["Wifi"], {
                className: "w-4 h-4 mr-2"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 230,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wifi$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__WifiOff$3e$__["WifiOff"], {
                className: "w-4 h-4 mr-2"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 232,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-sm",
                children: isOnline ? '已连接' : '网络断开'
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 234,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 219,
        columnNumber: 5
    }, this);
}
function StateWrapper({ loading, error, isEmpty = false, children, loadingProps = {}, errorProps = {}, emptyProps = {}, className }) {
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingState, {
            ...loadingProps,
            className: className
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 267,
            columnNumber: 12
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ErrorState, {
            error: error,
            ...errorProps,
            className: className
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 271,
            columnNumber: 12
        }, this);
    }
    if (isEmpty) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(EmptyState, {
            ...emptyProps,
            className: className
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
            lineNumber: 275,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: children
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 278,
        columnNumber: 10
    }, this);
}
function SidebarSkeleton({ itemCount = 6 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-4 space-y-2",
        children: [
            ...Array(itemCount)
        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-1",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                    lines: 1,
                    className: "h-10 rounded-lg"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                    lineNumber: 287,
                    columnNumber: 11
                }, this)
            }, i, false, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 286,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 284,
        columnNumber: 5
    }, this);
}
function CardGridSkeleton({ itemCount = 6 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
        children: [
            ...Array(itemCount)
        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                        lines: 1,
                        className: "h-48 rounded-lg"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 300,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                        lines: 2
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 301,
                        columnNumber: 11
                    }, this)
                ]
            }, i, true, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 299,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 297,
        columnNumber: 5
    }, this);
}
function ListSkeleton({ itemCount = 5 }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            ...Array(itemCount)
        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                        lines: 1,
                        className: "w-12 h-12 rounded-full"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 314,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$PageLoader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                            lines: 2
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                            lineNumber: 316,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                        lineNumber: 315,
                        columnNumber: 11
                    }, this)
                ]
            }, i, true, {
                fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
                lineNumber: 313,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/StateComponents.tsx",
        lineNumber: 311,
        columnNumber: 5
    }, this);
}
}}),
"[project]/web-app/src/hooks/useSearchHistory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useSearchHistory": (()=>useSearchHistory)
});
// MySQLAi.de - 搜索历史管理Hook
// 提供本地搜索历史存储、去重、排序和清理功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
// 默认配置
const DEFAULT_OPTIONS = {
    maxItems: 50,
    storageKey: 'mysqlai_search_history',
    enableFrequencyTracking: true,
    autoCleanup: true,
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
};
function useSearchHistory(options = {}) {
    const opts = {
        ...DEFAULT_OPTIONS,
        ...options
    };
    const [historyItems, setHistoryItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // 从本地存储加载历史记录
    const loadHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
            const stored = undefined;
        } catch (error) {
            console.error('加载搜索历史失败:', error);
            setHistoryItems([]);
        }
    }, [
        opts.storageKey,
        opts.autoCleanup,
        opts.maxAge
    ]);
    // 保存历史记录到本地存储
    const saveHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((items)=>{
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }, [
        opts.storageKey
    ]);
    // 初始化加载
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadHistory();
    }, [
        loadHistory
    ]);
    // 添加搜索记录
    const addToHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((query)=>{
        if (!query || query.trim().length < 2) return;
        const trimmedQuery = query.trim();
        const now = Date.now();
        setHistoryItems((prevItems)=>{
            // 查找是否已存在
            const existingIndex = prevItems.findIndex((item)=>item.query.toLowerCase() === trimmedQuery.toLowerCase());
            let newItems;
            if (existingIndex >= 0) {
                // 更新现有项目
                newItems = [
                    ...prevItems
                ];
                newItems[existingIndex] = {
                    ...newItems[existingIndex],
                    timestamp: now,
                    count: opts.enableFrequencyTracking ? newItems[existingIndex].count + 1 : 1
                };
            } else {
                // 添加新项目
                const newItem = {
                    query: trimmedQuery,
                    timestamp: now,
                    count: 1
                };
                newItems = [
                    newItem,
                    ...prevItems
                ];
            }
            // 限制数量
            if (newItems.length > opts.maxItems) {
                newItems = newItems.slice(0, opts.maxItems);
            }
            // 按时间戳排序（最新的在前）
            newItems.sort((a, b)=>b.timestamp - a.timestamp);
            // 保存到本地存储
            saveHistory(newItems);
            return newItems;
        });
    }, [
        opts.maxItems,
        opts.enableFrequencyTracking,
        saveHistory
    ]);
    // 删除搜索记录
    const removeFromHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((query)=>{
        setHistoryItems((prevItems)=>{
            const newItems = prevItems.filter((item)=>item.query.toLowerCase() !== query.toLowerCase());
            saveHistory(newItems);
            return newItems;
        });
    }, [
        saveHistory
    ]);
    // 清空搜索历史
    const clearHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setHistoryItems([]);
        saveHistory([]);
    }, [
        saveHistory
    ]);
    // 获取搜索次数
    const getSearchCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((query)=>{
        const item = historyItems.find((item)=>item.query.toLowerCase() === query.toLowerCase());
        return item?.count || 0;
    }, [
        historyItems
    ]);
    // 导出历史记录
    const exportHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return JSON.stringify({
            version: '1.0',
            timestamp: Date.now(),
            data: historyItems
        });
    }, [
        historyItems
    ]);
    // 导入历史记录
    const importHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        try {
            const parsed = JSON.parse(data);
            if (parsed.data && Array.isArray(parsed.data)) {
                setHistoryItems(parsed.data);
                saveHistory(parsed.data);
                return true;
            }
            return false;
        } catch (error) {
            console.error('导入搜索历史失败:', error);
            return false;
        }
    }, [
        saveHistory
    ]);
    // 计算派生数据
    const history = historyItems.map((item)=>item.query);
    const recentHistory = historyItems.sort((a, b)=>b.timestamp - a.timestamp).slice(0, 10).map((item)=>item.query);
    const popularHistory = opts.enableFrequencyTracking ? historyItems.filter((item)=>item.count > 1).sort((a, b)=>b.count - a.count).slice(0, 10).map((item)=>item.query) : [];
    const totalSearches = historyItems.reduce((sum, item)=>sum + item.count, 0);
    const uniqueSearches = historyItems.length;
    return {
        // 历史记录
        history,
        recentHistory,
        popularHistory,
        // 操作方法
        addToHistory,
        removeFromHistory,
        clearHistory,
        getSearchCount,
        // 统计信息
        totalSearches,
        uniqueSearches,
        // 工具方法
        exportHistory,
        importHistory
    };
}
}}),
"[project]/web-app/src/components/knowledge/SearchHistory.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SearchHistory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - 搜索历史界面组件
// 提供搜索历史显示、管理和快速重新搜索功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/hooks/useSearchHistory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript) <export default as RotateCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
'use client';
;
;
;
;
// 历史项组件
function HistoryItem({ query, count, timestamp, onSelect, onRemove, showCount = false }) {
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const formatTimestamp = (ts)=>{
        if (!ts) return '';
        const date = new Date(ts);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return '今天';
        if (diffDays === 1) return '昨天';
        if (diffDays < 7) return `${diffDays}天前`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
        return `${Math.floor(diffDays / 30)}月前`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 group",
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>onSelect(query),
                className: "flex-1 flex items-center gap-3 text-left min-w-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                        className: "w-4 h-4 text-gray-400 flex-shrink-0"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 min-w-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "font-medium text-gray-900 truncate",
                                children: query
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 text-xs text-gray-500 mt-1",
                                children: [
                                    timestamp && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: formatTimestamp(timestamp)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 80,
                                        columnNumber: 15
                                    }, this),
                                    showCount && count && count > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "•"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                                lineNumber: 84,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: [
                                                    count,
                                                    "次搜索"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                                lineNumber: 85,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 78,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            isHovered && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: (e)=>{
                    e.stopPropagation();
                    onRemove(query);
                },
                className: "p-1 text-gray-400 hover:text-red-500 transition-colors duration-150",
                title: "删除此搜索记录",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 102,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 93,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
}
function SearchHistory({ onSearchSelect, onClose, className = '', showStats = true, maxRecentItems = 8, maxPopularItems = 5 }) {
    const { recentHistory, popularHistory, totalSearches, uniqueSearches, removeFromHistory, clearHistory, getSearchCount } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchHistory"])();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('recent');
    const [showClearConfirm, setShowClearConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // 处理搜索选择
    const handleSearchSelect = (query)=>{
        onSearchSelect(query);
        onClose?.();
    };
    // 处理删除单个记录
    const handleRemoveItem = (query)=>{
        removeFromHistory(query);
    };
    // 处理清空所有记录
    const handleClearAll = ()=>{
        if (showClearConfirm) {
            clearHistory();
            setShowClearConfirm(false);
            onClose?.();
        } else {
            setShowClearConfirm(true);
        }
    };
    // 取消清空确认
    const handleCancelClear = ()=>{
        setShowClearConfirm(false);
    };
    const hasHistory = recentHistory.length > 0 || popularHistory.length > 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white border border-gray-200 rounded-lg shadow-lg ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between p-4 border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900",
                        children: "搜索历史"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleClearAll,
                                className: `px-3 py-1 text-sm rounded-md transition-colors duration-150 ${showClearConfirm ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'text-gray-500 hover:text-red-600 hover:bg-red-50'}`,
                                children: showClearConfirm ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "确认清空?"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 177,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: handleCancelClear,
                                            className: "text-gray-500 hover:text-gray-700",
                                            children: "取消"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 178,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 176,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                            className: "w-4 h-4"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 188,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "清空"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 187,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this),
                            onClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "p-1 text-gray-400 hover:text-gray-600 transition-colors duration-150",
                                title: "关闭搜索历史",
                                "aria-label": "关闭搜索历史",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "w-5 h-5"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 195,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 162,
                columnNumber: 7
            }, this),
            showStats && hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-4 py-3 bg-gray-50 border-b border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4 text-sm text-gray-600",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 213,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: [
                                        "总搜索: ",
                                        totalSearches,
                                        "次"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 212,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 217,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: [
                                        "不同查询: ",
                                        uniqueSearches,
                                        "个"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 218,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 216,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 211,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this),
            hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setActiveTab('recent'),
                        className: `flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${activeTab === 'recent' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 237,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "最近搜索"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 238,
                                    columnNumber: 15
                                }, this),
                                recentHistory.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full",
                                    children: recentHistory.length
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 240,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 236,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setActiveTab('popular'),
                        className: `flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${activeTab === 'popular' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 256,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "热门搜索"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 257,
                                    columnNumber: 15
                                }, this),
                                popularHistory.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full",
                                    children: popularHistory.length
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 259,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 255,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4",
                children: !hasHistory ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center py-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                            className: "w-12 h-12 text-gray-300 mx-auto mb-3"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 272,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 text-sm",
                            children: "暂无搜索历史"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 273,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-400 text-xs mt-1",
                            children: "开始搜索后，历史记录会显示在这里"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 274,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 271,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-1",
                    children: [
                        activeTab === 'recent' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                recentHistory.slice(0, maxRecentItems).map((query, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(HistoryItem, {
                                        query: query,
                                        count: getSearchCount(query),
                                        onSelect: handleSearchSelect,
                                        onRemove: handleRemoveItem,
                                        showCount: true
                                    }, `recent-${query}-${index}`, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 281,
                                        columnNumber: 19
                                    }, this)),
                                recentHistory.length > maxRecentItems && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-500",
                                        children: [
                                            "还有 ",
                                            recentHistory.length - maxRecentItems,
                                            " 条记录..."
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 292,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 291,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true),
                        activeTab === 'popular' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: popularHistory.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center py-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "w-8 h-8 text-gray-300 mx-auto mb-2"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 304,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-500 text-sm",
                                        children: "暂无热门搜索"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 305,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-400 text-xs mt-1",
                                        children: "多次搜索相同内容后会显示在这里"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 306,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 303,
                                columnNumber: 19
                            }, this) : popularHistory.slice(0, maxPopularItems).map((query, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(HistoryItem, {
                                    query: query,
                                    count: getSearchCount(query),
                                    onSelect: handleSearchSelect,
                                    onRemove: handleRemoveItem,
                                    showCount: true
                                }, `popular-${query}-${index}`, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 310,
                                    columnNumber: 21
                                }, this))
                        }, void 0, false)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 277,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 269,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
}
}}),
"[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>KnowledgeSidebarWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - KnowledgeSidebarWrapper客户端组件包装器
// 用于解决hydration mismatch问题，确保KnowledgeSidebar只在客户端渲染
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$contexts$2f$NavigationContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/contexts/NavigationContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/knowledge.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useKnowledgeData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/hooks/useKnowledgeData.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$search$2d$history$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/api/search-history.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$StateComponents$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/ui/StateComponents.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchHistory$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/SearchHistory.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
// 直接实现KnowledgeSidebar，包含完整的交互功能
function KnowledgeSidebar() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const navigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$contexts$2f$NavigationContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNavigation"])();
    // 状态管理
    const [expandedCategories, setExpandedCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [isLoaded, setIsLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showSearchHistory, setShowSearchHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchFocused, setSearchFocused] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [categoryArticles, setCategoryArticles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // 使用 useKnowledgeData Hook 获取真实分类数据
    const { data: dbCategories, loading, error, retry } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useKnowledgeData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useKnowledgeData"])('categories', {
        includeStats: true
    }, {
        autoFetch: true,
        cacheTime: 5 * 60 * 1000,
        debug: ("TURBOPACK compile-time value", "development") === 'development'
    });
    // 转换数据格式并排序 - 确保类型正确
    const categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!dbCategories || dbCategories.length === 0) return [];
        return dbCategories.sort((a, b)=>a.order_index - b.order_index);
    }, [
        dbCategories
    ]);
    // 处理分类点击
    const handleCategoryClick = async (categoryId)=>{
        // 切换展开状态
        const newExpanded = new Set(expandedCategories);
        const wasExpanded = newExpanded.has(categoryId);
        if (wasExpanded) {
            newExpanded.delete(categoryId);
        } else {
            newExpanded.add(categoryId);
            // 如果是展开操作且还没有加载过该分类的文章，则获取文章数据
            if (!categoryArticles[categoryId]) {
                try {
                    const articles = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$knowledge$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getKnowledgeItemsByCategory"])(categoryId);
                    setCategoryArticles((prev)=>({
                            ...prev,
                            [categoryId]: articles.map((article)=>({
                                    id: article.id,
                                    title: article.title
                                }))
                        }));
                } catch (error) {
                    console.error(`获取分类 ${categoryId} 的文章失败:`, error);
                    // 即使获取文章失败，也要设置一个空数组，避免重复请求
                    setCategoryArticles((prev)=>({
                            ...prev,
                            [categoryId]: []
                        }));
                }
            }
        }
        setExpandedCategories(newExpanded);
        // 更新导航状态
        navigation.setCurrentCategory(categoryId);
        // 跳转到分类页面
        router.push(`/knowledge/${categoryId}`);
    };
    // 处理知识点点击
    const handleItemClick = (categoryId, itemId)=>{
        navigation.setCurrentItem(itemId);
        router.push(`/knowledge/${categoryId}/${itemId}`);
    };
    // 处理搜索
    const handleSearch = (query)=>{
        navigation.setSearchQuery(query);
        // 如果查询不为空，保存到搜索历史
        if (query.trim()) {
            // 这里可以添加结果计数，暂时设为0
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$search$2d$history$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hybridSearchHistory"].addRecord(query.trim(), 0);
        }
    };
    // 处理搜索历史选择
    const handleSearchHistorySelect = (query)=>{
        navigation.setSearchQuery(query);
        setShowSearchHistory(false);
    };
    // 处理搜索框焦点
    const handleSearchFocus = ()=>{
        setSearchFocused(true);
        setShowSearchHistory(true);
    };
    // 处理搜索框失焦
    const handleSearchBlur = ()=>{
        setSearchFocused(false);
        // 延迟隐藏搜索历史，允许点击历史项
        setTimeout(()=>setShowSearchHistory(false), 200);
    };
    // 获取分类下的知识点
    const getCategoryItems = (categoryId)=>{
        return categoryArticles[categoryId] || [];
    };
    // 获取分类的文章数量（从API数据中获取）
    const getCategoryArticleCount = (category)=>{
        // 优先使用API返回的article_count
        if (typeof category.article_count === 'number') {
            return category.article_count;
        }
        // 如果没有，使用本地缓存的文章数据
        return categoryArticles[category.id]?.length || 0;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col h-full bg-white",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-shrink-0 p-4 border-b border-mysql-border",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-semibold text-mysql-text",
                            children: "知识库导航"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                            className: "h-4 w-4 text-mysql-text-light"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                            lineNumber: 157,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                        lineNumber: 156,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "text",
                                        placeholder: "搜索知识点...",
                                        value: navigation.searchQuery,
                                        onChange: (e)=>handleSearch(e.target.value),
                                        onFocus: handleSearchFocus,
                                        onBlur: handleSearchBlur,
                                        className: "w-full pl-10 pr-10 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary/30",
                                        "data-testid": "search-input"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                        lineNumber: 159,
                                        columnNumber: 13
                                    }, this),
                                    navigation.searchQuery && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>handleSearch(''),
                                        className: "absolute inset-y-0 right-0 pr-3 flex items-center text-mysql-text-light hover:text-mysql-text",
                                        children: "×"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                        lineNumber: 170,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                lineNumber: 155,
                                columnNumber: 11
                            }, this),
                            showSearchHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute top-full left-0 right-0 mt-1 z-50",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchHistory$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    onSelectQuery: handleSearchHistorySelect,
                                    onClose: ()=>setShowSearchHistory(false),
                                    maxItems: 5,
                                    className: "w-full"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                    lineNumber: 183,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                lineNumber: 182,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 overflow-y-auto",
                children: [
                    loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$StateComponents$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SidebarSkeleton"], {
                        itemCount: 6
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 197,
                        columnNumber: 21
                    }, this),
                    error && !loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$StateComponents$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorState"], {
                            error: "分类数据加载失败",
                            title: "加载失败",
                            onRetry: retry,
                            retryLabel: "重试",
                            variant: "network",
                            className: "py-8"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                            lineNumber: 202,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 201,
                        columnNumber: 11
                    }, this),
                    !loading && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 space-y-2",
                        "data-testid": "knowledge-categories",
                        children: categories.map((category)=>{
                            const isExpanded = expandedCategories.has(category.id);
                            const isActive = navigation.currentCategory === category.id;
                            const categoryItems = getCategoryItems(category.id);
                            const articleCount = getCategoryArticleCount(category);
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-1",
                                "data-testid": "category-item",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>handleCategoryClick(category.id),
                                        className: `w-full flex items-center px-3 py-2 text-left rounded-lg transition-all duration-200 hover:bg-mysql-primary-light hover:text-mysql-primary ${isActive ? 'bg-mysql-primary text-white' : 'text-mysql-text'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "w-4 h-4 mr-2 flex-shrink-0",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getIconEmoji"])(category.icon || 'FolderOpen')
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                lineNumber: 232,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "flex-1 font-medium",
                                                children: category.name
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                lineNumber: 233,
                                                columnNumber: 21
                                            }, this),
                                            articleCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs bg-mysql-primary-light text-mysql-primary px-2 py-1 rounded-full mr-2",
                                                children: articleCount
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                lineNumber: 235,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "w-4 h-4 flex-shrink-0",
                                                children: isExpanded ? '▼' : '▶'
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                lineNumber: 239,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                        lineNumber: 225,
                                        columnNumber: 19
                                    }, this),
                                    isExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "ml-6 space-y-1",
                                        children: categoryItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>handleItemClick(category.id, item.id),
                                                className: `w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors hover:bg-mysql-primary-light hover:text-mysql-primary ${navigation.currentItem === item.id ? 'bg-mysql-primary-light text-mysql-primary' : 'text-mysql-text-light'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "w-3 h-3 mr-2 flex-shrink-0",
                                                        children: "📄"
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                        lineNumber: 258,
                                                        columnNumber: 27
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm",
                                                        children: item.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                        lineNumber: 259,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, item.id, true, {
                                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                                lineNumber: 248,
                                                columnNumber: 25
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                        lineNumber: 246,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, category.id, true, {
                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                lineNumber: 223,
                                columnNumber: 17
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 215,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                lineNumber: 195,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-shrink-0 p-4 border-t border-mysql-border bg-mysql-bg-light",
                children: [
                    !loading && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-mysql-text-light text-center",
                                children: [
                                    "共 ",
                                    categories.length,
                                    " 个分类，",
                                    categories.reduce((total, cat)=>total + getCategoryArticleCount(cat), 0),
                                    " 个知识点"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                lineNumber: 275,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-mysql-text-light text-center mt-1",
                                children: "MySQL知识库 v1.0"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                                lineNumber: 278,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true),
                    loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$StateComponents$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LoadingState"], {
                        message: "加载中...",
                        size: "sm",
                        variant: "spinner",
                        className: "py-2"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                        lineNumber: 284,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
                lineNumber: 272,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
}
function KnowledgeSidebarWrapper() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(KnowledgeSidebar, {}, void 0, false, {
        fileName: "[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx",
        lineNumber: 297,
        columnNumber: 10
    }, this);
}
}}),
"[project]/web-app/src/app/knowledge/layout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>KnowledgeLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$contexts$2f$NavigationContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/contexts/NavigationContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$KnowledgeSidebarWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/KnowledgeSidebarWrapper.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
function KnowledgeLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$contexts$2f$NavigationContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NavigationProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex h-screen bg-white",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                    className: "hidden lg:flex lg:flex-shrink-0",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col w-80 border-r border-mysql-border bg-white h-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$KnowledgeSidebarWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                            lineNumber: 21,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                        lineNumber: 20,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                    lineNumber: 19,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                    className: "flex-1 flex flex-col min-w-0 h-full",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 relative focus:outline-none overflow-y-auto",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                        lineNumber: 27,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "lg:hidden"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/app/knowledge/layout.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=web-app_src_efd693d1._.js.map