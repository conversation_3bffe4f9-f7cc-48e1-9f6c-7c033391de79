{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4deb387e._.js", "server/edge/chunks/[root-of-the-server]__46d1e4a8._.js", "server/edge/chunks/web-app_edge-wrapper_961a8f5c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hUUNw3G6QWUY1VSlhes6yhf/o0u8ge1SZk6j+wwSKr8=", "__NEXT_PREVIEW_MODE_ID": "d6ebb472a1afb5435d70b264ff3bab43", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6fe7d5ab3fcd22f6e11783882cc31284ef6dccba37e2ad702897dbfbdfdbe886", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e502fb1a62f9e3acdcc5895de7325e0010ed39d082977d48700359bf9fe59b07"}}}, "instrumentation": null, "functions": {}}