{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4deb387e._.js", "server/edge/chunks/[root-of-the-server]__46d1e4a8._.js", "server/edge/chunks/web-app_edge-wrapper_961a8f5c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hUUNw3G6QWUY1VSlhes6yhf/o0u8ge1SZk6j+wwSKr8=", "__NEXT_PREVIEW_MODE_ID": "257b8f4ff1410e6bb2028454f70a9fc9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7f96d9724f5e2039ad2df77cf9da76447681f24442077e8e74ebb5033c594f00", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a8c9b4898ad78a3d0aedfc573adf639fda9b40edfefd9ca1a9c6697c70706665"}}}, "instrumentation": null, "functions": {}}