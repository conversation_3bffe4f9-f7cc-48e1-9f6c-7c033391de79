module.exports = {

"[project]/web-app/.next-internal/server/app/api/knowledge/search/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/web-app/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - Supabase 客户端配置
// 提供类型安全的 Supabase 客户端实例
__turbopack_context__.s({
    "auth": (()=>auth),
    "db": (()=>db),
    "default": (()=>__TURBOPACK__default__export__),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
// 环境变量验证 - 构建时使用占位符
const supabaseUrl = ("TURBOPACK compile-time value", "https://rlwppfewjevxzhqeupdq.supabase.co") || 'https://example.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJsd3BwZmV3amV2eHpocWV1cGRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMDY1NjksImV4cCI6MjA2Njc4MjU2OX0.gDrcpdWqA8gdUIBw-5OLN2iBSkeHIDvmNdLwV67NJvc") || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4YW1wbGUiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MjQ0NjQwMCwiZXhwIjoxOTU4MDIyNDAwfQ.example';
// 仅在客户端检查环境变量
if ("undefined" !== 'undefined' && (supabaseUrl.includes('example') || supabaseAnonKey.includes('example'))) {
    "TURBOPACK unreachable";
}
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
    },
    realtime: {
        params: {
            eventsPerSecond: 10
        }
    }
});
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY || supabaseAnonKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
const auth = {
    // 获取当前用户
    getCurrentUser: async ()=>{
        const { data: { user }, error } = await supabase.auth.getUser();
        return {
            user,
            error
        };
    },
    // 登录
    signIn: async (email, password)=>{
        return await supabase.auth.signInWithPassword({
            email,
            password
        });
    },
    // 注册
    signUp: async (email, password, metadata)=>{
        return await supabase.auth.signUp({
            email,
            password,
            options: {
                data: metadata
            }
        });
    },
    // 登出
    signOut: async ()=>{
        return await supabase.auth.signOut();
    },
    // 重置密码
    resetPassword: async (email)=>{
        return await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`
        });
    }
};
const db = {
    // 知识库文章
    articles: {
        getAll: ()=>supabase.from('knowledge_articles').select('*'),
        getById: (id)=>supabase.from('knowledge_articles').select('*').eq('id', id).single(),
        getByCategory: (categoryId)=>supabase.from('knowledge_articles').select('*').eq('category_id', categoryId),
        search: (query)=>supabase.from('knowledge_articles').select('*').textSearch('title,content', query)
    },
    // ER图项目
    erProjects: {
        getAll: ()=>supabase.from('er_projects').select('*'),
        getById: (id)=>supabase.from('er_projects').select('*').eq('id', id).single(),
        getByUser: (userId)=>supabase.from('er_projects').select('*').eq('user_id', userId),
        create: (project)=>supabase.from('er_projects').insert(project),
        update: (id, updates)=>supabase.from('er_projects').update(updates).eq('id', id),
        delete: (id)=>supabase.from('er_projects').delete().eq('id', id)
    },
    // 用户收藏
    favorites: {
        getByUser: (userId)=>supabase.from('user_favorites').select('*, knowledge_articles(*)').eq('user_id', userId),
        add: (userId, articleId)=>supabase.from('user_favorites').insert({
                user_id: userId,
                article_id: articleId
            }),
        remove: (userId, articleId)=>supabase.from('user_favorites').delete().eq('user_id', userId).eq('article_id', articleId)
    }
};
const __TURBOPACK__default__export__ = supabase;
}}),
"[project]/web-app/src/app/api/knowledge/search/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 知识库搜索 API
// 提供高级搜索功能和搜索统计
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/supabase.ts [app-route] (ecmascript)");
;
;
// 高亮文本函数
function highlightText(text, query) {
    if (!query || !text) return text;
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}
// 计算文本相似度（简化版TF-IDF）
function calculateRelevanceScore(article, query) {
    if (!query || !article) return 0;
    const queryTerms = query.toLowerCase().split(/\s+/).filter((term)=>term.length > 1);
    if (queryTerms.length === 0) return 0;
    let score = 0;
    const title = (article.title || '').toLowerCase();
    const content = (article.content || '').toLowerCase();
    const description = (article.description || '').toLowerCase();
    queryTerms.forEach((term)=>{
        // 标题匹配权重最高
        const titleMatches = (title.match(new RegExp(term, 'g')) || []).length;
        score += titleMatches * 10;
        // 描述匹配权重中等
        const descMatches = (description.match(new RegExp(term, 'g')) || []).length;
        score += descMatches * 5;
        // 内容匹配权重较低
        const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
        score += contentMatches * 2;
        // 完全匹配加分
        if (title.includes(term)) score += 20;
        if (description.includes(term)) score += 10;
    });
    // 标签匹配加分
    if (article.tags && Array.isArray(article.tags)) {
        queryTerms.forEach((term)=>{
            const tagMatches = article.tags.filter((tag)=>tag.toLowerCase().includes(term)).length;
            score += tagMatches * 15;
        });
    }
    return score;
}
// 智能排序函数
function intelligentSort(articles, query, sortBy, sortOrder) {
    if (!articles || articles.length === 0) return articles;
    // 为每篇文章计算相关性分数
    const articlesWithScore = articles.map((article)=>({
            ...article,
            relevanceScore: calculateRelevanceScore(article, query)
        }));
    // 根据排序方式进行排序
    return articlesWithScore.sort((a, b)=>{
        switch(sortBy){
            case 'relevance':
                // 相关性排序：分数高的在前，分数相同时按更新时间排序
                if (a.relevanceScore !== b.relevanceScore) {
                    return sortOrder === 'asc' ? a.relevanceScore - b.relevanceScore : b.relevanceScore - a.relevanceScore;
                }
                return new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime();
            case 'date':
                const dateA = new Date(a.last_updated).getTime();
                const dateB = new Date(b.last_updated).getTime();
                return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
            case 'title':
                const titleA = (a.title || '').toLowerCase();
                const titleB = (b.title || '').toLowerCase();
                return sortOrder === 'asc' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);
            default:
                // 默认按相关性排序
                return b.relevanceScore - a.relevanceScore;
        }
    });
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const query = searchParams.get('q') || searchParams.get('query');
        const category = searchParams.get('category');
        const tags = searchParams.get('tags');
        const difficulty = searchParams.get('difficulty');
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const sortBy = searchParams.get('sortBy') || 'relevance'; // relevance, date, title
        const sortOrder = searchParams.get('sortOrder') || 'desc';
        if (!query) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请提供搜索关键词'
            }, {
                status: 400
            });
        }
        // 记录搜索历史
        const userAgent = request.headers.get('user-agent');
        const forwarded = request.headers.get('x-forwarded-for');
        const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip');
        // 构建搜索查询
        let searchQuery = __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select(`
        *,
        knowledge_categories(id, name, icon, color)
      `);
        // 尝试全文搜索，如果失败则使用ilike搜索
        console.log('执行textSearch查询:', 'title,content', query);
        // 先尝试textSearch
        const textSearchQuery = __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select(`
        *,
        knowledge_categories(id, name, icon, color)
      `).textSearch('title,content', query);
        // 如果有其他筛选条件，也应用到textSearch
        if (category) {
            textSearchQuery.eq('category_id', category);
        }
        if (difficulty) {
            textSearchQuery.eq('difficulty', difficulty);
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag)=>tag.trim());
            textSearchQuery.overlaps('tags', tagArray);
        }
        const { data: textSearchResults, error: textSearchError } = await textSearchQuery;
        console.log('textSearch结果数量:', textSearchResults?.length || 0);
        console.log('textSearch错误:', textSearchError);
        // 如果textSearch有结果，使用它；否则使用ilike搜索
        let finalResults = textSearchResults;
        let finalError = textSearchError;
        if (!textSearchResults || textSearchResults.length === 0) {
            console.log('textSearch无结果，尝试ilike搜索...');
            // 使用ilike搜索作为备选方案
            let iLikeQuery = __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select(`
          *,
          knowledge_categories(id, name, icon, color)
        `).or(`title.ilike.%${query}%,content.ilike.%${query}%`);
            // 应用筛选条件
            if (category) {
                iLikeQuery = iLikeQuery.eq('category_id', category);
            }
            if (difficulty) {
                iLikeQuery = iLikeQuery.eq('difficulty', difficulty);
            }
            if (tags) {
                const tagArray = tags.split(',').map((tag)=>tag.trim());
                iLikeQuery = iLikeQuery.overlaps('tags', tagArray);
            }
            const { data: iLikeResults, error: iLikeError } = await iLikeQuery;
            console.log('ilike搜索结果数量:', iLikeResults?.length || 0);
            console.log('ilike搜索错误:', iLikeError);
            finalResults = iLikeResults;
            finalError = iLikeError;
        }
        // 更新原来的变量
        let allArticles = finalResults;
        let error = finalError;
        let count = finalResults?.length || 0;
        // 查询已在上面执行完成，筛选条件也已应用
        console.log('=== 搜索API调试信息 ===');
        console.log('查询关键词:', query);
        console.log('数据库查询结果数量:', allArticles?.length || 0);
        console.log('数据库错误:', error);
        console.log('第一条数据示例:', allArticles?.[0]);
        // 测试数据库连接 - 查询所有文章
        const { data: allData, error: allError } = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select('*');
        console.log('数据库连接测试 - 所有文章数量:', allData?.length || 0);
        console.log('数据库连接测试 - 错误:', allError);
        console.log('数据库中的文章标题:', allData?.map((article)=>article.title) || []);
        console.log('第一篇文章完整数据:', allData?.[0]);
        if (error) {
            console.error('搜索失败:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '搜索失败',
                details: error.message
            }, {
                status: 500
            });
        }
        // 使用智能排序算法对结果进行排序
        const sortedArticles = intelligentSort(allArticles || [], query, sortBy, sortOrder);
        console.log('排序后结果数量:', sortedArticles.length);
        console.log('排序后第一条数据:', sortedArticles[0]);
        // 手动分页
        const offset = (page - 1) * limit;
        const paginatedArticles = sortedArticles.slice(offset, offset + limit);
        console.log('分页后结果数量:', paginatedArticles.length);
        console.log('分页参数 - page:', page, 'limit:', limit, 'offset:', offset);
        // 异步记录搜索历史
        __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('search_history').insert({
            query,
            results_count: count || 0,
            ip_address: ip,
            user_agent: userAgent
        }).then(({ error })=>{
            if (error) console.error('记录搜索历史失败:', error);
        });
        // 为每个结果添加搜索高亮信息和相关性分数
        const resultsWithHighlight = paginatedArticles.map((article)=>({
                ...article,
                highlight: {
                    title: highlightText(article.title, query),
                    description: highlightText(article.description || '', query)
                },
                // 添加相关性分数用于调试和展示
                relevanceScore: article.relevanceScore || 0
            }));
        console.log('最终返回数据数量:', resultsWithHighlight.length);
        console.log('最终返回数据示例:', resultsWithHighlight[0]);
        console.log('=== 搜索API调试信息结束 ===');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: resultsWithHighlight,
            query: {
                text: query,
                category,
                tags,
                difficulty,
                sortBy,
                sortOrder
            },
            pagination: {
                page,
                limit,
                total: count || 0,
                totalPages: Math.ceil((count || 0) / limit)
            }
        });
    } catch (error) {
        console.error('API错误:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '服务器内部错误'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { query, limit = 5 } = body;
        if (!query || query.length < 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: []
            });
        }
        // 并行搜索多种建议类型
        const [titleMatches, tagMatches, popularSearches] = await Promise.all([
            // 1. 搜索文章标题匹配
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select('id, title, category_id, knowledge_categories(name)').ilike('title', `%${query}%`).limit(Math.ceil(limit * 0.6)),
            // 2. 搜索标签匹配
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('knowledge_articles').select('id, title, tags, category_id, knowledge_categories(name)').contains('tags', [
                query
            ]).limit(Math.ceil(limit * 0.3)),
            // 3. 搜索热门搜索词
            __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('search_history').select('query').ilike('query', `%${query}%`).gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // 最近30天
            .limit(Math.ceil(limit * 0.2)) // 20%的建议来自热门搜索
        ]);
        // 处理错误
        if (titleMatches.error) {
            console.error('搜索标题建议失败:', titleMatches.error);
        }
        if (tagMatches.error) {
            console.error('搜索标签建议失败:', tagMatches.error);
        }
        if (popularSearches.error) {
            console.error('获取热门搜索失败:', popularSearches.error);
        }
        // 构建建议列表
        const suggestions = [];
        // 添加标题匹配建议（优先级最高）
        if (titleMatches.data) {
            suggestions.push(...titleMatches.data.map((article)=>({
                    type: 'article',
                    text: article.title,
                    id: article.id,
                    category: article.knowledge_categories?.name || '未分类',
                    priority: 3 // 最高优先级
                })));
        }
        // 添加标签匹配建议
        if (tagMatches.data) {
            suggestions.push(...tagMatches.data.filter((article)=>!suggestions.some((s)=>s.id === article.id)) // 去重
            .map((article)=>({
                    type: 'article',
                    text: article.title,
                    id: article.id,
                    category: article.knowledge_categories?.name || '未分类',
                    priority: 2
                })));
        }
        // 添加热门搜索建议
        if (popularSearches.data) {
            suggestions.push(...popularSearches.data.map((search)=>({
                    type: 'query',
                    text: search.query,
                    priority: 1
                })));
        }
        // 按优先级和相关性排序
        const sortedSuggestions = suggestions.sort((a, b)=>{
            // 首先按优先级排序
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            // 然后按文本相似度排序（简单的字符串匹配）
            const aMatch = a.text.toLowerCase().indexOf(query.toLowerCase());
            const bMatch = b.text.toLowerCase().indexOf(query.toLowerCase());
            if (aMatch !== bMatch) {
                return aMatch - bMatch; // 匹配位置越靠前越好
            }
            return a.text.length - b.text.length; // 长度越短越好
        }).slice(0, limit).map(({ priority, ...suggestion })=>suggestion); // 移除priority字段
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: sortedSuggestions,
            meta: {
                query,
                total: suggestions.length,
                limit
            }
        });
    } catch (error) {
        console.error('API错误:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '服务器内部错误'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__70aa42c3._.js.map