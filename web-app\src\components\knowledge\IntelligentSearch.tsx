'use client';

// MySQLAi.de - 智能搜索主组件
// 整合搜索输入、建议、筛选、历史、结果等所有搜索功能

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Search, X } from 'lucide-react';
import SearchSuggestions from './SearchSuggestions';
import AdvancedSearchFilters from './AdvancedSearchFilters';
import SearchHistory from './SearchHistory';
import SearchResultItem from './SearchResultItem';
import { useSearchHistory } from '@/hooks/useSearchHistory';
import { searchApi } from '@/lib/api/knowledge';

// 搜索状态类型
interface SearchState {
  query: string;
  isSearching: boolean;
  results: any[];
  suggestions: any[];
  showSuggestions: boolean;
  showHistory: boolean;
  showFilters: boolean;
  filters: {
    category?: string;
    difficulty?: string;
    tags: string[];
    sortBy: 'relevance' | 'date' | 'title';
    sortOrder: 'asc' | 'desc';
  };
  error?: string;
  totalResults: number;
}

// 组件属性
interface IntelligentSearchProps {
  placeholder?: string;
  autoFocus?: boolean;
  showAdvancedFilters?: boolean;
  showSearchHistory?: boolean;
  maxResults?: number;
  className?: string;
  onResultSelect?: (result: any) => void;
  categories?: Array<{ value: string; label: string; count?: number }>;
  availableTags?: Array<{ value: string; label: string; count?: number }>;
}

export default function IntelligentSearch({
  placeholder = '搜索知识库...',
  autoFocus = false,
  showAdvancedFilters = true,
  showSearchHistory = true,
  maxResults = 20,
  className = '',
  onResultSelect,
  categories = [],
  availableTags = []
}: IntelligentSearchProps) {
  // 状态管理
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    isSearching: false,
    results: [],
    suggestions: [],
    showSuggestions: false,
    showHistory: false,
    showFilters: false,
    filters: {
      tags: [],
      sortBy: 'relevance',
      sortOrder: 'desc'
    },
    totalResults: 0
  });

  // 搜索历史Hook
  const {
    addToHistory,
    recentHistory,
    popularHistory,
    clearHistory,
    removeFromHistory,
    getSearchCount,
    totalSearches,
    uniqueSearches
  } = useSearchHistory();

  // 引用
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 执行搜索
  const performSearch = useCallback(async (query: string, filters?: any) => {
    if (!query.trim()) {
      setSearchState(prev => ({
        ...prev,
        results: [],
        totalResults: 0,
        showSuggestions: false
      }));
      return;
    }

    setSearchState(prev => ({
      ...prev,
      isSearching: true,
      error: undefined
    }));

    try {
      // 使用传入的filters或当前状态的filters
      const currentFilters = filters || searchState.filters;

      // 调用搜索API
      const response = await searchApi.search({
        query,
        category: currentFilters.category,
        difficulty: currentFilters.difficulty,
        tags: currentFilters.tags,
        sortBy: currentFilters.sortBy,
        sortOrder: currentFilters.sortOrder,
        limit: maxResults
      });

      console.log('=== 前端搜索响应调试 ===');
      console.log('API响应成功:', response.success);
      console.log('API返回数据:', response.data);
      console.log('数据数量:', response.data?.length || 0);
      console.log('第一条数据:', response.data?.[0]);

      if (response.success) {
        // 添加到搜索历史
        addToHistory(query);

        setSearchState(prev => ({
          ...prev,
          results: response.data || [],
          totalResults: response.data?.length || 0,
          isSearching: false,
          showSuggestions: false,
          showHistory: false
        }));

        console.log('搜索状态已更新，结果数量:', response.data?.length || 0);
      } else {
        console.error('搜索失败:', response.error);
        throw new Error(response.error || '搜索失败');
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '搜索出错，请稍后重试',
        isSearching: false,
        results: [],
        totalResults: 0
      }));
    }
  }, [maxResults, addToHistory]); // 移除searchState.filters依赖

  // 处理搜索输入变化
  const handleSearchChange = useCallback((value: string) => {
    setSearchState(prev => ({
      ...prev,
      query: value,
      showSuggestions: value.length >= 2,
      showHistory: value.length === 0
    }));

    // 清除之前的搜索定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果查询为空，清空结果
    if (!value.trim()) {
      setSearchState(prev => ({
        ...prev,
        results: [],
        totalResults: 0,
        showSuggestions: false
      }));
      return;
    }

    // 延迟执行搜索 - 只有当输入长度>=2时才搜索
    if (value.trim().length >= 2) {
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(value);
      }, 800); // 增加延迟到800ms，减少频繁搜索
    }
  }, [performSearch]);

  // 处理搜索提交
  const handleSearchSubmit = useCallback((query: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    performSearch(query);
  }, [performSearch]);

  // 处理建议选择
  const handleSuggestionSelect = useCallback((suggestion: any) => {
    const query = suggestion.text || suggestion.query;
    setSearchState(prev => ({ ...prev, query }));
    handleSearchSubmit(query);
  }, [handleSearchSubmit]);

  // 关闭建议
  const handleCloseSuggestions = useCallback(() => {
    setSearchState(prev => ({ ...prev, showSuggestions: false }));
  }, []);

  // 关闭历史
  const handleCloseHistory = useCallback(() => {
    setSearchState(prev => ({ ...prev, showHistory: false }));
  }, []);

  // 处理历史选择
  const handleHistorySelect = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }));
    handleSearchSubmit(query);
  }, [handleSearchSubmit]);

  // 处理筛选器变化
  const handleFiltersChange = useCallback((filters: any) => {
    setSearchState(prev => {
      const newState = { ...prev, filters };

      // 如果有查询，重新搜索
      if (prev.query.trim()) {
        // 使用setTimeout避免在setState中直接调用performSearch
        setTimeout(() => {
          performSearch(prev.query, filters);
        }, 0);
      }

      return newState;
    });
  }, [performSearch]);

  // 清空搜索
  const handleClearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      results: [],
      totalResults: 0,
      showSuggestions: false,
      showHistory: true,
      error: undefined
    }));
    searchInputRef.current?.focus();
  }, []);

  // 切换筛选器显示
  const toggleFilters = useCallback(() => {
    setSearchState(prev => ({ ...prev, showFilters: !prev.showFilters }));
  }, []);

  // 组件挂载时自动聚焦
  useEffect(() => {
    if (autoFocus && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [autoFocus]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* 搜索输入区域 */}
      <div className="relative">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          
          <input
            ref={searchInputRef}
            type="text"
            value={searchState.query}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearchSubmit(searchState.query);
              }
            }}
            placeholder={placeholder}
            className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />

          {searchState.query && (
            <button
              type="button"
              onClick={handleClearSearch}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* 搜索建议 */}
        {searchState.showSuggestions && (
          <div className="absolute top-full left-0 right-0 z-20 mt-1">
            <SearchSuggestions
              query={searchState.query}
              onSelect={handleSuggestionSelect}
              onClose={handleCloseSuggestions}
            />
          </div>
        )}

        {/* 搜索历史 */}
        {searchState.showHistory && showSearchHistory && (
          <div className="absolute top-full left-0 right-0 z-20 mt-1">
            <SearchHistory
              onSelect={handleHistorySelect}
              onClose={handleCloseHistory}
              maxItems={8}
              showStats={true}
            />
          </div>
        )}
      </div>

      {/* 高级筛选器 */}
      {showAdvancedFilters && (
        <div className="mt-4">
          <AdvancedSearchFilters
            filters={searchState.filters}
            onFiltersChange={handleFiltersChange}
            categories={categories}
            availableTags={availableTags}
            isCollapsed={!searchState.showFilters}
            onToggleCollapse={toggleFilters}
            showResultCount={searchState.results.length > 0}
            resultCount={searchState.totalResults}
          />
        </div>
      )}

      {/* 搜索状态和结果 */}
      <div className="mt-6">
        {/* 加载状态 */}
        {searchState.isSearching && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">搜索中...</p>
          </div>
        )}

        {/* 错误状态 */}
        {searchState.error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <p className="text-red-600">{searchState.error}</p>
          </div>
        )}

        {/* 搜索结果 */}
        {!searchState.isSearching && !searchState.error && searchState.results.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                搜索结果 ({searchState.totalResults})
              </h2>
            </div>
            
            <div className="space-y-4">
              {searchState.results.map((result, index) => (
                <SearchResultItem
                  key={result.id || index}
                  item={result}
                  query={searchState.query}
                  showRelevanceScore={true}
                  onBookmark={(id, bookmarked) => {
                    // TODO: 实现收藏功能
                    console.log('Bookmark:', id, bookmarked);
                  }}
                  onShare={(item) => {
                    // TODO: 实现分享功能
                    console.log('Share:', item);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* 无结果状态 */}
        {!searchState.isSearching && !searchState.error && searchState.query && searchState.results.length === 0 && (
          <div className="text-center py-8">
            <Search className="mx-auto h-12 w-12 text-gray-300" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">未找到相关结果</h3>
            <p className="mt-1 text-sm text-gray-500">
              尝试使用不同的关键词或调整筛选条件
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
