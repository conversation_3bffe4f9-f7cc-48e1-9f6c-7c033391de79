(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/web-app/src/lib/api/knowledge.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// MySQLAi.de - 知识库 API 客户端
// 提供类型安全的 API 调用方法
__turbopack_context__.s({
    "articlesApi": (()=>articlesApi),
    "categoriesApi": (()=>categoriesApi),
    "codeExamplesApi": (()=>codeExamplesApi),
    "searchApi": (()=>searchApi),
    "statsApi": (()=>statsApi)
});
class APICache {
    cache = new Map();
    defaultTTL = 5 * 60 * 1000;
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        return item.data;
    }
    set(key, data, ttl = this.defaultTTL) {
        const now = Date.now();
        this.cache.set(key, {
            data,
            timestamp: now,
            expiry: now + ttl
        });
    }
    clear() {
        this.cache.clear();
    }
    generateKey(endpoint, options) {
        const method = options?.method || 'GET';
        const body = options?.body || '';
        return `${method}:${endpoint}:${btoa(body).slice(0, 20)}`;
    }
}
// 全局API缓存实例
const apiCache = new APICache();
// 防抖管理
const debounceMap = new Map();
function debounce(func, delay, key) {
    return (...args)=>{
        return new Promise((resolve, reject)=>{
            // 清除之前的定时器
            if (debounceMap.has(key)) {
                clearTimeout(debounceMap.get(key));
            }
            // 设置新的定时器
            const timeoutId = setTimeout(async ()=>{
                try {
                    const result = await func(...args);
                    resolve(result);
                } catch (error) {
                    reject(error);
                } finally{
                    debounceMap.delete(key);
                }
            }, delay);
            debounceMap.set(key, timeoutId);
        });
    };
}
// 基础 API 调用函数
async function apiCall(endpoint, options) {
    try {
        // 构建正确的API URL
        let apiUrl;
        if ("TURBOPACK compile-time truthy", 1) {
            // 客户端：使用绝对URL
            apiUrl = `${window.location.origin}/api/knowledge${endpoint}`;
        } else {
            "TURBOPACK unreachable";
        }
        // 检查缓存（仅对GET请求）
        const method = options?.method || 'GET';
        if (method === 'GET') {
            const cacheKey = apiCache.generateKey(endpoint, options);
            const cachedData = apiCache.get(cacheKey);
            if (cachedData) {
                return cachedData;
            }
        }
        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30秒超时
        try {
            const response = await fetch(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options?.headers
                },
                signal: controller.signal,
                ...options
            });
            clearTimeout(timeoutId);
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('响应不是有效的JSON格式');
            }
            const data = await response.json();
            // 缓存成功的GET请求结果
            if (method === 'GET' && data.success) {
                const cacheKey = apiCache.generateKey(endpoint, options);
                // 搜索结果缓存时间较短，其他数据缓存时间较长
                const ttl = endpoint.includes('/search') ? 2 * 60 * 1000 : 5 * 60 * 1000;
                apiCache.set(cacheKey, data, ttl);
            }
            return data;
        } catch (fetchError) {
            clearTimeout(timeoutId);
            throw fetchError;
        }
    } catch (error) {
        console.error('API调用失败:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '网络错误'
        };
    }
}
const categoriesApi = {
    // 获取所有分类
    getAll: async (includeStats = false)=>{
        const params = includeStats ? '?includeStats=true' : '';
        return apiCall(`/categories${params}`);
    },
    // 获取单个分类
    getById: async (id, includeArticles = false)=>{
        const params = includeArticles ? '?includeArticles=true' : '';
        return apiCall(`/categories/${id}${params}`);
    },
    // 创建分类
    create: async (category)=>{
        return apiCall('/categories', {
            method: 'POST',
            body: JSON.stringify(category)
        });
    },
    // 更新分类
    update: async (id, updates)=>{
        return apiCall(`/categories/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除分类
    delete: async (id)=>{
        return apiCall(`/categories/${id}`, {
            method: 'DELETE'
        });
    },
    // 批量更新排序
    updateOrder: async (categories)=>{
        return apiCall('/categories', {
            method: 'PUT',
            body: JSON.stringify({
                categories
            })
        });
    }
};
const articlesApi = {
    // 获取文章列表
    getAll: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.category) searchParams.set('category', params.category);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.tags) searchParams.set('tags', params.tags);
        if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        if (params?.includeCodeExamples) searchParams.set('includeCodeExamples', 'true');
        if (params?.includeRelated) searchParams.set('includeRelated', 'true');
        const query = searchParams.toString();
        return apiCall(`/articles${query ? `?${query}` : ''}`);
    },
    // 获取单个文章
    getById: async (id, options)=>{
        const params = new URLSearchParams();
        if (options?.includeCodeExamples === false) params.set('includeCodeExamples', 'false');
        if (options?.includeRelated === false) params.set('includeRelated', 'false');
        const query = params.toString();
        return apiCall(`/articles/${id}${query ? `?${query}` : ''}`);
    },
    // 创建文章
    create: async (article)=>{
        return apiCall('/articles', {
            method: 'POST',
            body: JSON.stringify(article)
        });
    },
    // 更新文章
    update: async (id, updates)=>{
        return apiCall(`/articles/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除文章
    delete: async (id)=>{
        return apiCall(`/articles/${id}`, {
            method: 'DELETE'
        });
    },
    // 搜索文章（带防抖）
    search: debounce(async (query, params)=>{
        const searchParams = new URLSearchParams();
        searchParams.set('search', query);
        if (params?.category) searchParams.set('category', params.category);
        if (params?.tags) searchParams.set('tags', params.tags);
        if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        return apiCall(`/articles?${searchParams.toString()}`);
    }, 300, 'articles-search')
};
const codeExamplesApi = {
    // 获取代码示例列表
    getAll: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.articleId) searchParams.set('articleId', params.articleId);
        if (params?.language) searchParams.set('language', params.language);
        if (params?.search) searchParams.set('search', params.search);
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        const query = searchParams.toString();
        return apiCall(`/code-examples${query ? `?${query}` : ''}`);
    },
    // 创建代码示例
    create: async (example)=>{
        return apiCall('/code-examples', {
            method: 'POST',
            body: JSON.stringify(example)
        });
    },
    // 更新代码示例
    update: async (id, updates)=>{
        return apiCall(`/code-examples/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    },
    // 删除代码示例
    delete: async (id)=>{
        return apiCall(`/code-examples/${id}`, {
            method: 'DELETE'
        });
    },
    // 批量更新排序
    updateOrder: async (examples)=>{
        return apiCall('/code-examples', {
            method: 'PUT',
            body: JSON.stringify({
                examples
            })
        });
    }
};
const searchApi = {
    // 搜索文章（带防抖和缓存）
    search: debounce(async (params)=>{
        const searchParams = new URLSearchParams();
        searchParams.set('q', params.query);
        if (params.category) searchParams.set('category', params.category);
        if (params.tags) searchParams.set('tags', params.tags);
        if (params.difficulty) searchParams.set('difficulty', params.difficulty);
        if (params.page) searchParams.set('page', params.page.toString());
        if (params.limit) searchParams.set('limit', params.limit.toString());
        if (params.sortBy) searchParams.set('sortBy', params.sortBy);
        if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);
        return apiCall(`/search?${searchParams.toString()}`);
    }, 300, 'search-main'),
    // 获取搜索建议（带防抖）
    getSuggestions: debounce(async (query, limit = 5)=>{
        // 查询长度小于2时直接返回空结果，避免无意义的API调用
        if (!query || query.trim().length < 2) {
            return {
                success: true,
                data: []
            };
        }
        return apiCall('/search', {
            method: 'POST',
            body: JSON.stringify({
                query: query.trim(),
                limit
            })
        });
    }, 200, 'search-suggestions'),
    // 清除搜索缓存
    clearCache: ()=>{
        apiCache.clear();
    },
    // 取消所有防抖请求
    cancelPendingRequests: ()=>{
        debounceMap.forEach((timeoutId)=>{
            clearTimeout(timeoutId);
        });
        debounceMap.clear();
    }
};
const statsApi = {
    // 获取统计数据
    get: async (params)=>{
        const searchParams = new URLSearchParams();
        if (params?.period) searchParams.set('period', params.period);
        if (params?.includeSearchStats) searchParams.set('includeSearchStats', 'true');
        const query = searchParams.toString();
        return apiCall(`/stats${query ? `?${query}` : ''}`);
    },
    // 导出统计数据
    export: async (format = 'json', includeDetails = false)=>{
        return apiCall('/stats/export', {
            method: 'POST',
            body: JSON.stringify({
                format,
                includeDetails
            })
        });
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/components/knowledge/SearchSuggestions.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SearchSuggestions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// MySQLAi.de - 智能搜索建议组件
// 提供实时搜索建议、键盘导航、搜索历史显示功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/api/knowledge.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-client] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function SearchSuggestions({ query, isVisible, onSelect, onClose, searchHistory = [], className = '', maxSuggestions = 8, showHistory = true, showCategories = true }) {
    _s();
    const [suggestions, setSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const itemRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    // 使用ref存储最新值，避免useEffect依赖问题
    const suggestionsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(suggestions);
    const selectedIndexRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(selectedIndex);
    // 更新ref值
    suggestionsRef.current = suggestions;
    selectedIndexRef.current = selectedIndex;
    // 获取搜索建议
    const fetchSuggestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SearchSuggestions.useCallback[fetchSuggestions]": async (searchQuery)=>{
            if (!searchQuery || searchQuery.length < 2) {
                setSuggestions([]);
                return;
            }
            setLoading(true);
            setError(null);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["searchApi"].getSuggestions(searchQuery, maxSuggestions - 2);
                if (response.success && response.data) {
                    const apiSuggestions = response.data.map({
                        "SearchSuggestions.useCallback[fetchSuggestions].apiSuggestions": (item)=>({
                                ...item,
                                icon: item.type === 'article' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                    lineNumber: 75,
                                    columnNumber: 43
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                    lineNumber: 75,
                                    columnNumber: 78
                                }, this)
                            })
                    }["SearchSuggestions.useCallback[fetchSuggestions].apiSuggestions"]);
                    // 添加搜索历史建议 - 使用当前的props值而不是依赖
                    const historySuggestions = showHistory ? searchHistory.filter({
                        "SearchSuggestions.useCallback[fetchSuggestions]": (historyItem)=>historyItem.toLowerCase().includes(searchQuery.toLowerCase()) && historyItem !== searchQuery
                    }["SearchSuggestions.useCallback[fetchSuggestions]"]).slice(0, 2).map({
                        "SearchSuggestions.useCallback[fetchSuggestions]": (historyItem)=>({
                                type: 'history',
                                text: historyItem,
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                    lineNumber: 89,
                                    columnNumber: 23
                                }, this)
                            })
                    }["SearchSuggestions.useCallback[fetchSuggestions]"]) : [];
                    // 合并建议并去重
                    const allSuggestions = [
                        ...historySuggestions,
                        ...apiSuggestions
                    ];
                    const uniqueSuggestions = allSuggestions.filter({
                        "SearchSuggestions.useCallback[fetchSuggestions].uniqueSuggestions": (suggestion, index, self)=>index === self.findIndex({
                                "SearchSuggestions.useCallback[fetchSuggestions].uniqueSuggestions": (s)=>s.text === suggestion.text
                            }["SearchSuggestions.useCallback[fetchSuggestions].uniqueSuggestions"])
                    }["SearchSuggestions.useCallback[fetchSuggestions].uniqueSuggestions"]);
                    setSuggestions(uniqueSuggestions.slice(0, maxSuggestions));
                } else {
                    setError(response.error || '获取建议失败');
                    setSuggestions([]);
                }
            } catch (err) {
                console.error('获取搜索建议失败:', err);
                setError('网络错误，请稍后重试');
                setSuggestions([]);
            } finally{
                setLoading(false);
            }
        }
    }["SearchSuggestions.useCallback[fetchSuggestions]"], [
        maxSuggestions
    ]); // 只保留稳定的依赖
    // 监听查询变化
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchSuggestions.useEffect": ()=>{
            if (isVisible && query) {
                fetchSuggestions(query);
            } else {
                setSuggestions([]);
                setSelectedIndex(-1);
            }
        }
    }["SearchSuggestions.useEffect"], [
        query,
        isVisible
    ]); // 移除fetchSuggestions依赖，避免循环
    // 键盘导航处理
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchSuggestions.useEffect": ()=>{
            const handleKeyDown = {
                "SearchSuggestions.useEffect.handleKeyDown": (event)=>{
                    if (!isVisible || suggestionsRef.current.length === 0) return;
                    switch(event.key){
                        case 'ArrowDown':
                            event.preventDefault();
                            setSelectedIndex({
                                "SearchSuggestions.useEffect.handleKeyDown": (prev)=>prev < suggestionsRef.current.length - 1 ? prev + 1 : 0
                            }["SearchSuggestions.useEffect.handleKeyDown"]);
                            break;
                        case 'ArrowUp':
                            event.preventDefault();
                            setSelectedIndex({
                                "SearchSuggestions.useEffect.handleKeyDown": (prev)=>prev > 0 ? prev - 1 : suggestionsRef.current.length - 1
                            }["SearchSuggestions.useEffect.handleKeyDown"]);
                            break;
                        case 'Enter':
                            event.preventDefault();
                            if (selectedIndexRef.current >= 0 && selectedIndexRef.current < suggestionsRef.current.length) {
                                onSelect(suggestionsRef.current[selectedIndexRef.current]);
                            }
                            break;
                        case 'Escape':
                            event.preventDefault();
                            onClose();
                            break;
                    }
                }
            }["SearchSuggestions.useEffect.handleKeyDown"];
            if (isVisible) {
                document.addEventListener('keydown', handleKeyDown);
                return ({
                    "SearchSuggestions.useEffect": ()=>document.removeEventListener('keydown', handleKeyDown)
                })["SearchSuggestions.useEffect"];
            }
        }
    }["SearchSuggestions.useEffect"], [
        isVisible,
        onSelect,
        onClose
    ]); // 只依赖isVisible和稳定的回调函数
    // 滚动到选中项
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchSuggestions.useEffect": ()=>{
            if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {
                itemRefs.current[selectedIndex]?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }
        }
    }["SearchSuggestions.useEffect"], [
        selectedIndex
    ]);
    // 点击外部关闭
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SearchSuggestions.useEffect": ()=>{
            const handleClickOutside = {
                "SearchSuggestions.useEffect.handleClickOutside": (event)=>{
                    if (containerRef.current && !containerRef.current.contains(event.target)) {
                        onClose();
                    }
                }
            }["SearchSuggestions.useEffect.handleClickOutside"];
            if (isVisible) {
                document.addEventListener('mousedown', handleClickOutside);
                return ({
                    "SearchSuggestions.useEffect": ()=>document.removeEventListener('mousedown', handleClickOutside)
                })["SearchSuggestions.useEffect"];
            }
        }
    }["SearchSuggestions.useEffect"], [
        isVisible,
        onClose
    ]);
    // 处理建议项点击
    const handleSuggestionClick = (suggestion)=>{
        onSelect(suggestion);
    };
    // 获取建议项样式
    const getSuggestionItemClass = (index)=>{
        const baseClass = "flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150";
        const selectedClass = index === selectedIndex ? "bg-blue-50 text-blue-700 border-l-2 border-blue-500" : "hover:bg-gray-50 text-gray-700";
        return `${baseClass} ${selectedClass}`;
    };
    // 获取建议类型标签
    const getTypeLabel = (type)=>{
        switch(type){
            case 'article':
                return '文章';
            case 'query':
                return '热门';
            case 'history':
                return '历史';
            default:
                return '';
        }
    };
    if (!isVisible) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: containerRef,
        className: `absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto ${className}`,
        role: "listbox",
        "aria-label": "搜索建议",
        children: [
            loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center py-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                        lineNumber: 220,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "ml-2 text-sm text-gray-500",
                        children: "获取建议中..."
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                        lineNumber: 221,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                lineNumber: 219,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-4 py-3 text-sm text-red-600 bg-red-50",
                children: error
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this),
            !loading && !error && suggestions.length === 0 && query.length >= 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-4 py-3 text-sm text-gray-500 text-center",
                children: "暂无相关建议"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                lineNumber: 232,
                columnNumber: 9
            }, this),
            !loading && !error && suggestions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "py-1",
                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: (el)=>itemRefs.current[index] = el,
                        className: getSuggestionItemClass(index),
                        onClick: ()=>handleSuggestionClick(suggestion),
                        role: "option",
                        "aria-selected": index === selectedIndex,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0 text-gray-400",
                                children: suggestion.icon
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                lineNumber: 248,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 min-w-0",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-medium truncate",
                                                children: suggestion.text
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                                lineNumber: 254,
                                                columnNumber: 19
                                            }, this),
                                            showCategories && suggestion.category && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full",
                                                children: suggestion.category
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                                lineNumber: 258,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                        lineNumber: 253,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-500 mt-1",
                                        children: getTypeLabel(suggestion.type)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                        lineNumber: 263,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                lineNumber: 252,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                className: "w-4 h-4 text-gray-400 flex-shrink-0"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                                lineNumber: 268,
                                columnNumber: 15
                            }, this)
                        ]
                    }, `${suggestion.type}-${suggestion.text}-${index}`, true, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                        lineNumber: 240,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                lineNumber: 238,
                columnNumber: 9
            }, this),
            !loading && !error && suggestions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t",
                children: "使用 ↑↓ 键导航，回车选择，ESC 关闭"
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
                lineNumber: 275,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/SearchSuggestions.tsx",
        lineNumber: 212,
        columnNumber: 5
    }, this);
}
_s(SearchSuggestions, "xCt6IAsBRzYCAVkXrfzxTHAk8XM=");
_c = SearchSuggestions;
var _c;
__turbopack_context__.k.register(_c, "SearchSuggestions");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AdvancedSearchFilters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// MySQLAi.de - 高级搜索筛选组件
// 提供分类、难度、标签、排序等多维度搜索筛选功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-client] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
// 难度选项
const DIFFICULTY_OPTIONS = [
    {
        value: 'beginner',
        label: '初级',
        color: 'bg-green-100 text-green-800'
    },
    {
        value: 'intermediate',
        label: '中级',
        color: 'bg-yellow-100 text-yellow-800'
    },
    {
        value: 'advanced',
        label: '高级',
        color: 'bg-red-100 text-red-800'
    },
    {
        value: 'expert',
        label: '专家',
        color: 'bg-purple-100 text-purple-800'
    }
];
// 排序选项
const SORT_OPTIONS = [
    {
        value: 'relevance',
        label: '相关性',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
            className: "w-4 h-4"
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
            lineNumber: 62,
            columnNumber: 45
        }, this)
    },
    {
        value: 'date',
        label: '更新时间',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
            className: "w-4 h-4"
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
            lineNumber: 63,
            columnNumber: 41
        }, this)
    },
    {
        value: 'title',
        label: '标题',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
            className: "w-4 h-4"
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
            lineNumber: 64,
            columnNumber: 40
        }, this)
    }
];
function AdvancedSearchFilters({ filters, onFiltersChange, categories = [], availableTags = [], className = '', isCollapsed = false, onToggleCollapse, showResultCount = true, resultCount = 0 }) {
    _s();
    const [isExpanded, setIsExpanded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!isCollapsed);
    const [tagSearchQuery, setTagSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [showTagDropdown, setShowTagDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // 处理筛选器变化
    const handleFilterChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AdvancedSearchFilters.useCallback[handleFilterChange]": (key, value)=>{
            const newFilters = {
                ...filters,
                [key]: value
            };
            onFiltersChange(newFilters);
        }
    }["AdvancedSearchFilters.useCallback[handleFilterChange]"], [
        filters,
        onFiltersChange
    ]);
    // 处理标签添加
    const handleTagAdd = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AdvancedSearchFilters.useCallback[handleTagAdd]": (tagValue)=>{
            if (!filters.tags.includes(tagValue)) {
                const newTags = [
                    ...filters.tags,
                    tagValue
                ];
                handleFilterChange('tags', newTags);
            }
            setTagSearchQuery('');
            setShowTagDropdown(false);
        }
    }["AdvancedSearchFilters.useCallback[handleTagAdd]"], [
        filters.tags,
        handleFilterChange
    ]);
    // 处理标签移除
    const handleTagRemove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AdvancedSearchFilters.useCallback[handleTagRemove]": (tagValue)=>{
            const newTags = filters.tags.filter({
                "AdvancedSearchFilters.useCallback[handleTagRemove].newTags": (tag)=>tag !== tagValue
            }["AdvancedSearchFilters.useCallback[handleTagRemove].newTags"]);
            handleFilterChange('tags', newTags);
        }
    }["AdvancedSearchFilters.useCallback[handleTagRemove]"], [
        filters.tags,
        handleFilterChange
    ]);
    // 清空所有筛选器
    const handleClearAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AdvancedSearchFilters.useCallback[handleClearAll]": ()=>{
            onFiltersChange({
                tags: [],
                sortBy: 'relevance',
                sortOrder: 'desc'
            });
        }
    }["AdvancedSearchFilters.useCallback[handleClearAll]"], [
        onFiltersChange
    ]);
    // 切换展开/收起
    const handleToggleExpanded = ()=>{
        const newExpanded = !isExpanded;
        setIsExpanded(newExpanded);
        onToggleCollapse?.();
    };
    // 过滤可用标签
    const filteredTags = availableTags.filter((tag)=>tag.label.toLowerCase().includes(tagSearchQuery.toLowerCase()) && !filters.tags.includes(tag.value));
    // 检查是否有活动筛选器
    const hasActiveFilters = filters.category || filters.difficulty || filters.tags.length > 0 || filters.sortBy !== 'relevance' || filters.sortOrder !== 'desc';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white border border-gray-200 rounded-lg shadow-sm ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between p-4 border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleToggleExpanded,
                                className: "flex items-center gap-2 text-gray-700 hover:text-gray-900 transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: "高级筛选"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 144,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                        className: `w-4 h-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 145,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 138,
                                columnNumber: 11
                            }, this),
                            hasActiveFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",
                                children: [
                                    [
                                        filters.category ? 1 : 0,
                                        filters.difficulty ? 1 : 0,
                                        filters.tags.length,
                                        filters.sortBy !== 'relevance' || filters.sortOrder !== 'desc' ? 1 : 0
                                    ].reduce((a, b)=>a + b, 0),
                                    " 个筛选器"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 137,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            showResultCount && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-gray-500",
                                children: [
                                    resultCount,
                                    " 个结果"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this),
                            hasActiveFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleClearAll,
                                className: "text-sm text-gray-500 hover:text-red-600 transition-colors",
                                children: "清空筛选"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 172,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this),
            isExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 space-y-4",
                children: [
                    categories.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 mb-2",
                                children: "分类"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 189,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>handleFilterChange('category', undefined),
                                        className: `px-3 py-2 text-sm rounded-md border transition-colors ${!filters.category ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`,
                                        children: "全部分类"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 193,
                                        columnNumber: 17
                                    }, this),
                                    categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: ()=>handleFilterChange('category', category.value),
                                            className: `px-3 py-2 text-sm rounded-md border transition-colors ${filters.category === category.value ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`,
                                            children: [
                                                category.label,
                                                category.count && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "ml-1 text-xs text-gray-500",
                                                    children: [
                                                        "(",
                                                        category.count,
                                                        ")"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                    lineNumber: 217,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, category.value, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                            lineNumber: 205,
                                            columnNumber: 19
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 192,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 188,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 mb-2",
                                children: "难度等级"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 229,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>handleFilterChange('difficulty', undefined),
                                        className: `px-3 py-2 text-sm rounded-md border transition-colors ${!filters.difficulty ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`,
                                        children: "全部难度"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 233,
                                        columnNumber: 15
                                    }, this),
                                    DIFFICULTY_OPTIONS.map((difficulty)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: ()=>handleFilterChange('difficulty', difficulty.value),
                                            className: `px-3 py-2 text-sm rounded-md border transition-colors ${filters.difficulty === difficulty.value ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `inline-block w-2 h-2 rounded-full mr-2 ${difficulty.color?.split(' ')[0] || 'bg-gray-300'}`
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                    lineNumber: 255,
                                                    columnNumber: 19
                                                }, this),
                                                difficulty.label
                                            ]
                                        }, difficulty.value, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                            lineNumber: 245,
                                            columnNumber: 17
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 232,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 228,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 mb-2",
                                children: "标签"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 264,
                                columnNumber: 13
                            }, this),
                            filters.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2 mb-3",
                                children: filters.tags.map((tag)=>{
                                    const tagOption = availableTags.find((t)=>t.value === tag);
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                className: "w-3 h-3"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                lineNumber: 278,
                                                columnNumber: 23
                                            }, this),
                                            tagOption?.label || tag,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>handleTagRemove(tag),
                                                className: "ml-1 hover:text-blue-600",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                    className: "w-3 h-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                    lineNumber: 285,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                lineNumber: 280,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, tag, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 274,
                                        columnNumber: 21
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 270,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "text",
                                        value: tagSearchQuery,
                                        onChange: (e)=>setTagSearchQuery(e.target.value),
                                        onFocus: ()=>setShowTagDropdown(true),
                                        onBlur: ()=>setTimeout(()=>setShowTagDropdown(false), 200),
                                        placeholder: "搜索标签...",
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 295,
                                        columnNumber: 15
                                    }, this),
                                    showTagDropdown && filteredTags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto",
                                        children: filteredTags.slice(0, 10).map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>handleTagAdd(tag.value),
                                                className: "w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                        className: "w-4 h-4 text-gray-400"
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                        lineNumber: 315,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: tag.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                        lineNumber: 316,
                                                        columnNumber: 23
                                                    }, this),
                                                    tag.count && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "ml-auto text-xs text-gray-500",
                                                        children: tag.count
                                                    }, void 0, false, {
                                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                        lineNumber: 318,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, tag.value, true, {
                                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                                lineNumber: 309,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 307,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 294,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 263,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-4 pt-2 border-t border-gray-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                        className: "w-4 h-4 text-gray-500"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 332,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm font-medium text-gray-700",
                                        children: "排序:"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 333,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 331,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: SORT_OPTIONS.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>handleFilterChange('sortBy', option.value),
                                        className: `flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors ${filters.sortBy === option.value ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`,
                                        children: [
                                            option.icon,
                                            option.label
                                        ]
                                    }, option.value, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                        lineNumber: 338,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 336,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: ()=>handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc'),
                                className: "flex items-center gap-1 px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors",
                                title: filters.sortOrder === 'asc' ? '升序' : '降序',
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: `transform transition-transform ${filters.sortOrder === 'asc' ? 'rotate-180' : ''}`,
                                    children: "↓"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                    lineNumber: 360,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                                lineNumber: 354,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                        lineNumber: 330,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
                lineNumber: 185,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
}
_s(AdvancedSearchFilters, "EFzME/jEbQhv+ELxB+iO1m8RtBg=");
_c = AdvancedSearchFilters;
var _c;
__turbopack_context__.k.register(_c, "AdvancedSearchFilters");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/hooks/useSearchHistory.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useSearchHistory": (()=>useSearchHistory)
});
// MySQLAi.de - 搜索历史管理Hook
// 提供本地搜索历史存储、去重、排序和清理功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
// 默认配置
const DEFAULT_OPTIONS = {
    maxItems: 50,
    storageKey: 'mysqlai_search_history',
    enableFrequencyTracking: true,
    autoCleanup: true,
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
};
function useSearchHistory(options = {}) {
    _s();
    const opts = {
        ...DEFAULT_OPTIONS,
        ...options
    };
    const [historyItems, setHistoryItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // 从本地存储加载历史记录
    const loadHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[loadHistory]": ()=>{
            try {
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
                const stored = localStorage.getItem(opts.storageKey);
                if (stored) {
                    const parsed = JSON.parse(stored);
                    // 自动清理过期项
                    if (opts.autoCleanup) {
                        const now = Date.now();
                        const validItems = parsed.filter({
                            "useSearchHistory.useCallback[loadHistory].validItems": (item)=>now - item.timestamp <= opts.maxAge
                        }["useSearchHistory.useCallback[loadHistory].validItems"]);
                        setHistoryItems(validItems);
                        // 如果清理了项目，更新存储
                        if (validItems.length !== parsed.length) {
                            localStorage.setItem(opts.storageKey, JSON.stringify(validItems));
                        }
                    } else {
                        setHistoryItems(parsed);
                    }
                }
            } catch (error) {
                console.error('加载搜索历史失败:', error);
                setHistoryItems([]);
            }
        }
    }["useSearchHistory.useCallback[loadHistory]"], [
        opts.storageKey,
        opts.autoCleanup,
        opts.maxAge
    ]);
    // 保存历史记录到本地存储
    const saveHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[saveHistory]": (items)=>{
            try {
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
                localStorage.setItem(opts.storageKey, JSON.stringify(items));
            } catch (error) {
                console.error('保存搜索历史失败:', error);
            }
        }
    }["useSearchHistory.useCallback[saveHistory]"], [
        opts.storageKey
    ]);
    // 初始化加载
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSearchHistory.useEffect": ()=>{
            loadHistory();
        }
    }["useSearchHistory.useEffect"], [
        loadHistory
    ]);
    // 添加搜索记录
    const addToHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[addToHistory]": (query)=>{
            if (!query || query.trim().length < 2) return;
            const trimmedQuery = query.trim();
            const now = Date.now();
            setHistoryItems({
                "useSearchHistory.useCallback[addToHistory]": (prevItems)=>{
                    // 查找是否已存在
                    const existingIndex = prevItems.findIndex({
                        "useSearchHistory.useCallback[addToHistory].existingIndex": (item)=>item.query.toLowerCase() === trimmedQuery.toLowerCase()
                    }["useSearchHistory.useCallback[addToHistory].existingIndex"]);
                    let newItems;
                    if (existingIndex >= 0) {
                        // 更新现有项目
                        newItems = [
                            ...prevItems
                        ];
                        newItems[existingIndex] = {
                            ...newItems[existingIndex],
                            timestamp: now,
                            count: opts.enableFrequencyTracking ? newItems[existingIndex].count + 1 : 1
                        };
                    } else {
                        // 添加新项目
                        const newItem = {
                            query: trimmedQuery,
                            timestamp: now,
                            count: 1
                        };
                        newItems = [
                            newItem,
                            ...prevItems
                        ];
                    }
                    // 限制数量
                    if (newItems.length > opts.maxItems) {
                        newItems = newItems.slice(0, opts.maxItems);
                    }
                    // 按时间戳排序（最新的在前）
                    newItems.sort({
                        "useSearchHistory.useCallback[addToHistory]": (a, b)=>b.timestamp - a.timestamp
                    }["useSearchHistory.useCallback[addToHistory]"]);
                    // 保存到本地存储
                    saveHistory(newItems);
                    return newItems;
                }
            }["useSearchHistory.useCallback[addToHistory]"]);
        }
    }["useSearchHistory.useCallback[addToHistory]"], [
        opts.maxItems,
        opts.enableFrequencyTracking,
        saveHistory
    ]);
    // 删除搜索记录
    const removeFromHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[removeFromHistory]": (query)=>{
            setHistoryItems({
                "useSearchHistory.useCallback[removeFromHistory]": (prevItems)=>{
                    const newItems = prevItems.filter({
                        "useSearchHistory.useCallback[removeFromHistory].newItems": (item)=>item.query.toLowerCase() !== query.toLowerCase()
                    }["useSearchHistory.useCallback[removeFromHistory].newItems"]);
                    saveHistory(newItems);
                    return newItems;
                }
            }["useSearchHistory.useCallback[removeFromHistory]"]);
        }
    }["useSearchHistory.useCallback[removeFromHistory]"], [
        saveHistory
    ]);
    // 清空搜索历史
    const clearHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[clearHistory]": ()=>{
            setHistoryItems([]);
            saveHistory([]);
        }
    }["useSearchHistory.useCallback[clearHistory]"], [
        saveHistory
    ]);
    // 获取搜索次数
    const getSearchCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[getSearchCount]": (query)=>{
            const item = historyItems.find({
                "useSearchHistory.useCallback[getSearchCount].item": (item)=>item.query.toLowerCase() === query.toLowerCase()
            }["useSearchHistory.useCallback[getSearchCount].item"]);
            return item?.count || 0;
        }
    }["useSearchHistory.useCallback[getSearchCount]"], [
        historyItems
    ]);
    // 导出历史记录
    const exportHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[exportHistory]": ()=>{
            return JSON.stringify({
                version: '1.0',
                timestamp: Date.now(),
                data: historyItems
            });
        }
    }["useSearchHistory.useCallback[exportHistory]"], [
        historyItems
    ]);
    // 导入历史记录
    const importHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSearchHistory.useCallback[importHistory]": (data)=>{
            try {
                const parsed = JSON.parse(data);
                if (parsed.data && Array.isArray(parsed.data)) {
                    setHistoryItems(parsed.data);
                    saveHistory(parsed.data);
                    return true;
                }
                return false;
            } catch (error) {
                console.error('导入搜索历史失败:', error);
                return false;
            }
        }
    }["useSearchHistory.useCallback[importHistory]"], [
        saveHistory
    ]);
    // 计算派生数据
    const history = historyItems.map((item)=>item.query);
    const recentHistory = historyItems.sort((a, b)=>b.timestamp - a.timestamp).slice(0, 10).map((item)=>item.query);
    const popularHistory = opts.enableFrequencyTracking ? historyItems.filter((item)=>item.count > 1).sort((a, b)=>b.count - a.count).slice(0, 10).map((item)=>item.query) : [];
    const totalSearches = historyItems.reduce((sum, item)=>sum + item.count, 0);
    const uniqueSearches = historyItems.length;
    return {
        // 历史记录
        history,
        recentHistory,
        popularHistory,
        // 操作方法
        addToHistory,
        removeFromHistory,
        clearHistory,
        getSearchCount,
        // 统计信息
        totalSearches,
        uniqueSearches,
        // 工具方法
        exportHistory,
        importHistory
    };
}
_s(useSearchHistory, "AHNXeE/NUt9e1V4iq2IN/C7ZmLE=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/components/knowledge/SearchHistory.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SearchHistory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// MySQLAi.de - 搜索历史界面组件
// 提供搜索历史显示、管理和快速重新搜索功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/hooks/useSearchHistory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript) <export default as RotateCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
// 历史项组件
function HistoryItem({ query, count, timestamp, onSelect, onRemove, showCount = false }) {
    _s();
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const formatTimestamp = (ts)=>{
        if (!ts) return '';
        const date = new Date(ts);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return '今天';
        if (diffDays === 1) return '昨天';
        if (diffDays < 7) return `${diffDays}天前`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
        return `${Math.floor(diffDays / 30)}月前`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 group",
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>onSelect(query),
                className: "flex-1 flex items-center gap-3 text-left min-w-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                        className: "w-4 h-4 text-gray-400 flex-shrink-0"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 min-w-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "font-medium text-gray-900 truncate",
                                children: query
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 text-xs text-gray-500 mt-1",
                                children: [
                                    timestamp && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: formatTimestamp(timestamp)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 80,
                                        columnNumber: 15
                                    }, this),
                                    showCount && count && count > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "•"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                                lineNumber: 84,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: [
                                                    count,
                                                    "次搜索"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                                lineNumber: 85,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 78,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            isHovered && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: (e)=>{
                    e.stopPropagation();
                    onRemove(query);
                },
                className: "p-1 text-gray-400 hover:text-red-500 transition-colors duration-150",
                title: "删除此搜索记录",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 102,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 93,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
}
_s(HistoryItem, "FPQn8a98tPjpohC7NUYORQR8GJE=");
_c = HistoryItem;
function SearchHistory({ onSearchSelect, onClose, className = '', showStats = true, maxRecentItems = 8, maxPopularItems = 5 }) {
    _s1();
    const { recentHistory, popularHistory, totalSearches, uniqueSearches, removeFromHistory, clearHistory, getSearchCount } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchHistory"])();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('recent');
    const [showClearConfirm, setShowClearConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // 处理搜索选择
    const handleSearchSelect = (query)=>{
        onSearchSelect(query);
        onClose?.();
    };
    // 处理删除单个记录
    const handleRemoveItem = (query)=>{
        removeFromHistory(query);
    };
    // 处理清空所有记录
    const handleClearAll = ()=>{
        if (showClearConfirm) {
            clearHistory();
            setShowClearConfirm(false);
            onClose?.();
        } else {
            setShowClearConfirm(true);
        }
    };
    // 取消清空确认
    const handleCancelClear = ()=>{
        setShowClearConfirm(false);
    };
    const hasHistory = recentHistory.length > 0 || popularHistory.length > 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white border border-gray-200 rounded-lg shadow-lg ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between p-4 border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900",
                        children: "搜索历史"
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleClearAll,
                                className: `px-3 py-1 text-sm rounded-md transition-colors duration-150 ${showClearConfirm ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'text-gray-500 hover:text-red-600 hover:bg-red-50'}`,
                                children: showClearConfirm ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "确认清空?"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 177,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: handleCancelClear,
                                            className: "text-gray-500 hover:text-gray-700",
                                            children: "取消"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 178,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 176,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                            className: "w-4 h-4"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 188,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "清空"
                                        }, void 0, false, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 187,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this),
                            onClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onClose,
                                className: "p-1 text-gray-400 hover:text-gray-600 transition-colors duration-150",
                                title: "关闭搜索历史",
                                "aria-label": "关闭搜索历史",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "w-5 h-5"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 195,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 162,
                columnNumber: 7
            }, this),
            showStats && hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-4 py-3 bg-gray-50 border-b border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4 text-sm text-gray-600",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 213,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: [
                                        "总搜索: ",
                                        totalSearches,
                                        "次"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 212,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 217,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: [
                                        "不同查询: ",
                                        uniqueSearches,
                                        "个"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 218,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 216,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 211,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this),
            hasHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setActiveTab('recent'),
                        className: `flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${activeTab === 'recent' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 237,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "最近搜索"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 238,
                                    columnNumber: 15
                                }, this),
                                recentHistory.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full",
                                    children: recentHistory.length
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 240,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 236,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setActiveTab('popular'),
                        className: `flex-1 px-4 py-3 text-sm font-medium transition-colors duration-150 ${activeTab === 'popular' ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' : 'text-gray-500 hover:text-gray-700'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 256,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "热门搜索"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 257,
                                    columnNumber: 15
                                }, this),
                                popularHistory.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full",
                                    children: popularHistory.length
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 259,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 255,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4",
                children: !hasHistory ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center py-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                            className: "w-12 h-12 text-gray-300 mx-auto mb-3"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 272,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 text-sm",
                            children: "暂无搜索历史"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 273,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-400 text-xs mt-1",
                            children: "开始搜索后，历史记录会显示在这里"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                            lineNumber: 274,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 271,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-1",
                    children: [
                        activeTab === 'recent' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                recentHistory.slice(0, maxRecentItems).map((query, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(HistoryItem, {
                                        query: query,
                                        count: getSearchCount(query),
                                        onSelect: handleSearchSelect,
                                        onRemove: handleRemoveItem,
                                        showCount: true
                                    }, `recent-${query}-${index}`, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 281,
                                        columnNumber: 19
                                    }, this)),
                                recentHistory.length > maxRecentItems && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-500",
                                        children: [
                                            "还有 ",
                                            recentHistory.length - maxRecentItems,
                                            " 条记录..."
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 292,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 291,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true),
                        activeTab === 'popular' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: popularHistory.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center py-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "w-8 h-8 text-gray-300 mx-auto mb-2"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 304,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-500 text-sm",
                                        children: "暂无热门搜索"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 305,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-400 text-xs mt-1",
                                        children: "多次搜索相同内容后会显示在这里"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                        lineNumber: 306,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                lineNumber: 303,
                                columnNumber: 19
                            }, this) : popularHistory.slice(0, maxPopularItems).map((query, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(HistoryItem, {
                                    query: query,
                                    count: getSearchCount(query),
                                    onSelect: handleSearchSelect,
                                    onRemove: handleRemoveItem,
                                    showCount: true
                                }, `popular-${query}-${index}`, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                                    lineNumber: 310,
                                    columnNumber: 21
                                }, this))
                        }, void 0, false)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                    lineNumber: 277,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
                lineNumber: 269,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/SearchHistory.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
}
_s1(SearchHistory, "pP8DNCJZ28rZI4YANanNi35JcSo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchHistory"]
    ];
});
_c1 = SearchHistory;
var _c, _c1;
__turbopack_context__.k.register(_c, "HistoryItem");
__turbopack_context__.k.register(_c1, "SearchHistory");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/components/knowledge/SearchResultItem.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SearchResultItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// MySQLAi.de - 搜索结果项组件
// 提供搜索结果高亮显示、相关性分数、快速操作功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bookmark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bookmark$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bookmark.js [app-client] (ecmascript) <export default as Bookmark>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/share.js [app-client] (ecmascript) <export default as Share>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-client] (ecmascript) <export default as ChevronRight>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
// 难度配置
const DIFFICULTY_CONFIG = {
    beginner: {
        label: '初级',
        color: 'bg-green-100 text-green-800',
        icon: '●'
    },
    intermediate: {
        label: '中级',
        color: 'bg-yellow-100 text-yellow-800',
        icon: '●●'
    },
    advanced: {
        label: '高级',
        color: 'bg-orange-100 text-orange-800',
        icon: '●●●'
    },
    expert: {
        label: '专家',
        color: 'bg-red-100 text-red-800',
        icon: '●●●●'
    }
};
function SearchResultItem({ item, query, onBookmark, onShare, showRelevanceScore = false, showActions = true, className = '', isBookmarked = false }) {
    _s();
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // 处理高亮文本渲染
    const renderHighlightedText = (text, highlightedText)=>{
        if (!highlightedText || !query) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: text
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                lineNumber: 79,
                columnNumber: 14
            }, this);
        }
        // 如果有高亮文本，直接使用（已包含<mark>标签）
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            dangerouslySetInnerHTML: {
                __html: highlightedText
            },
            className: "[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded"
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
            lineNumber: 84,
            columnNumber: 7
        }, this);
    };
    // 格式化时间
    const formatDate = (dateString)=>{
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return '今天';
        if (diffDays === 1) return '昨天';
        if (diffDays < 7) return `${diffDays}天前`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
        return `${Math.floor(diffDays / 30)}月前`;
    };
    // 处理收藏
    const handleBookmark = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        onBookmark?.(item.id, !isBookmarked);
    };
    // 处理分享
    const handleShare = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        onShare?.(item);
    };
    // 获取难度配置
    const difficultyConfig = item.difficulty ? DIFFICULTY_CONFIG[item.difficulty] : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `group bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 ${className}`,
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: `/knowledge/${item.id}`,
            className: "block p-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start justify-between mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 min-w-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2",
                                    children: renderHighlightedText(item.title, item.highlight?.title)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 133,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-4 text-sm text-gray-500 mb-3",
                                    children: [
                                        item.category && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-3 h-3 rounded-full",
                                                    style: {
                                                        backgroundColor: item.category.color || '#6B7280'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 142,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: item.category.name
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 146,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                            lineNumber: 141,
                                            columnNumber: 17
                                        }, this),
                                        difficultyConfig && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `px-2 py-1 rounded-full text-xs font-medium ${difficultyConfig.color}`,
                                            children: [
                                                difficultyConfig.icon,
                                                " ",
                                                difficultyConfig.label
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                            lineNumber: 152,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 159,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: formatDate(item.last_updated)
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 160,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                            lineNumber: 158,
                                            columnNumber: 15
                                        }, this),
                                        item.view_count && item.view_count > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: item.view_count
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                            lineNumber: 165,
                                            columnNumber: 17
                                        }, this),
                                        showRelevanceScore && item.relevanceScore && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        "相关性: ",
                                                        Math.round(item.relevanceScore)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                                    lineNumber: 175,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                            lineNumber: 173,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 131,
                            columnNumber: 11
                        }, this),
                        showActions && isHovered && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 ml-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: handleBookmark,
                                    className: "p-2 text-gray-400 hover:text-yellow-500 transition-colors",
                                    title: isBookmarked ? '取消收藏' : '收藏文章',
                                    children: isBookmarked ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bookmark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bookmark$3e$__["Bookmark"], {
                                        className: "w-5 h-5 text-yellow-500 fill-current"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                        lineNumber: 191,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bookmark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bookmark$3e$__["Bookmark"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                        lineNumber: 193,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 184,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: handleShare,
                                    className: "p-2 text-gray-400 hover:text-blue-500 transition-colors",
                                    title: "分享文章",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share$3e$__["Share"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                        lineNumber: 203,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 197,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 183,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                    lineNumber: 130,
                    columnNumber: 9
                }, this),
                item.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2",
                    children: renderHighlightedText(item.description, item.highlight?.description)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                    lineNumber: 211,
                    columnNumber: 11
                }, this),
                item.highlight?.content && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-50 border-l-4 border-blue-500 p-3 mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-700 leading-relaxed",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            dangerouslySetInnerHTML: {
                                __html: item.highlight.content
                            },
                            className: "[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 220,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                        lineNumber: 219,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                    lineNumber: 218,
                    columnNumber: 11
                }, this),
                item.tags && item.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2 mb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                            className: "w-4 h-4 text-gray-400"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-2",
                            children: [
                                item.tags.slice(0, 5).map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full hover:bg-gray-200 transition-colors",
                                        children: tag
                                    }, index, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                        lineNumber: 234,
                                        columnNumber: 17
                                    }, this)),
                                item.tags.length > 5 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full",
                                    children: [
                                        "+",
                                        item.tags.length - 5
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 242,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 232,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                    lineNumber: 230,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 text-xs text-gray-400",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 253,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "知识库文章"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 254,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 252,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1 text-blue-600 group-hover:text-blue-700 transition-colors",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm font-medium",
                                    children: "查看详情"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 258,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                    className: "w-4 h-4 group-hover:translate-x-1 transition-transform"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                                    lineNumber: 259,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
                    lineNumber: 251,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
            lineNumber: 128,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/knowledge/SearchResultItem.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
}
_s(SearchResultItem, "FPQn8a98tPjpohC7NUYORQR8GJE=");
_c = SearchResultItem;
var _c;
__turbopack_context__.k.register(_c, "SearchResultItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/components/knowledge/IntelligentSearch.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>IntelligentSearch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// MySQLAi.de - 智能搜索主组件
// 整合搜索输入、建议、筛选、历史、结果等所有搜索功能
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchSuggestions$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/SearchSuggestions.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$AdvancedSearchFilters$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/AdvancedSearchFilters.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/SearchHistory.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchResultItem$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/SearchResultItem.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/hooks/useSearchHistory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/api/knowledge.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function IntelligentSearch({ placeholder = '搜索知识库...', autoFocus = false, showAdvancedFilters = true, showSearchHistory = true, maxResults = 20, className = '', onResultSelect, categories = [], availableTags = [] }) {
    _s();
    // 状态管理
    const [searchState, setSearchState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        query: '',
        isSearching: false,
        results: [],
        suggestions: [],
        showSuggestions: false,
        showHistory: false,
        showFilters: false,
        filters: {
            tags: [],
            sortBy: 'relevance',
            sortOrder: 'desc'
        },
        totalResults: 0
    });
    // 搜索历史Hook
    const { addToHistory, recentHistory, popularHistory, clearHistory, removeFromHistory, getSearchCount, totalSearches, uniqueSearches } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchHistory"])();
    // 引用
    const searchInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const searchTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    // 执行搜索
    const performSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[performSearch]": async (query, filters)=>{
            if (!query.trim()) {
                setSearchState({
                    "IntelligentSearch.useCallback[performSearch]": (prev)=>({
                            ...prev,
                            results: [],
                            totalResults: 0,
                            showSuggestions: false
                        })
                }["IntelligentSearch.useCallback[performSearch]"]);
                return;
            }
            setSearchState({
                "IntelligentSearch.useCallback[performSearch]": (prev)=>({
                        ...prev,
                        isSearching: true,
                        error: undefined
                    })
            }["IntelligentSearch.useCallback[performSearch]"]);
            try {
                // 使用传入的filters或当前状态的filters
                const currentFilters = filters || searchState.filters;
                // 调用搜索API
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$api$2f$knowledge$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["searchApi"].search({
                    query,
                    category: currentFilters.category,
                    difficulty: currentFilters.difficulty,
                    tags: currentFilters.tags,
                    sortBy: currentFilters.sortBy,
                    sortOrder: currentFilters.sortOrder,
                    limit: maxResults
                });
                if (response.success) {
                    // 添加到搜索历史
                    addToHistory(query);
                    setSearchState({
                        "IntelligentSearch.useCallback[performSearch]": (prev)=>({
                                ...prev,
                                results: response.data || [],
                                totalResults: response.data?.length || 0,
                                isSearching: false,
                                showSuggestions: false,
                                showHistory: false
                            })
                    }["IntelligentSearch.useCallback[performSearch]"]);
                } else {
                    throw new Error(response.error || '搜索失败');
                }
            } catch (error) {
                console.error('Search error:', error);
                setSearchState({
                    "IntelligentSearch.useCallback[performSearch]": (prev)=>({
                            ...prev,
                            error: error instanceof Error ? error.message : '搜索出错，请稍后重试',
                            isSearching: false,
                            results: [],
                            totalResults: 0
                        })
                }["IntelligentSearch.useCallback[performSearch]"]);
            }
        }
    }["IntelligentSearch.useCallback[performSearch]"], [
        maxResults,
        addToHistory
    ]); // 移除searchState.filters依赖
    // 处理搜索输入变化
    const handleSearchChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleSearchChange]": (value)=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleSearchChange]": (prev)=>({
                        ...prev,
                        query: value,
                        showSuggestions: value.length >= 2,
                        showHistory: value.length === 0
                    })
            }["IntelligentSearch.useCallback[handleSearchChange]"]);
            // 清除之前的搜索定时器
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
            // 如果查询为空，清空结果
            if (!value.trim()) {
                setSearchState({
                    "IntelligentSearch.useCallback[handleSearchChange]": (prev)=>({
                            ...prev,
                            results: [],
                            totalResults: 0,
                            showSuggestions: false
                        })
                }["IntelligentSearch.useCallback[handleSearchChange]"]);
                return;
            }
            // 延迟执行搜索 - 只有当输入长度>=2时才搜索
            if (value.trim().length >= 2) {
                searchTimeoutRef.current = setTimeout({
                    "IntelligentSearch.useCallback[handleSearchChange]": ()=>{
                        performSearch(value);
                    }
                }["IntelligentSearch.useCallback[handleSearchChange]"], 800); // 增加延迟到800ms，减少频繁搜索
            }
        }
    }["IntelligentSearch.useCallback[handleSearchChange]"], [
        performSearch
    ]);
    // 处理搜索提交
    const handleSearchSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleSearchSubmit]": (query)=>{
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
            performSearch(query);
        }
    }["IntelligentSearch.useCallback[handleSearchSubmit]"], [
        performSearch
    ]);
    // 处理建议选择
    const handleSuggestionSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleSuggestionSelect]": (suggestion)=>{
            const query = suggestion.text || suggestion.query;
            setSearchState({
                "IntelligentSearch.useCallback[handleSuggestionSelect]": (prev)=>({
                        ...prev,
                        query
                    })
            }["IntelligentSearch.useCallback[handleSuggestionSelect]"]);
            handleSearchSubmit(query);
        }
    }["IntelligentSearch.useCallback[handleSuggestionSelect]"], [
        handleSearchSubmit
    ]);
    // 关闭建议
    const handleCloseSuggestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleCloseSuggestions]": ()=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleCloseSuggestions]": (prev)=>({
                        ...prev,
                        showSuggestions: false
                    })
            }["IntelligentSearch.useCallback[handleCloseSuggestions]"]);
        }
    }["IntelligentSearch.useCallback[handleCloseSuggestions]"], []);
    // 关闭历史
    const handleCloseHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleCloseHistory]": ()=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleCloseHistory]": (prev)=>({
                        ...prev,
                        showHistory: false
                    })
            }["IntelligentSearch.useCallback[handleCloseHistory]"]);
        }
    }["IntelligentSearch.useCallback[handleCloseHistory]"], []);
    // 处理历史选择
    const handleHistorySelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleHistorySelect]": (query)=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleHistorySelect]": (prev)=>({
                        ...prev,
                        query
                    })
            }["IntelligentSearch.useCallback[handleHistorySelect]"]);
            handleSearchSubmit(query);
        }
    }["IntelligentSearch.useCallback[handleHistorySelect]"], [
        handleSearchSubmit
    ]);
    // 处理筛选器变化
    const handleFiltersChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleFiltersChange]": (filters)=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleFiltersChange]": (prev)=>{
                    const newState = {
                        ...prev,
                        filters
                    };
                    // 如果有查询，重新搜索
                    if (prev.query.trim()) {
                        // 使用setTimeout避免在setState中直接调用performSearch
                        setTimeout({
                            "IntelligentSearch.useCallback[handleFiltersChange]": ()=>{
                                performSearch(prev.query, filters);
                            }
                        }["IntelligentSearch.useCallback[handleFiltersChange]"], 0);
                    }
                    return newState;
                }
            }["IntelligentSearch.useCallback[handleFiltersChange]"]);
        }
    }["IntelligentSearch.useCallback[handleFiltersChange]"], [
        performSearch
    ]);
    // 清空搜索
    const handleClearSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[handleClearSearch]": ()=>{
            setSearchState({
                "IntelligentSearch.useCallback[handleClearSearch]": (prev)=>({
                        ...prev,
                        query: '',
                        results: [],
                        totalResults: 0,
                        showSuggestions: false,
                        showHistory: true,
                        error: undefined
                    })
            }["IntelligentSearch.useCallback[handleClearSearch]"]);
            searchInputRef.current?.focus();
        }
    }["IntelligentSearch.useCallback[handleClearSearch]"], []);
    // 切换筛选器显示
    const toggleFilters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "IntelligentSearch.useCallback[toggleFilters]": ()=>{
            setSearchState({
                "IntelligentSearch.useCallback[toggleFilters]": (prev)=>({
                        ...prev,
                        showFilters: !prev.showFilters
                    })
            }["IntelligentSearch.useCallback[toggleFilters]"]);
        }
    }["IntelligentSearch.useCallback[toggleFilters]"], []);
    // 组件挂载时自动聚焦
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "IntelligentSearch.useEffect": ()=>{
            if (autoFocus && searchInputRef.current) {
                searchInputRef.current.focus();
            }
        }
    }["IntelligentSearch.useEffect"], [
        autoFocus
    ]);
    // 清理定时器
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "IntelligentSearch.useEffect": ()=>{
            return ({
                "IntelligentSearch.useEffect": ()=>{
                    if (searchTimeoutRef.current) {
                        clearTimeout(searchTimeoutRef.current);
                    }
                }
            })["IntelligentSearch.useEffect"];
        }
    }["IntelligentSearch.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-full max-w-4xl mx-auto ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                    className: "h-5 w-5 text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                    lineNumber: 274,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 273,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                ref: searchInputRef,
                                type: "text",
                                value: searchState.query,
                                onChange: (e)=>handleSearchChange(e.target.value),
                                onKeyDown: (e)=>{
                                    if (e.key === 'Enter') {
                                        handleSearchSubmit(searchState.query);
                                    }
                                },
                                placeholder: placeholder,
                                className: "block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 277,
                                columnNumber: 11
                            }, this),
                            searchState.query && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleClearSearch,
                                className: "absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-5 w-5"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                    lineNumber: 297,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 292,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 272,
                        columnNumber: 9
                    }, this),
                    searchState.showSuggestions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-full left-0 right-0 z-20 mt-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchSuggestions$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            query: searchState.query,
                            onSelect: handleSuggestionSelect,
                            onClose: handleCloseSuggestions
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                            lineNumber: 305,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 304,
                        columnNumber: 11
                    }, this),
                    searchState.showHistory && showSearchHistory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-full left-0 right-0 z-20 mt-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            onSelect: handleHistorySelect,
                            onClose: handleCloseHistory,
                            maxItems: 8,
                            showStats: true
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                            lineNumber: 316,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 315,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                lineNumber: 271,
                columnNumber: 7
            }, this),
            showAdvancedFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$AdvancedSearchFilters$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    filters: searchState.filters,
                    onFiltersChange: handleFiltersChange,
                    categories: categories,
                    availableTags: availableTags,
                    isCollapsed: !searchState.showFilters,
                    onToggleCollapse: toggleFilters,
                    showResultCount: searchState.results.length > 0,
                    resultCount: searchState.totalResults
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                    lineNumber: 329,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                lineNumber: 328,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-6",
                children: [
                    searchState.isSearching && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 347,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-gray-600",
                                children: "搜索中..."
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 348,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 346,
                        columnNumber: 11
                    }, this),
                    searchState.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-red-50 border border-red-200 rounded-lg p-4 text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-red-600",
                            children: searchState.error
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                            lineNumber: 355,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 354,
                        columnNumber: 11
                    }, this),
                    !searchState.isSearching && !searchState.error && searchState.results.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-gray-900",
                                    children: [
                                        "搜索结果 (",
                                        searchState.totalResults,
                                        ")"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                    lineNumber: 363,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 362,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: searchState.results.map((result, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$SearchResultItem$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        item: result,
                                        query: searchState.query,
                                        showRelevanceScore: true,
                                        onBookmark: (id, bookmarked)=>{
                                            // TODO: 实现收藏功能
                                            console.log('Bookmark:', id, bookmarked);
                                        },
                                        onShare: (item)=>{
                                            // TODO: 实现分享功能
                                            console.log('Share:', item);
                                        }
                                    }, result.id || index, false, {
                                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                        lineNumber: 370,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 368,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 361,
                        columnNumber: 11
                    }, this),
                    !searchState.isSearching && !searchState.error && searchState.query && searchState.results.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                className: "mx-auto h-12 w-12 text-gray-300"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 392,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "mt-2 text-sm font-medium text-gray-900",
                                children: "未找到相关结果"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 393,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-gray-500",
                                children: "尝试使用不同的关键词或调整筛选条件"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                                lineNumber: 394,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                        lineNumber: 391,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
                lineNumber: 343,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/components/knowledge/IntelligentSearch.tsx",
        lineNumber: 269,
        columnNumber: 5
    }, this);
}
_s(IntelligentSearch, "LZkP2nXZDXIsTy4KWJCnkZvjhCU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$hooks$2f$useSearchHistory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchHistory"]
    ];
});
_c = IntelligentSearch;
var _c;
__turbopack_context__.k.register(_c, "IntelligentSearch");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/web-app/src/app/test-search/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TestSearchPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$IntelligentSearch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/knowledge/IntelligentSearch.tsx [app-client] (ecmascript)");
'use client';
;
;
function TestSearchPage() {
    // 模拟分类数据
    const categories = [
        {
            value: '1',
            label: 'MySQL基础',
            count: 25
        },
        {
            value: '2',
            label: '查询优化',
            count: 18
        },
        {
            value: '3',
            label: '索引设计',
            count: 22
        },
        {
            value: '4',
            label: '存储引擎',
            count: 15
        },
        {
            value: '5',
            label: '备份恢复',
            count: 12
        },
        {
            value: '6',
            label: '主从复制',
            count: 8
        },
        {
            value: '7',
            label: '性能监控',
            count: 10
        }
    ];
    // 模拟标签数据
    const availableTags = [
        {
            value: 'select',
            label: 'SELECT查询',
            count: 45
        },
        {
            value: 'index',
            label: '索引优化',
            count: 38
        },
        {
            value: 'performance',
            label: '性能调优',
            count: 32
        },
        {
            value: 'innodb',
            label: 'InnoDB',
            count: 28
        },
        {
            value: 'backup',
            label: '数据备份',
            count: 20
        },
        {
            value: 'replication',
            label: '主从复制',
            count: 15
        },
        {
            value: 'transaction',
            label: '事务处理',
            count: 25
        },
        {
            value: 'lock',
            label: '锁机制',
            count: 18
        },
        {
            value: 'join',
            label: 'JOIN连接',
            count: 22
        },
        {
            value: 'trigger',
            label: '触发器',
            count: 12
        },
        {
            value: 'procedure',
            label: '存储过程',
            count: 14
        },
        {
            value: 'view',
            label: '视图',
            count: 16
        },
        {
            value: 'partition',
            label: '分区表',
            count: 8
        },
        {
            value: 'cluster',
            label: '集群',
            count: 6
        }
    ];
    // 处理搜索结果选择
    const handleResultSelect = (result)=>{
        console.log('选择了搜索结果:', result);
    // 这里可以跳转到详情页面或执行其他操作
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white shadow-sm border-b border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold text-gray-900 mb-2",
                                children: "智能搜索系统测试"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600",
                                children: "测试和演示MySQLAi.de的智能搜索功能"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 54,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/app/test-search/page.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-semibold text-blue-900 mb-3",
                                children: "🚀 智能搜索功能特性"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 65,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-blue-800",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "🔍 实时搜索建议"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 70,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "输入2个字符即可获得智能建议，支持文章标题和热门搜索词"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 71,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 69,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "📊 智能排序算法"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 74,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "基于TF-IDF的相关性计算，多维度权重排序"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 75,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 73,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "🏷️ 高级筛选"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 78,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "支持分类、难度、标签、排序等多维度筛选"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 79,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 77,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "📝 搜索历史"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 82,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "本地存储搜索历史，支持最近搜索和热门搜索"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 83,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 81,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "⚡ 性能优化"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 86,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "TTL缓存、防抖处理、请求取消等性能优化"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 87,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 85,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-medium mb-2",
                                                children: "📱 响应式设计"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 90,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "完美适配桌面端和移动端，触摸友好"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 91,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 89,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg shadow-lg p-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$knowledge$2f$IntelligentSearch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            placeholder: "搜索MySQL知识点、教程、最佳实践...",
                            autoFocus: true,
                            showAdvancedFilters: true,
                            showSearchHistory: true,
                            maxResults: 20,
                            onResultSelect: handleResultSelect,
                            categories: categories,
                            availableTags: availableTags,
                            className: "w-full"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/app/test-search/page.tsx",
                            lineNumber: 98,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-semibold text-gray-900 mb-4",
                                children: "💡 使用说明"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-3 text-sm text-gray-600",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium",
                                                children: "1"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 118,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "在搜索框中输入关键词，系统会自动显示搜索建议和历史记录"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 119,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium",
                                                children: "2"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 122,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: '点击"高级筛选"可以按分类、难度、标签等条件筛选结果'
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 123,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 121,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium",
                                                children: "3"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 126,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "搜索历史会自动保存在本地，支持最近搜索和热门搜索两个标签页"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 127,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 125,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium",
                                                children: "4"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 130,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "搜索结果支持相关性排序、时间排序和标题排序"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 131,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 129,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium",
                                                children: "5"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 134,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "支持键盘导航：↑↓选择建议，回车确认，ESC关闭"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 135,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 133,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 116,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                        lineNumber: 112,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-8 grid grid-cols-1 md:grid-cols-2 gap-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold text-gray-900 mb-4",
                                        children: "🔧 技术实现"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "space-y-2 text-sm text-gray-600",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• React 19 + TypeScript + Tailwind CSS"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 147,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 智能防抖和缓存机制"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 148,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• TF-IDF相关性算法"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 149,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• localStorage本地存储"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 150,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 响应式设计和无障碍支持"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 151,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 组件化架构，易于维护"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 152,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 146,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold text-gray-900 mb-4",
                                        children: "📈 性能优化"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 157,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "space-y-2 text-sm text-gray-600",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 搜索结果缓存（5分钟TTL）"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 161,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 搜索建议缓存（2分钟TTL）"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 162,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 300ms防抖延迟"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 163,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 请求取消和超时处理"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 164,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 虚拟滚动（大量结果时）"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 165,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 懒加载和预加载策略"
                                            }, void 0, false, {
                                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                                lineNumber: 166,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                                lineNumber: 156,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/app/test-search/page.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/web-app/src/app/test-search/page.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/web-app/src/app/test-search/page.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_c = TestSearchPage;
var _c;
__turbopack_context__.k.register(_c, "TestSearchPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=web-app_src_72f11f87._.js.map