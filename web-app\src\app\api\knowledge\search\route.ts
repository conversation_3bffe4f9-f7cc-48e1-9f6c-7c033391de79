// MySQLAi.de - 知识库搜索 API
// 提供高级搜索功能和搜索统计

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 高亮文本函数
function highlightText(text: string, query: string): string {
  if (!query || !text) return text;

  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

// 计算文本相似度（简化版TF-IDF）
function calculateRelevanceScore(article: any, query: string): number {
  if (!query || !article) return 0;

  const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
  if (queryTerms.length === 0) return 0;

  let score = 0;
  const title = (article.title || '').toLowerCase();
  const content = (article.content || '').toLowerCase();
  const description = (article.description || '').toLowerCase();

  queryTerms.forEach(term => {
    // 标题匹配权重最高
    const titleMatches = (title.match(new RegExp(term, 'g')) || []).length;
    score += titleMatches * 10;

    // 描述匹配权重中等
    const descMatches = (description.match(new RegExp(term, 'g')) || []).length;
    score += descMatches * 5;

    // 内容匹配权重较低
    const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
    score += contentMatches * 2;

    // 完全匹配加分
    if (title.includes(term)) score += 20;
    if (description.includes(term)) score += 10;
  });

  // 标签匹配加分
  if (article.tags && Array.isArray(article.tags)) {
    queryTerms.forEach(term => {
      const tagMatches = article.tags.filter((tag: string) =>
        tag.toLowerCase().includes(term)
      ).length;
      score += tagMatches * 15;
    });
  }

  return score;
}

// 智能排序函数
function intelligentSort(articles: any[], query: string, sortBy: string, sortOrder: string): any[] {
  if (!articles || articles.length === 0) return articles;

  // 为每篇文章计算相关性分数
  const articlesWithScore = articles.map(article => ({
    ...article,
    relevanceScore: calculateRelevanceScore(article, query)
  }));

  // 根据排序方式进行排序
  return articlesWithScore.sort((a, b) => {
    switch (sortBy) {
      case 'relevance':
        // 相关性排序：分数高的在前，分数相同时按更新时间排序
        if (a.relevanceScore !== b.relevanceScore) {
          return sortOrder === 'asc' ? a.relevanceScore - b.relevanceScore : b.relevanceScore - a.relevanceScore;
        }
        return new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime();

      case 'date':
        const dateA = new Date(a.last_updated).getTime();
        const dateB = new Date(b.last_updated).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;

      case 'title':
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return sortOrder === 'asc' ? titleA.localeCompare(titleB) : titleB.localeCompare(titleA);

      default:
        // 默认按相关性排序
        return b.relevanceScore - a.relevanceScore;
    }
  });
}

// GET /api/knowledge/search - 高级搜索
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const query = searchParams.get('q') || searchParams.get('query');
    const category = searchParams.get('category');
    const tags = searchParams.get('tags');
    const difficulty = searchParams.get('difficulty');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'relevance'; // relevance, date, title
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!query) {
      return NextResponse.json(
        { success: false, error: '请提供搜索关键词' },
        { status: 400 }
      );
    }

    // 记录搜索历史
    const userAgent = request.headers.get('user-agent');
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip');

    // 构建搜索查询
    let searchQuery = supabase
      .from('knowledge_articles')
      .select(`
        *,
        knowledge_categories(id, name, icon, color)
      `);

    // 尝试全文搜索，如果失败则使用ilike搜索
    console.log('执行textSearch查询:', 'title,content', query);

    // 先尝试textSearch
    const textSearchQuery = supabase
      .from('knowledge_articles')
      .select(`
        *,
        knowledge_categories(id, name, icon, color)
      `)
      .textSearch('title,content', query);

    // 如果有其他筛选条件，也应用到textSearch
    if (category) {
      textSearchQuery.eq('category_id', category);
    }
    if (difficulty) {
      textSearchQuery.eq('difficulty', difficulty);
    }
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      textSearchQuery.overlaps('tags', tagArray);
    }

    const { data: textSearchResults, error: textSearchError } = await textSearchQuery;
    console.log('textSearch结果数量:', textSearchResults?.length || 0);
    console.log('textSearch错误:', textSearchError);

    // 如果textSearch有结果，使用它；否则使用ilike搜索
    let finalResults = textSearchResults;
    let finalError = textSearchError;

    if (!textSearchResults || textSearchResults.length === 0) {
      console.log('textSearch无结果，尝试ilike搜索...');

      // 使用ilike搜索作为备选方案
      let iLikeQuery = supabase
        .from('knowledge_articles')
        .select(`
          *,
          knowledge_categories(id, name, icon, color)
        `)
        .or(`title.ilike.%${query}%,content.ilike.%${query}%`);

      // 应用筛选条件
      if (category) {
        iLikeQuery = iLikeQuery.eq('category_id', category);
      }
      if (difficulty) {
        iLikeQuery = iLikeQuery.eq('difficulty', difficulty);
      }
      if (tags) {
        const tagArray = tags.split(',').map(tag => tag.trim());
        iLikeQuery = iLikeQuery.overlaps('tags', tagArray);
      }

      const { data: iLikeResults, error: iLikeError } = await iLikeQuery;
      console.log('ilike搜索结果数量:', iLikeResults?.length || 0);
      console.log('ilike搜索错误:', iLikeError);

      finalResults = iLikeResults;
      finalError = iLikeError;
    }

    // 更新原来的变量
    let allArticles = finalResults;
    let error = finalError;
    let count = finalResults?.length || 0;

    // 查询已在上面执行完成，筛选条件也已应用

    console.log('=== 搜索API调试信息 ===');
    console.log('查询关键词:', query);
    console.log('数据库查询结果数量:', allArticles?.length || 0);
    console.log('数据库错误:', error);
    console.log('第一条数据示例:', allArticles?.[0]);

    // 测试数据库连接 - 查询所有文章
    const { data: allData, error: allError } = await supabase
      .from('knowledge_articles')
      .select('*');
    console.log('数据库连接测试 - 所有文章数量:', allData?.length || 0);
    console.log('数据库连接测试 - 错误:', allError);
    console.log('数据库中的文章标题:', allData?.map(article => article.title) || []);
    console.log('第一篇文章完整数据:', allData?.[0]);

    if (error) {
      console.error('搜索失败:', error);
      return NextResponse.json(
        { success: false, error: '搜索失败', details: error.message },
        { status: 500 }
      );
    }

    // 使用智能排序算法对结果进行排序
    const sortedArticles = intelligentSort(allArticles || [], query, sortBy, sortOrder);

    console.log('排序后结果数量:', sortedArticles.length);
    console.log('排序后第一条数据:', sortedArticles[0]);

    // 手动分页
    const offset = (page - 1) * limit;
    const paginatedArticles = sortedArticles.slice(offset, offset + limit);

    console.log('分页后结果数量:', paginatedArticles.length);
    console.log('分页参数 - page:', page, 'limit:', limit, 'offset:', offset);

    // 异步记录搜索历史
    supabase
      .from('search_history')
      .insert({
        query,
        results_count: count || 0,
        ip_address: ip,
        user_agent: userAgent
      })
      .then(({ error }) => {
        if (error) console.error('记录搜索历史失败:', error);
      });

    // 为每个结果添加搜索高亮信息和相关性分数
    const resultsWithHighlight = paginatedArticles.map(article => ({
      ...article,
      highlight: {
        title: highlightText(article.title, query),
        description: highlightText(article.description || '', query)
      },
      // 添加相关性分数用于调试和展示
      relevanceScore: article.relevanceScore || 0
    }));

    console.log('最终返回数据数量:', resultsWithHighlight.length);
    console.log('最终返回数据示例:', resultsWithHighlight[0]);
    console.log('=== 搜索API调试信息结束 ===');

    return NextResponse.json({
      success: true,
      data: resultsWithHighlight,
      query: {
        text: query,
        category,
        tags,
        difficulty,
        sortBy,
        sortOrder
      },
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/search - 搜索建议
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, limit = 5 } = body;

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    // 并行搜索多种建议类型
    const [titleMatches, tagMatches, popularSearches] = await Promise.all([
      // 1. 搜索文章标题匹配
      supabase
        .from('knowledge_articles')
        .select('id, title, category_id, knowledge_categories(name)')
        .ilike('title', `%${query}%`)
        .limit(Math.ceil(limit * 0.6)), // 60%的建议来自标题匹配

      // 2. 搜索标签匹配
      supabase
        .from('knowledge_articles')
        .select('id, title, tags, category_id, knowledge_categories(name)')
        .contains('tags', [query])
        .limit(Math.ceil(limit * 0.3)), // 30%的建议来自标签匹配

      // 3. 搜索热门搜索词
      supabase
        .from('search_history')
        .select('query')
        .ilike('query', `%${query}%`)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // 最近30天
        .limit(Math.ceil(limit * 0.2)) // 20%的建议来自热门搜索
    ]);

    // 处理错误
    if (titleMatches.error) {
      console.error('搜索标题建议失败:', titleMatches.error);
    }
    if (tagMatches.error) {
      console.error('搜索标签建议失败:', tagMatches.error);
    }
    if (popularSearches.error) {
      console.error('获取热门搜索失败:', popularSearches.error);
    }

    // 构建建议列表
    const suggestions = [];

    // 添加标题匹配建议（优先级最高）
    if (titleMatches.data) {
      suggestions.push(...titleMatches.data.map(article => ({
        type: 'article' as const,
        text: article.title,
        id: article.id,
        category: (article.knowledge_categories as any)?.name || '未分类',
        priority: 3 // 最高优先级
      })));
    }

    // 添加标签匹配建议
    if (tagMatches.data) {
      suggestions.push(...tagMatches.data
        .filter(article => !suggestions.some(s => s.id === article.id)) // 去重
        .map(article => ({
          type: 'article' as const,
          text: article.title,
          id: article.id,
          category: (article.knowledge_categories as any)?.name || '未分类',
          priority: 2
        })));
    }

    // 添加热门搜索建议
    if (popularSearches.data) {
      suggestions.push(...popularSearches.data.map(search => ({
        type: 'query' as const,
        text: search.query,
        priority: 1
      })));
    }

    // 按优先级和相关性排序
    const sortedSuggestions = suggestions
      .sort((a, b) => {
        // 首先按优先级排序
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        // 然后按文本相似度排序（简单的字符串匹配）
        const aMatch = a.text.toLowerCase().indexOf(query.toLowerCase());
        const bMatch = b.text.toLowerCase().indexOf(query.toLowerCase());
        if (aMatch !== bMatch) {
          return aMatch - bMatch; // 匹配位置越靠前越好
        }
        return a.text.length - b.text.length; // 长度越短越好
      })
      .slice(0, limit)
      .map(({ priority, ...suggestion }) => suggestion); // 移除priority字段

    return NextResponse.json({
      success: true,
      data: sortedSuggestions,
      meta: {
        query,
        total: suggestions.length,
        limit
      }
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}


