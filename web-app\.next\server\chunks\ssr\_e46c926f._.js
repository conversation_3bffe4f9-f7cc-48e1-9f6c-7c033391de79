module.exports = {

"[project]/web-app/src/lib/legal.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// MySQLAi.de - 法律条款数据管理文件
// 包含服务条款、隐私政策、免责声明、Cookie政策的完整内容和管理系统
// 法律页面类型定义
__turbopack_context__.s({
    "COOKIES_CONTENT": (()=>COOKIES_CONTENT),
    "DISCLAIMER_CONTENT": (()=>DISCLAIMER_CONTENT),
    "LEGAL_CONTENTS": (()=>LEGAL_CONTENTS),
    "PRIVACY_CONTENT": (()=>PRIVACY_CONTENT),
    "TERMS_CONTENT": (()=>TERMS_CONTENT),
    "formatLastUpdated": (()=>formatLastUpdated),
    "getAllLegalPageTypes": (()=>getAllLegalPageTypes),
    "getLegalContent": (()=>getLegalContent),
    "getLegalNavigationLinks": (()=>getLegalNavigationLinks),
    "getLegalPageMeta": (()=>getLegalPageMeta),
    "isValidLegalPageType": (()=>isValidLegalPageType)
});
const TERMS_CONTENT = {
    type: 'terms',
    title: '服务条款',
    description: 'MySQLAi.de平台服务使用条款和用户协议',
    lastUpdated: '2025-06-28',
    version: '1.0',
    sections: [
        {
            id: 'acceptance',
            title: '1. 条款接受',
            content: '欢迎使用MySQLAi.de（以下简称"本平台"）提供的MySQL智能分析服务。通过访问或使用本平台的任何服务，您表示同意遵守本服务条款（以下简称"本条款"）。如果您不同意本条款的任何部分，请不要使用本平台的服务。'
        },
        {
            id: 'service-description',
            title: '2. 服务描述',
            content: 'MySQLAi.de是一个专业的MySQL数据库智能分析平台，为用户提供以下服务：',
            subsections: [
                {
                    id: 'service-mysql-analysis',
                    title: '2.1 MySQL智能分析',
                    content: '基于AI技术的数据库性能分析、优化建议和智能诊断服务。'
                },
                {
                    id: 'service-project-management',
                    title: '2.2 项目管理',
                    content: '提供数据库项目的任务管理、进度跟踪和团队协作功能。'
                },
                {
                    id: 'service-report-display',
                    title: '2.3 报告展示',
                    content: '支持多媒体格式的项目报告生成、展示和分享功能。'
                },
                {
                    id: 'service-technical-support',
                    title: '2.4 技术支持',
                    content: '7×24小时专业技术支持服务，包括在线咨询、远程协助等。'
                }
            ]
        },
        {
            id: 'user-obligations',
            title: '3. 用户义务',
            content: '使用本平台服务时，您需要遵守以下义务：',
            subsections: [
                {
                    id: 'legal-compliance',
                    title: '3.1 法律合规',
                    content: '遵守中华人民共和国相关法律法规，不得利用本平台从事违法活动。'
                },
                {
                    id: 'account-security',
                    title: '3.2 账户安全',
                    content: '妥善保管账户信息，对账户下的所有活动承担责任。'
                },
                {
                    id: 'data-accuracy',
                    title: '3.3 数据准确性',
                    content: '确保提供给本平台的数据信息真实、准确、完整。'
                },
                {
                    id: 'proper-usage',
                    title: '3.4 合理使用',
                    content: '合理使用平台资源，不得恶意攻击、滥用或干扰平台正常运行。'
                }
            ]
        },
        {
            id: 'intellectual-property',
            title: '4. 知识产权',
            content: '本平台的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。未经授权，不得复制、传播、展示、镜像、上传、下载使用。'
        },
        {
            id: 'privacy-protection',
            title: '5. 隐私保护',
            content: '我们重视用户隐私保护，具体的隐私保护措施请参阅《隐私政策》。我们承诺按照相关法律法规和本平台隐私政策处理用户个人信息。'
        },
        {
            id: 'service-availability',
            title: '6. 服务可用性',
            content: '我们努力确保服务的连续性和稳定性，但不保证服务不会中断。因系统维护、升级或不可抗力等原因导致的服务中断，我们将尽快恢复服务。'
        },
        {
            id: 'limitation-of-liability',
            title: '7. 责任限制',
            content: '在法律允许的最大范围内，本平台对因使用或无法使用本服务而导致的任何直接、间接、偶然、特殊或后果性损害不承担责任。'
        },
        {
            id: 'terms-modification',
            title: '8. 条款修改',
            content: '我们保留随时修改本条款的权利。修改后的条款将在本平台公布，继续使用本服务即表示您接受修改后的条款。'
        },
        {
            id: 'governing-law',
            title: '9. 适用法律',
            content: '本条款的解释和执行适用中华人民共和国法律。如发生争议，应通过友好协商解决；协商不成的，提交本平台所在地人民法院管辖。'
        },
        {
            id: 'contact-information',
            title: '10. 联系方式',
            content: '如您对本条款有任何疑问，请通过以下方式联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
        }
    ]
};
const PRIVACY_CONTENT = {
    type: 'privacy',
    title: '隐私政策',
    description: 'MySQLAi.de平台用户隐私保护政策和个人信息处理规则',
    lastUpdated: '2025-06-28',
    version: '1.0',
    sections: [
        {
            id: 'introduction',
            title: '1. 引言',
            content: 'MySQLAi.de（以下简称"我们"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'
        },
        {
            id: 'information-collection',
            title: '2. 我们收集的信息',
            content: '为了向您提供更好的服务，我们可能会收集以下类型的信息：',
            subsections: [
                {
                    id: 'account-information',
                    title: '2.1 账户信息',
                    content: '当您注册账户时，我们会收集您的用户名、邮箱地址、手机号码等基本信息。'
                },
                {
                    id: 'usage-information',
                    title: '2.2 使用信息',
                    content: '您使用我们服务时产生的信息，包括访问时间、使用功能、操作记录等。'
                },
                {
                    id: 'device-information',
                    title: '2.3 设备信息',
                    content: '您使用的设备信息，包括设备型号、操作系统、浏览器类型、IP地址等。'
                },
                {
                    id: 'database-information',
                    title: '2.4 数据库信息',
                    content: '为提供MySQL分析服务，我们可能需要访问您的数据库结构信息（不包含敏感业务数据）。'
                }
            ]
        },
        {
            id: 'information-usage',
            title: '3. 信息使用目的',
            content: '我们收集和使用您的个人信息主要用于以下目的：',
            subsections: [
                {
                    id: 'service-provision',
                    title: '3.1 服务提供',
                    content: '为您提供MySQL智能分析、项目管理、报告展示等核心服务。'
                },
                {
                    id: 'service-improvement',
                    title: '3.2 服务改进',
                    content: '分析用户使用习惯，优化产品功能和用户体验。'
                },
                {
                    id: 'security-protection',
                    title: '3.3 安全保护',
                    content: '保护您的账户安全，防范欺诈、滥用等风险。'
                },
                {
                    id: 'customer-support',
                    title: '3.4 客户支持',
                    content: '为您提供技术支持和客户服务。'
                }
            ]
        },
        {
            id: 'information-sharing',
            title: '4. 信息共享',
            content: '我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：',
            subsections: [
                {
                    id: 'legal-requirement',
                    title: '4.1 法律要求',
                    content: '根据法律法规、法律程序、政府要求或司法裁定需要披露。'
                },
                {
                    id: 'user-consent',
                    title: '4.2 用户同意',
                    content: '获得您的明确同意后，与第三方共享特定信息。'
                },
                {
                    id: 'service-providers',
                    title: '4.3 服务提供商',
                    content: '与我们的服务提供商共享必要信息，以便他们为我们提供服务。'
                }
            ]
        },
        {
            id: 'information-security',
            title: '5. 信息安全',
            content: '我们采用行业标准的安全措施保护您的个人信息：',
            subsections: [
                {
                    id: 'encryption',
                    title: '5.1 数据加密',
                    content: '使用SSL/TLS加密技术保护数据传输安全。'
                },
                {
                    id: 'access-control',
                    title: '5.2 访问控制',
                    content: '严格限制员工对个人信息的访问权限。'
                },
                {
                    id: 'security-monitoring',
                    title: '5.3 安全监控',
                    content: '建立完善的安全监控和应急响应机制。'
                }
            ]
        },
        {
            id: 'user-rights',
            title: '6. 您的权利',
            content: '您对自己的个人信息享有以下权利：',
            subsections: [
                {
                    id: 'access-right',
                    title: '6.1 知情权',
                    content: '您有权了解我们收集、使用您个人信息的情况。'
                },
                {
                    id: 'correction-right',
                    title: '6.2 更正权',
                    content: '您有权要求我们更正或补充您的个人信息。'
                },
                {
                    id: 'deletion-right',
                    title: '6.3 删除权',
                    content: '在特定情况下，您有权要求我们删除您的个人信息。'
                }
            ]
        },
        {
            id: 'policy-updates',
            title: '7. 政策更新',
            content: '我们可能会不时更新本隐私政策。更新后的政策将在本平台公布，并通过适当方式通知您。'
        },
        {
            id: 'contact-us',
            title: '8. 联系我们',
            content: '如您对本隐私政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
        }
    ]
};
const DISCLAIMER_CONTENT = {
    type: 'disclaimer',
    title: '免责声明',
    description: 'MySQLAi.de平台服务免责条款和责任限制说明',
    lastUpdated: '2025-06-28',
    version: '1.0',
    sections: [
        {
            id: 'general-disclaimer',
            title: '1. 一般免责',
            content: 'MySQLAi.de平台（以下简称"本平台"）提供的所有信息、建议和服务仅供参考，不构成任何形式的保证。用户使用本平台服务的风险由用户自行承担。'
        },
        {
            id: 'service-limitations',
            title: '2. 服务限制',
            content: '本平台的服务存在以下限制：',
            subsections: [
                {
                    id: 'analysis-accuracy',
                    title: '2.1 分析准确性',
                    content: 'MySQL智能分析结果基于算法和数据模型，可能存在误差，不保证100%准确。'
                },
                {
                    id: 'service-availability',
                    title: '2.2 服务可用性',
                    content: '服务可能因维护、升级、网络故障等原因暂时中断，我们不承担因此造成的损失。'
                },
                {
                    id: 'data-security',
                    title: '2.3 数据安全',
                    content: '虽然我们采取安全措施保护数据，但无法保证绝对安全，不承担因数据泄露造成的损失。'
                }
            ]
        },
        {
            id: 'user-responsibility',
            title: '3. 用户责任',
            content: '用户在使用本平台服务时应承担以下责任：',
            subsections: [
                {
                    id: 'data-backup',
                    title: '3.1 数据备份',
                    content: '用户应自行备份重要数据，本平台不承担数据丢失的责任。'
                },
                {
                    id: 'decision-making',
                    title: '3.2 决策责任',
                    content: '基于本平台分析结果做出的业务决策，责任由用户自行承担。'
                },
                {
                    id: 'compliance',
                    title: '3.3 合规使用',
                    content: '用户应确保使用本平台服务符合相关法律法规要求。'
                }
            ]
        },
        {
            id: 'third-party-services',
            title: '4. 第三方服务',
            content: '本平台可能包含第三方服务链接，对于第三方服务的内容、隐私政策或做法，我们不承担任何责任。'
        },
        {
            id: 'liability-limitation',
            title: '5. 责任限制',
            content: '在法律允许的最大范围内，本平台对任何直接、间接、偶然、特殊、后果性或惩罚性损害不承担责任，包括但不限于利润损失、数据丢失、业务中断等。'
        },
        {
            id: 'indemnification',
            title: '6. 赔偿',
            content: '用户同意就因违反本免责声明或使用本平台服务而产生的任何索赔、损失或费用，向本平台提供赔偿和保护。'
        },
        {
            id: 'governing-law',
            title: '7. 适用法律',
            content: '本免责声明受中华人民共和国法律管辖。任何争议应通过友好协商解决，协商不成的提交本平台所在地人民法院管辖。'
        }
    ]
};
const COOKIES_CONTENT = {
    type: 'cookies',
    title: 'Cookie政策',
    description: 'MySQLAi.de平台Cookie使用说明和管理指南',
    lastUpdated: '2025-06-28',
    version: '1.0',
    sections: [
        {
            id: 'what-are-cookies',
            title: '1. 什么是Cookie',
            content: 'Cookie是网站存储在您设备上的小型文本文件，用于记住您的偏好设置和改善您的浏览体验。Cookie不会损害您的设备或文件。'
        },
        {
            id: 'cookie-types',
            title: '2. Cookie类型',
            content: '我们使用以下类型的Cookie：',
            subsections: [
                {
                    id: 'essential-cookies',
                    title: '2.1 必要Cookie',
                    content: '这些Cookie对网站正常运行是必需的，包括用户身份验证、安全防护等功能。'
                },
                {
                    id: 'functional-cookies',
                    title: '2.2 功能Cookie',
                    content: '这些Cookie用于记住您的偏好设置，如语言选择、主题设置等，以提供个性化体验。'
                },
                {
                    id: 'analytics-cookies',
                    title: '2.3 分析Cookie',
                    content: '这些Cookie帮助我们了解用户如何使用网站，以便改进网站性能和用户体验。'
                },
                {
                    id: 'marketing-cookies',
                    title: '2.4 营销Cookie',
                    content: '这些Cookie用于跟踪用户在网站上的活动，以便提供相关的广告和营销内容。'
                }
            ]
        },
        {
            id: 'cookie-usage',
            title: '3. Cookie使用目的',
            content: '我们使用Cookie的主要目的包括：',
            subsections: [
                {
                    id: 'user-authentication',
                    title: '3.1 用户认证',
                    content: '保持您的登录状态，确保账户安全。'
                },
                {
                    id: 'preference-storage',
                    title: '3.2 偏好存储',
                    content: '记住您的设置和偏好，提供个性化服务。'
                },
                {
                    id: 'performance-analysis',
                    title: '3.3 性能分析',
                    content: '分析网站使用情况，优化网站性能。'
                },
                {
                    id: 'security-protection',
                    title: '3.4 安全保护',
                    content: '防范恶意攻击，保护网站和用户安全。'
                }
            ]
        },
        {
            id: 'cookie-management',
            title: '4. Cookie管理',
            content: '您可以通过以下方式管理Cookie：',
            subsections: [
                {
                    id: 'browser-settings',
                    title: '4.1 浏览器设置',
                    content: '大多数浏览器允许您控制Cookie设置，包括接受、拒绝或删除Cookie。'
                },
                {
                    id: 'platform-settings',
                    title: '4.2 平台设置',
                    content: '您可以在账户设置中管理某些Cookie偏好。'
                },
                {
                    id: 'opt-out',
                    title: '4.3 退出选择',
                    content: '您可以选择退出某些非必要Cookie，但这可能影响网站功能。'
                }
            ]
        },
        {
            id: 'third-party-cookies',
            title: '5. 第三方Cookie',
            content: '我们的网站可能包含第三方服务提供商设置的Cookie，如分析工具、社交媒体插件等。这些第三方Cookie受其各自隐私政策约束。'
        },
        {
            id: 'policy-updates',
            title: '6. 政策更新',
            content: '我们可能会不时更新本Cookie政策。更新后的政策将在网站上公布，建议您定期查看。'
        },
        {
            id: 'contact-information',
            title: '7. 联系我们',
            content: '如您对本Cookie政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
        }
    ]
};
const LEGAL_CONTENTS = {
    terms: TERMS_CONTENT,
    privacy: PRIVACY_CONTENT,
    disclaimer: DISCLAIMER_CONTENT,
    cookies: COOKIES_CONTENT
};
function getLegalContent(type) {
    return LEGAL_CONTENTS[type];
}
function getLegalPageMeta(type) {
    const content = getLegalContent(type);
    return {
        type: content.type,
        title: content.title,
        description: content.description,
        keywords: generateKeywords(type),
        lastUpdated: content.lastUpdated,
        version: content.version
    };
}
// 工具函数：生成法律页面关键词
function generateKeywords(type) {
    const baseKeywords = [
        'MySQLAi.de',
        'MySQL',
        '数据库',
        '法律声明'
    ];
    const typeKeywords = {
        terms: [
            '服务条款',
            '用户协议',
            '使用条款',
            '服务协议'
        ],
        privacy: [
            '隐私政策',
            '个人信息保护',
            '数据保护',
            '隐私保护'
        ],
        disclaimer: [
            '免责声明',
            '责任限制',
            '法律免责',
            '服务限制'
        ],
        cookies: [
            'Cookie政策',
            'Cookie使用',
            '网站Cookie',
            'Cookie管理'
        ]
    };
    return [
        ...baseKeywords,
        ...typeKeywords[type]
    ];
}
function getAllLegalPageTypes() {
    return [
        'terms',
        'privacy',
        'disclaimer',
        'cookies'
    ];
}
function getLegalNavigationLinks(currentType) {
    return getAllLegalPageTypes().filter((type)=>type !== currentType).map((type)=>{
        const meta = getLegalPageMeta(type);
        return {
            type,
            title: meta.title,
            href: `/${type}`,
            description: meta.description
        };
    });
}
function formatLastUpdated(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function isValidLegalPageType(type) {
    return getAllLegalPageTypes().includes(type);
}
}}),
"[project]/web-app/src/components/ui/Breadcrumb.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - Breadcrumb面包屑导航组件
// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-ssr] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$navigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/navigation.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
const Breadcrumb = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(({ pathname, maxItems = 5, className, ...props })=>{
    // 获取面包屑数据
    const breadcrumbs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$navigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getBreadcrumbs"])(pathname), [
        pathname
    ]);
    // 如果只有首页，不显示面包屑
    if (breadcrumbs.length <= 1) {
        return null;
    }
    // 处理超长路径的截断
    const displayBreadcrumbs = breadcrumbs.length > maxItems ? [
        breadcrumbs[0],
        {
            name: '...',
            href: '#',
            isEllipsis: true
        },
        ...breadcrumbs.slice(-2) // 最后两项
    ] : breadcrumbs;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].nav, {
        initial: {
            opacity: 0,
            y: -10
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.3,
            ease: 'easeOut'
        },
        "aria-label": "面包屑导航",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center space-x-1 text-sm', 'py-2 px-1', className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
            className: "flex items-center space-x-1 md:space-x-2",
            children: displayBreadcrumbs.map((item, index)=>{
                const isLast = index === displayBreadcrumbs.length - 1;
                const isEllipsis = 'isEllipsis' in item && item.isEllipsis;
                const isHome = item.href === '/';
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "flex items-center",
                    children: [
                        index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                            className: "w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0",
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                            lineNumber: 60,
                            columnNumber: 17
                        }, this),
                        isEllipsis ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-gray-400 px-1",
                            children: "..."
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                            lineNumber: 68,
                            columnNumber: 17
                        }, this) : isLast ? // 当前页面（不可点击）
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('font-medium text-mysql-primary', 'px-2 py-1 rounded-md', 'bg-mysql-primary/5', 'flex items-center space-x-1'),
                            "aria-current": "page",
                            children: [
                                isHome && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"], {
                                    className: "w-3 h-3 md:w-4 md:h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                                    lineNumber: 80,
                                    columnNumber: 30
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('truncate max-w-[80px] md:max-w-[120px]', isHome && 'hidden md:inline'),
                                    children: item.name
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                                    lineNumber: 81,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                            lineNumber: 71,
                            columnNumber: 17
                        }, this) : // 可点击的面包屑项
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                            whileHover: {
                                scale: 1.05
                            },
                            whileTap: {
                                scale: 0.95
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: item.href,
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-gray-600 hover:text-mysql-primary', 'px-2 py-1 rounded-md', 'hover:bg-mysql-primary/5', 'transition-all duration-200', 'flex items-center space-x-1', 'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'),
                                children: [
                                    isHome && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"], {
                                        className: "w-3 h-3 md:w-4 md:h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                                        lineNumber: 105,
                                        columnNumber: 32
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('truncate max-w-[80px] md:max-w-[120px]', isHome && 'hidden md:inline'),
                                        children: item.name
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                                        lineNumber: 106,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                                lineNumber: 94,
                                columnNumber: 19
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                            lineNumber: 90,
                            columnNumber: 17
                        }, this)
                    ]
                }, `${item.href}-${index}`, true, {
                    fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
                    lineNumber: 57,
                    columnNumber: 13
                }, this);
            })
        }, void 0, false, {
            fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/ui/Breadcrumb.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
});
Breadcrumb.displayName = 'Breadcrumb';
const __TURBOPACK__default__export__ = Breadcrumb;
}}),
"[project]/web-app/src/components/layout/LegalPageLayout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// MySQLAi.de - LegalPageLayout法律页面布局组件
// 为法律声明页面提供统一的布局结构和视觉风格
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-ssr] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-ssr] (ecmascript) <export default as Scale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$legal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/lib/legal.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$Breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/web-app/src/components/ui/Breadcrumb.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
const LegalPageLayout = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(({ type, title, lastUpdated, children, pathname, className, ...props })=>{
    // 获取其他法律页面的导航链接
    const navigationLinks = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$legal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLegalNavigationLinks"])(type), [
        type
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('min-h-screen bg-gradient-to-br from-gray-50 to-white', className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$components$2f$ui$2f$Breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        pathname: pathname
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 47,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    initial: {
                        opacity: 0,
                        x: -20
                    },
                    animate: {
                        opacity: 1,
                        x: 0
                    },
                    transition: {
                        delay: 0.1,
                        duration: 0.3
                    },
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/",
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('inline-flex items-center space-x-2', 'text-mysql-primary hover:text-mysql-primary-dark', 'transition-colors duration-200', 'group'),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                className: "w-4 h-4 transition-transform group-hover:-translate-x-1"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium",
                                children: "返回首页"
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                lineNumber: 68,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].header, {
                    initial: {
                        opacity: 0,
                        y: 20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    transition: {
                        delay: 0.2,
                        duration: 0.4
                    },
                    className: "mb-12 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center mb-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('w-16 h-16 rounded-full', 'bg-mysql-primary/10 flex items-center justify-center', 'border-2 border-mysql-primary/20'),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__["Scale"], {
                                    className: "w-8 h-8 text-mysql-primary"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                    lineNumber: 86,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                lineNumber: 81,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-3xl md:text-4xl lg:text-5xl font-bold', 'text-gray-900 mb-4', 'leading-tight'),
                            children: title
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 91,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-center space-x-2 text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                    className: "w-4 h-4"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm",
                                    children: [
                                        "最后更新：",
                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$legal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatLastUpdated"])(lastUpdated)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].main, {
                    initial: {
                        opacity: 0,
                        y: 20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    transition: {
                        delay: 0.3,
                        duration: 0.4
                    },
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('bg-white rounded-xl shadow-lg', 'border border-gray-200', 'p-6 md:p-8 lg:p-12', 'mb-12'),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('prose prose-lg max-w-none', 'prose-headings:text-gray-900 prose-headings:font-semibold', 'prose-p:text-gray-700 prose-p:leading-relaxed', 'prose-a:text-mysql-primary prose-a:no-underline hover:prose-a:underline', 'prose-strong:text-gray-900', 'prose-ul:text-gray-700 prose-ol:text-gray-700', 'prose-li:marker:text-mysql-primary'),
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                        lineNumber: 120,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 109,
                    columnNumber: 9
                }, this),
                navigationLinks.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].section, {
                    initial: {
                        opacity: 0,
                        y: 20
                    },
                    animate: {
                        opacity: 1,
                        y: 0
                    },
                    transition: {
                        delay: 0.4,
                        duration: 0.4
                    },
                    className: "mb-12",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-gray-900 mb-6",
                            children: "其他法律声明"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: navigationLinks.map((link)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                                    whileHover: {
                                        scale: 1.02,
                                        y: -2
                                    },
                                    whileTap: {
                                        scale: 0.98
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: link.href,
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('block p-4 rounded-lg border border-gray-200', 'bg-white hover:bg-gray-50', 'transition-all duration-200', 'group'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "font-medium text-gray-900 group-hover:text-mysql-primary",
                                                            children: link.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                                            lineNumber: 162,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 mt-1",
                                                            children: link.description
                                                        }, void 0, false, {
                                                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                                            lineNumber: 165,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                    className: "w-5 h-5 text-gray-400 group-hover:text-mysql-primary transition-colors"
                                                }, void 0, false, {
                                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                            lineNumber: 160,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                        lineNumber: 151,
                                        columnNumber: 19
                                    }, this)
                                }, link.type, false, {
                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                    lineNumber: 146,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 135,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].footer, {
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: 1
                    },
                    transition: {
                        delay: 0.5,
                        duration: 0.4
                    },
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$web$2d$app$2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-center py-8 px-6', 'bg-gray-50 rounded-lg', 'border border-gray-200'),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-sm leading-relaxed",
                            children: [
                                "如您对本",
                                title,
                                "有任何疑问，请通过",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/contact",
                                    className: "text-mysql-primary hover:text-mysql-primary-dark mx-1",
                                    children: "联系我们"
                                }, void 0, false, {
                                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this),
                                "页面与我们取得联系。"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 189,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 text-xs mt-2",
                            children: "MySQLAi.de 致力于为用户提供透明、公正的服务条款"
                        }, void 0, false, {
                            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                            lineNumber: 199,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
                    lineNumber: 179,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
            lineNumber: 44,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/web-app/src/components/layout/LegalPageLayout.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
});
LegalPageLayout.displayName = 'LegalPageLayout';
const __TURBOPACK__default__export__ = LegalPageLayout;
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ArrowLeft)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m12 19-7-7 7-7",
            key: "1l729n"
        }
    ],
    [
        "path",
        {
            d: "M19 12H5",
            key: "x3x0zl"
        }
    ]
];
const ArrowLeft = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("arrow-left", __iconNode);
;
 //# sourceMappingURL=arrow-left.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-ssr] (ecmascript) <export default as ArrowLeft>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ArrowLeft": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Scale)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",
            key: "7g6ntu"
        }
    ],
    [
        "path",
        {
            d: "m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",
            key: "ijws7r"
        }
    ],
    [
        "path",
        {
            d: "M7 21h10",
            key: "1b0cd5"
        }
    ],
    [
        "path",
        {
            d: "M12 3v18",
            key: "108xh3"
        }
    ],
    [
        "path",
        {
            d: "M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",
            key: "3gwbw2"
        }
    ]
];
const Scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("scale", __iconNode);
;
 //# sourceMappingURL=scale.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-ssr] (ecmascript) <export default as Scale>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Scale": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Clock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M12 6v6l4 2",
            key: "mmk7yg"
        }
    ],
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "10",
            key: "1mglay"
        }
    ]
];
const Clock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("clock", __iconNode);
;
 //# sourceMappingURL=clock.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Clock": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>House)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",
            key: "5wwlr5"
        }
    ],
    [
        "path",
        {
            d: "M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",
            key: "1d0kgt"
        }
    ]
];
const House = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("house", __iconNode);
;
 //# sourceMappingURL=house.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-ssr] (ecmascript) <export default as Home>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Home": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-ssr] (ecmascript)");
}}),

};

//# sourceMappingURL=_e46c926f._.js.map