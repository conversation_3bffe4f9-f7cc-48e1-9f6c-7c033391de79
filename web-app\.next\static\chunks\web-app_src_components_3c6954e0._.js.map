{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/KnowledgeCard.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - KnowledgeCard知识点卡片组件\n// 基于FeatureCard组件，专门用于展示MySQL知识点信息\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  BookOpen, \n  Clock, \n  Star, \n  ArrowRight, \n  Calendar,\n  Tag,\n  TrendingUp,\n  Database,\n  Code,\n  Settings\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { KnowledgeCardProps, KnowledgeItem } from '@/lib/types';\n\n// 难度等级配置\nconst difficultyConfig = {\n  beginner: {\n    label: '初级',\n    color: 'bg-green-100 text-green-700 border-green-200',\n    icon: '🟢'\n  },\n  intermediate: {\n    label: '中级', \n    color: 'bg-yellow-100 text-yellow-700 border-yellow-200',\n    icon: '🟡'\n  },\n  advanced: {\n    label: '高级',\n    color: 'bg-red-100 text-red-700 border-red-200', \n    icon: '🔴'\n  }\n};\n\n// 分类图标映射\nconst categoryIcons = {\n  'basics': Database,\n  'database-operations': Settings,\n  'table-operations': Code,\n  'data-operations': BookOpen,\n  'advanced-queries': TrendingUp,\n  'management': Settings\n};\n\n// 估算阅读时间（基于内容长度）\nconst estimateReadingTime = (content: string): number => {\n  const wordsPerMinute = 200; // 中文阅读速度约200字/分钟\n  const wordCount = content.length;\n  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));\n};\n\n// 格式化更新时间\nconst formatUpdateTime = (dateString: string): string => {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffTime = Math.abs(now.getTime() - date.getTime());\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  \n  if (diffDays === 1) return '今天更新';\n  if (diffDays <= 7) return `${diffDays}天前更新`;\n  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前更新`;\n  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\n};\n\nconst KnowledgeCard = React.forwardRef<HTMLDivElement, KnowledgeCardProps>(({\n  item,\n  displayMode = 'grid',\n  onClick,\n  className,\n  ...props\n}, ref) => {\n  const difficulty = difficultyConfig[item.difficulty];\n  const CategoryIcon = categoryIcons[(item.category_id || '') as keyof typeof categoryIcons] || BookOpen;\n  const readingTime = estimateReadingTime(item.content);\n  const updateTime = formatUpdateTime(item.last_updated);\n\n  // 网格模式（详细显示）- grid模式和list模式支持\n  if (displayMode === 'grid') {\n    return (\n      <motion.div\n        ref={ref}\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.4, ease: \"easeOut\" }}\n        viewport={{ once: true }}\n        className=\"group h-full\"\n        {...props}\n      >\n        <div\n          className={cn(\n            'relative bg-white rounded-2xl shadow-lg border border-mysql-border h-full',\n            'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',\n            'cursor-pointer overflow-hidden',\n            'hover:ring-2 hover:ring-mysql-primary/20',\n            className\n          )}\n          onClick={(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            onClick?.();\n          }}\n          data-testid=\"knowledge-card\"\n        >\n          {/* 顶部渐变装饰 */}\n          <div className=\"absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-mysql-primary to-mysql-accent\" />\n\n          <div className=\"p-6 h-full flex flex-col\">\n            {/* 头部：图标、标题和难度 */}\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex items-center flex-1 min-w-0\">\n                <div className=\"flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-mysql-primary to-mysql-accent shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0\">\n                  <CategoryIcon className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"ml-3 flex-1 min-w-0\">\n                  <h3 className=\"text-lg font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 line-clamp-2\">\n                    {item.title}\n                  </h3>\n                </div>\n              </div>\n              <div className={cn(\n                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',\n                difficulty.color\n              )}>\n                <span className=\"mr-1\">{difficulty.icon}</span>\n                {difficulty.label}\n              </div>\n            </div>\n\n            {/* 描述 */}\n            <p className=\"text-mysql-text-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3\">\n              {item.description}\n            </p>\n\n            {/* 标签云 */}\n            {item.tags && item.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-4\">\n                {item.tags.slice(0, 3).map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-2 py-1 text-xs bg-mysql-primary-light text-mysql-primary rounded-md\"\n                  >\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {tag}\n                  </span>\n                ))}\n                {item.tags.length > 3 && (\n                  <span className=\"text-xs text-mysql-text-light\">\n                    +{item.tags.length - 3}\n                  </span>\n                )}\n              </div>\n            )}\n\n            {/* 底部信息 */}\n            <div className=\"mt-auto pt-4 border-t border-mysql-border\">\n              <div className=\"flex items-center justify-between text-xs text-mysql-text-light mb-3\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-4 h-4 mr-1\" />\n                  <span>{readingTime}分钟阅读</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <Calendar className=\"w-4 h-4 mr-1\" />\n                  <span>{updateTime}</span>\n                </div>\n              </div>\n              \n              {/* 查看详情按钮 */}\n              <motion.div\n                className=\"flex items-center justify-between text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"text-sm font-medium\">查看详情</span>\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.div>\n            </div>\n          </div>\n\n          {/* 悬停光效 */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\" />\n        </div>\n      </motion.div>\n    );\n  }\n\n  // 列表模式（紧凑显示）\n  return (\n    <motion.div\n      ref={ref}\n      initial={{ opacity: 0, x: -20 }}\n      whileInView={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.3, ease: \"easeOut\" }}\n      viewport={{ once: true }}\n      className=\"group\"\n      {...props}\n    >\n      <div\n        className={cn(\n          'bg-white rounded-xl p-4 shadow-md border border-mysql-border',\n          'hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out',\n          'cursor-pointer hover:ring-2 hover:ring-mysql-primary/20',\n          className\n        )}\n        onClick={onClick}\n        data-testid=\"knowledge-card\"\n      >\n        <div className=\"flex items-center space-x-4\">\n          {/* 图标 */}\n          <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-mysql-primary to-mysql-accent text-white flex-shrink-0\">\n            <CategoryIcon className=\"w-5 h-5\" />\n          </div>\n\n          {/* 内容 */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center justify-between mb-1\">\n              <h4 className=\"text-base font-semibold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300 truncate\">\n                {item.title}\n              </h4>\n              <div className={cn(\n                'px-2 py-1 text-xs font-medium rounded-full border flex-shrink-0 ml-2',\n                difficulty.color\n              )}>\n                {difficulty.label}\n              </div>\n            </div>\n            \n            <p className=\"text-mysql-text-light text-sm line-clamp-2 mb-2\">\n              {item.description}\n            </p>\n\n            <div className=\"flex items-center justify-between text-xs text-mysql-text-light\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-3 h-3 mr-1\" />\n                  <span>{readingTime}分钟</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <Tag className=\"w-3 h-3 mr-1\" />\n                  <span>{item.tags?.length || 0}个标签</span>\n                </div>\n              </div>\n              <span>{updateTime}</span>\n            </div>\n          </div>\n\n          {/* 箭头 */}\n          <ArrowRight className=\"w-5 h-5 text-mysql-text-light group-hover:text-mysql-primary transition-colors duration-300 flex-shrink-0\" />\n        </div>\n      </div>\n    </motion.div>\n  );\n});\n\nKnowledgeCard.displayName = 'KnowledgeCard';\n\nexport default KnowledgeCard;\n"], "names": [], "mappings": ";;;;AAEA,oCAAoC;AACpC,mCAAmC;AAEnC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;AAsBA,SAAS;AACT,MAAM,mBAAmB;IACvB,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA,cAAc;QACZ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;IACR;AACF;AAEA,SAAS;AACT,MAAM,gBAAgB;IACpB,UAAU,6MAAA,CAAA,WAAQ;IAClB,uBAAuB,6MAAA,CAAA,WAAQ;IAC/B,oBAAoB,qMAAA,CAAA,OAAI;IACxB,mBAAmB,iNAAA,CAAA,WAAQ;IAC3B,oBAAoB,qNAAA,CAAA,aAAU;IAC9B,cAAc,6MAAA,CAAA,WAAQ;AACxB;AAEA,iBAAiB;AACjB,MAAM,sBAAsB,CAAC;IAC3B,MAAM,iBAAiB,KAAK,iBAAiB;IAC7C,MAAM,YAAY,QAAQ,MAAM;IAChC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,YAAY;AAC3C;AAEA,UAAU;AACV,MAAM,mBAAmB,CAAC;IACxB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;IACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE1D,IAAI,aAAa,GAAG,OAAO;IAC3B,IAAI,YAAY,GAAG,OAAO,GAAG,SAAS,IAAI,CAAC;IAC3C,IAAI,YAAY,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC3D,OAAO,KAAK,kBAAkB,CAAC,SAAS;QAAE,OAAO;QAAS,KAAK;IAAU;AAC3E;AAEA,MAAM,8BAAgB,6JAAA,CAAA,UAAK,CAAC,UAAU,MAAqC,CAAC,EAC1E,IAAI,EACJ,cAAc,MAAM,EACpB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,gBAAgB,CAAC,KAAK,UAAU,CAAC;IACpD,MAAM,eAAe,aAAa,CAAE,KAAK,WAAW,IAAI,GAAkC,IAAI,iNAAA,CAAA,WAAQ;IACtG,MAAM,cAAc,oBAAoB,KAAK,OAAO;IACpD,MAAM,aAAa,iBAAiB,KAAK,YAAY;IAErD,8BAA8B;IAC9B,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,UAAU;gBAAE,MAAM;YAAK;YACvB,WAAU;YACT,GAAG,KAAK;sBAET,cAAA,6LAAC;gBACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,6EACA,yEACA,kCACA,4CACA;gBAEF,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB;gBACF;gBACA,eAAY;;kCAGZ,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAa,WAAU;;;;;;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACf,wEACA,WAAW,KAAK;;0DAEhB,6LAAC;gDAAK,WAAU;0DAAQ,WAAW,IAAI;;;;;;4CACtC,WAAW,KAAK;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;4BAIlB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;oCAOR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;wCAAK,WAAU;;4CAAgC;4CAC5C,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;0CAO7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;4DAAM;4DAAY;;;;;;;;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAM;;;;;;;;;;;;;;;;;;kDAKX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,aAAa;IACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,gEACA,2EACA,2DACA;YAEF,SAAS;YACT,eAAY;sBAEZ,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAa,WAAU;;;;;;;;;;;kCAI1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACf,wEACA,WAAW,KAAK;kDAEf,WAAW,KAAK;;;;;;;;;;;;0CAIrB,6LAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;4DAAM;4DAAY;;;;;;;;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;;4DAAM,KAAK,IAAI,EAAE,UAAU;4DAAE;;;;;;;;;;;;;;;;;;;kDAGlC,6LAAC;kDAAM;;;;;;;;;;;;;;;;;;kCAKX,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKhC;;AAEA,cAAc,WAAW,GAAG;uCAEb", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 智能搜索建议组件\n// 提供实时搜索建议、键盘导航、搜索历史显示功能\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { searchApi } from '@/lib/api/knowledge';\nimport { ChevronRight, Clock, TrendingUp, FileText } from 'lucide-react';\n\n// 建议项类型\ninterface SuggestionItem {\n  type: 'article' | 'query' | 'history';\n  text: string;\n  id?: string;\n  category?: string;\n  icon?: React.ReactNode;\n}\n\n// 组件属性\ninterface SearchSuggestionsProps {\n  query: string;\n  isVisible: boolean;\n  onSelect: (suggestion: SuggestionItem) => void;\n  onClose: () => void;\n  searchHistory?: string[];\n  className?: string;\n  maxSuggestions?: number;\n  showHistory?: boolean;\n  showCategories?: boolean;\n}\n\nexport default function SearchSuggestions({\n  query,\n  isVisible,\n  onSelect,\n  onClose,\n  searchHistory = [],\n  className = '',\n  maxSuggestions = 8,\n  showHistory = true,\n  showCategories = true\n}: SearchSuggestionsProps) {\n  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const [error, setError] = useState<string | null>(null);\n  \n  const containerRef = useRef<HTMLDivElement>(null);\n  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);\n\n  // 使用ref存储最新值，避免useEffect依赖问题\n  const suggestionsRef = useRef(suggestions);\n  const selectedIndexRef = useRef(selectedIndex);\n\n  // 更新ref值\n  suggestionsRef.current = suggestions;\n  selectedIndexRef.current = selectedIndex;\n\n  // 获取搜索建议\n  const fetchSuggestions = useCallback(async (searchQuery: string) => {\n    if (!searchQuery || searchQuery.length < 2) {\n      setSuggestions([]);\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await searchApi.getSuggestions(searchQuery, maxSuggestions - 2);\n\n      if (response.success && response.data) {\n        const apiSuggestions: SuggestionItem[] = response.data.map(item => ({\n          ...item,\n          icon: item.type === 'article' ? <FileText className=\"w-4 h-4\" /> : <TrendingUp className=\"w-4 h-4\" />\n        }));\n\n        // 添加搜索历史建议 - 使用当前的props值而不是依赖\n        const historySuggestions: SuggestionItem[] = showHistory\n          ? searchHistory\n              .filter(historyItem =>\n                historyItem.toLowerCase().includes(searchQuery.toLowerCase()) &&\n                historyItem !== searchQuery\n              )\n              .slice(0, 2)\n              .map(historyItem => ({\n                type: 'history' as const,\n                text: historyItem,\n                icon: <Clock className=\"w-4 h-4\" />\n              }))\n          : [];\n\n        // 合并建议并去重\n        const allSuggestions = [...historySuggestions, ...apiSuggestions];\n        const uniqueSuggestions = allSuggestions.filter((suggestion, index, self) =>\n          index === self.findIndex(s => s.text === suggestion.text)\n        );\n\n        setSuggestions(uniqueSuggestions.slice(0, maxSuggestions));\n      } else {\n        setError(response.error || '获取建议失败');\n        setSuggestions([]);\n      }\n    } catch (err) {\n      console.error('获取搜索建议失败:', err);\n      setError('网络错误，请稍后重试');\n      setSuggestions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [maxSuggestions]); // 只保留稳定的依赖\n\n  // 监听查询变化\n  useEffect(() => {\n    if (isVisible && query) {\n      fetchSuggestions(query);\n    } else {\n      setSuggestions([]);\n      setSelectedIndex(-1);\n    }\n  }, [query, isVisible]); // 移除fetchSuggestions依赖，避免循环\n\n  // 键盘导航处理\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (!isVisible || suggestionsRef.current.length === 0) return;\n\n      switch (event.key) {\n        case 'ArrowDown':\n          event.preventDefault();\n          setSelectedIndex(prev =>\n            prev < suggestionsRef.current.length - 1 ? prev + 1 : 0\n          );\n          break;\n        case 'ArrowUp':\n          event.preventDefault();\n          setSelectedIndex(prev =>\n            prev > 0 ? prev - 1 : suggestionsRef.current.length - 1\n          );\n          break;\n        case 'Enter':\n          event.preventDefault();\n          if (selectedIndexRef.current >= 0 && selectedIndexRef.current < suggestionsRef.current.length) {\n            onSelect(suggestionsRef.current[selectedIndexRef.current]);\n          }\n          break;\n        case 'Escape':\n          event.preventDefault();\n          onClose();\n          break;\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }\n  }, [isVisible, onSelect, onClose]); // 只依赖isVisible和稳定的回调函数\n\n  // 滚动到选中项\n  useEffect(() => {\n    if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {\n      itemRefs.current[selectedIndex]?.scrollIntoView({\n        behavior: 'smooth',\n        block: 'nearest'\n      });\n    }\n  }, [selectedIndex]);\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [isVisible, onClose]);\n\n  // 处理建议项点击\n  const handleSuggestionClick = (suggestion: SuggestionItem) => {\n    onSelect(suggestion);\n  };\n\n  // 获取建议项样式\n  const getSuggestionItemClass = (index: number) => {\n    const baseClass = \"flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150\";\n    const selectedClass = index === selectedIndex \n      ? \"bg-blue-50 text-blue-700 border-l-2 border-blue-500\" \n      : \"hover:bg-gray-50 text-gray-700\";\n    \n    return `${baseClass} ${selectedClass}`;\n  };\n\n  // 获取建议类型标签\n  const getTypeLabel = (type: string) => {\n    switch (type) {\n      case 'article': return '文章';\n      case 'query': return '热门';\n      case 'history': return '历史';\n      default: return '';\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div \n      ref={containerRef}\n      className={`absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto ${className}`}\n      role=\"listbox\"\n      aria-label=\"搜索建议\"\n    >\n      {loading && (\n        <div className=\"flex items-center justify-center py-4\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>\n          <span className=\"ml-2 text-sm text-gray-500\">获取建议中...</span>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"px-4 py-3 text-sm text-red-600 bg-red-50\">\n          {error}\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length === 0 && query.length >= 2 && (\n        <div className=\"px-4 py-3 text-sm text-gray-500 text-center\">\n          暂无相关建议\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length > 0 && (\n        <div className=\"py-1\">\n          {suggestions.map((suggestion, index) => (\n            <div\n              key={`${suggestion.type}-${suggestion.text}-${index}`}\n              ref={el => itemRefs.current[index] = el}\n              className={getSuggestionItemClass(index)}\n              onClick={() => handleSuggestionClick(suggestion)}\n              role=\"option\"\n              aria-selected={index === selectedIndex}\n            >\n              <div className=\"flex-shrink-0 text-gray-400\">\n                {suggestion.icon}\n              </div>\n              \n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"font-medium truncate\">\n                    {suggestion.text}\n                  </span>\n                  {showCategories && suggestion.category && (\n                    <span className=\"text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full\">\n                      {suggestion.category}\n                    </span>\n                  )}\n                </div>\n                <div className=\"text-xs text-gray-500 mt-1\">\n                  {getTypeLabel(suggestion.type)}\n                </div>\n              </div>\n\n              <ChevronRight className=\"w-4 h-4 text-gray-400 flex-shrink-0\" />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {!loading && !error && suggestions.length > 0 && (\n        <div className=\"px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t\">\n          使用 ↑↓ 键导航，回车选择，ESC 关闭\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,yBAAyB;AAEzB;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;AA+Be,SAAS,kBAAkB,EACxC,KAAK,EACL,SAAS,EACT,QAAQ,EACR,OAAO,EACP,gBAAgB,EAAE,EAClB,YAAY,EAAE,EACd,iBAAiB,CAAC,EAClB,cAAc,IAAI,EAClB,iBAAiB,IAAI,EACE;;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA6B,EAAE;IAErD,6BAA6B;IAC7B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,SAAS;IACT,eAAe,OAAO,GAAG;IACzB,iBAAiB,OAAO,GAAG;IAE3B,SAAS;IACT,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC1C,IAAI,CAAC,eAAe,YAAY,MAAM,GAAG,GAAG;gBAC1C,eAAe,EAAE;gBACjB;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,+IAAA,CAAA,YAAS,CAAC,cAAc,CAAC,aAAa,iBAAiB;gBAE9E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,iBAAmC,SAAS,IAAI,CAAC,GAAG;0FAAC,CAAA,OAAQ,CAAC;gCAClE,GAAG,IAAI;gCACP,MAAM,KAAK,IAAI,KAAK,0BAAY,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAAe,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;4BAC3F,CAAC;;oBAED,8BAA8B;oBAC9B,MAAM,qBAAuC,cACzC,cACG,MAAM;2EAAC,CAAA,cACN,YAAY,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,gBAAgB;0EAEjB,KAAK,CAAC,GAAG,GACT,GAAG;2EAAC,CAAA,cAAe,CAAC;gCACnB,MAAM;gCACN,MAAM;gCACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;4BACzB,CAAC;4EACH,EAAE;oBAEN,UAAU;oBACV,MAAM,iBAAiB;2BAAI;2BAAuB;qBAAe;oBACjE,MAAM,oBAAoB,eAAe,MAAM;6FAAC,CAAC,YAAY,OAAO,OAClE,UAAU,KAAK,SAAS;qGAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,IAAI;;;oBAG1D,eAAe,kBAAkB,KAAK,CAAC,GAAG;gBAC5C,OAAO;oBACL,SAAS,SAAS,KAAK,IAAI;oBAC3B,eAAe,EAAE;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,eAAe,EAAE;YACnB,SAAU;gBACR,WAAW;YACb;QACF;0DAAG;QAAC;KAAe,GAAG,WAAW;IAEjC,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa,OAAO;gBACtB,iBAAiB;YACnB,OAAO;gBACL,eAAe,EAAE;gBACjB,iBAAiB,CAAC;YACpB;QACF;sCAAG;QAAC;QAAO;KAAU,GAAG,4BAA4B;IAEpD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB,CAAC;oBACrB,IAAI,CAAC,aAAa,eAAe,OAAO,CAAC,MAAM,KAAK,GAAG;oBAEvD,OAAQ,MAAM,GAAG;wBACf,KAAK;4BACH,MAAM,cAAc;4BACpB;6EAAiB,CAAA,OACf,OAAO,eAAe,OAAO,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAExD;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;6EAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,eAAe,OAAO,CAAC,MAAM,GAAG;;4BAExD;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB,IAAI,iBAAiB,OAAO,IAAI,KAAK,iBAAiB,OAAO,GAAG,eAAe,OAAO,CAAC,MAAM,EAAE;gCAC7F,SAAS,eAAe,OAAO,CAAC,iBAAiB,OAAO,CAAC;4BAC3D;4BACA;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;4BACA;oBACJ;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,WAAW;gBACrC;mDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;YACvD;QACF;sCAAG;QAAC;QAAW;QAAU;KAAQ,GAAG,uBAAuB;IAE3D,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,iBAAiB,KAAK,SAAS,OAAO,CAAC,cAAc,EAAE;gBACzD,SAAS,OAAO,CAAC,cAAc,EAAE,eAAe;oBAC9C,UAAU;oBACV,OAAO;gBACT;YACF;QACF;sCAAG;QAAC;KAAc;IAElB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAChF;oBACF;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;gBACvC;mDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;YACzD;QACF;sCAAG;QAAC;QAAW;KAAQ;IAEvB,UAAU;IACV,MAAM,wBAAwB,CAAC;QAC7B,SAAS;IACX;IAEA,UAAU;IACV,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAY;QAClB,MAAM,gBAAgB,UAAU,gBAC5B,wDACA;QAEJ,OAAO,GAAG,UAAU,CAAC,EAAE,eAAe;IACxC;IAEA,WAAW;IACX,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,oHAAoH,EAAE,WAAW;QAC7I,MAAK;QACL,cAAW;;YAEV,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;YAIhD,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,KAAK,KAAK,MAAM,MAAM,IAAI,mBACjE,6LAAC;gBAAI,WAAU;0BAA8C;;;;;;YAK9D,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,GAAG,mBAC1C,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;wBACrC,WAAW,uBAAuB;wBAClC,SAAS,IAAM,sBAAsB;wBACrC,MAAK;wBACL,iBAAe,UAAU;;0CAEzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,IAAI;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,WAAW,IAAI;;;;;;4CAEjB,kBAAkB,WAAW,QAAQ,kBACpC,6LAAC;gDAAK,WAAU;0DACb,WAAW,QAAQ;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;kDACZ,aAAa,WAAW,IAAI;;;;;;;;;;;;0CAIjC,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;uBA3BnB,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;YAiC5D,CAAC,WAAW,CAAC,SAAS,YAAY,MAAM,GAAG,mBAC1C,6LAAC;gBAAI,WAAU;0BAAsD;;;;;;;;;;;;AAM7E;GAzPwB;KAAA", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/AdvancedSearchFilters.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 高级搜索筛选组件\n// 提供分类、难度、标签、排序等多维度搜索筛选功能\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Filter,\n  X,\n  ChevronDown,\n  Tag,\n  Settings,\n  Calendar,\n  Star\n} from 'lucide-react';\n\n// 筛选选项类型\ninterface FilterOption {\n  value: string;\n  label: string;\n  count?: number;\n  color?: string;\n  icon?: React.ReactNode;\n}\n\n// 筛选状态类型\ninterface FilterState {\n  category?: string;\n  difficulty?: string;\n  tags: string[];\n  sortBy: 'relevance' | 'date' | 'title';\n  sortOrder: 'asc' | 'desc';\n  dateRange?: {\n    start?: string;\n    end?: string;\n  };\n}\n\n// 组件属性\ninterface AdvancedSearchFiltersProps {\n  filters: FilterState;\n  onFiltersChange: (filters: FilterState) => void;\n  categories?: FilterOption[];\n  availableTags?: FilterOption[];\n  className?: string;\n  isCollapsed?: boolean;\n  onToggleCollapse?: () => void;\n  showResultCount?: boolean;\n  resultCount?: number;\n}\n\n// 难度选项\nconst DIFFICULTY_OPTIONS: FilterOption[] = [\n  { value: 'beginner', label: '初级', color: 'bg-green-100 text-green-800' },\n  { value: 'intermediate', label: '中级', color: 'bg-yellow-100 text-yellow-800' },\n  { value: 'advanced', label: '高级', color: 'bg-red-100 text-red-800' },\n  { value: 'expert', label: '专家', color: 'bg-purple-100 text-purple-800' }\n];\n\n// 排序选项\nconst SORT_OPTIONS: FilterOption[] = [\n  { value: 'relevance', label: '相关性', icon: <Star className=\"w-4 h-4\" /> },\n  { value: 'date', label: '更新时间', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'title', label: '标题', icon: <Tag className=\"w-4 h-4\" /> }\n];\n\nexport default function AdvancedSearchFilters({\n  filters,\n  onFiltersChange,\n  categories = [],\n  availableTags = [],\n  className = '',\n  isCollapsed = false,\n  onToggleCollapse,\n  showResultCount = true,\n  resultCount = 0\n}: AdvancedSearchFiltersProps) {\n  const [isExpanded, setIsExpanded] = useState(!isCollapsed);\n  const [tagSearchQuery, setTagSearchQuery] = useState('');\n  const [showTagDropdown, setShowTagDropdown] = useState(false);\n\n  // 处理筛选器变化\n  const handleFilterChange = useCallback((key: keyof FilterState, value: any) => {\n    const newFilters = { ...filters, [key]: value };\n    onFiltersChange(newFilters);\n  }, [filters, onFiltersChange]);\n\n  // 处理标签添加\n  const handleTagAdd = useCallback((tagValue: string) => {\n    if (!filters.tags.includes(tagValue)) {\n      const newTags = [...filters.tags, tagValue];\n      handleFilterChange('tags', newTags);\n    }\n    setTagSearchQuery('');\n    setShowTagDropdown(false);\n  }, [filters.tags, handleFilterChange]);\n\n  // 处理标签移除\n  const handleTagRemove = useCallback((tagValue: string) => {\n    const newTags = filters.tags.filter(tag => tag !== tagValue);\n    handleFilterChange('tags', newTags);\n  }, [filters.tags, handleFilterChange]);\n\n  // 清空所有筛选器\n  const handleClearAll = useCallback(() => {\n    onFiltersChange({\n      tags: [],\n      sortBy: 'relevance',\n      sortOrder: 'desc'\n    });\n  }, [onFiltersChange]);\n\n  // 切换展开/收起\n  const handleToggleExpanded = () => {\n    const newExpanded = !isExpanded;\n    setIsExpanded(newExpanded);\n    onToggleCollapse?.();\n  };\n\n  // 过滤可用标签\n  const filteredTags = availableTags.filter(tag => \n    tag.label.toLowerCase().includes(tagSearchQuery.toLowerCase()) &&\n    !filters.tags.includes(tag.value)\n  );\n\n  // 检查是否有活动筛选器\n  const hasActiveFilters = filters.category || \n                          filters.difficulty || \n                          filters.tags.length > 0 ||\n                          filters.sortBy !== 'relevance' ||\n                          filters.sortOrder !== 'desc';\n\n  return (\n    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <div className=\"flex items-center gap-3\">\n          <button\n            type=\"button\"\n            onClick={handleToggleExpanded}\n            className=\"flex items-center gap-2 text-gray-700 hover:text-gray-900 transition-colors\"\n          >\n            <Filter className=\"w-5 h-5\" />\n            <span className=\"font-medium\">高级筛选</span>\n            <ChevronDown\n              className={`w-4 h-4 transition-transform duration-200 ${\n                isExpanded ? 'rotate-180' : ''\n              }`}\n            />\n          </button>\n          \n          {hasActiveFilters && (\n            <span className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\">\n              {[\n                filters.category ? 1 : 0,\n                filters.difficulty ? 1 : 0,\n                filters.tags.length,\n                (filters.sortBy !== 'relevance' || filters.sortOrder !== 'desc') ? 1 : 0\n              ].reduce((a, b) => a + b, 0)} 个筛选器\n            </span>\n          )}\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          {showResultCount && (\n            <span className=\"text-sm text-gray-500\">\n              {resultCount} 个结果\n            </span>\n          )}\n          \n          {hasActiveFilters && (\n            <button\n              type=\"button\"\n              onClick={handleClearAll}\n              className=\"text-sm text-gray-500 hover:text-red-600 transition-colors\"\n            >\n              清空筛选\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 筛选器内容 */}\n      {isExpanded && (\n        <div className=\"p-4 space-y-4\">\n          {/* 分类筛选 */}\n          {categories.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                分类\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n                <button\n                  type=\"button\"\n                  onClick={() => handleFilterChange('category', undefined)}\n                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                    !filters.category\n                      ? 'bg-blue-50 border-blue-200 text-blue-700'\n                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  全部分类\n                </button>\n                {categories.map((category) => (\n                  <button\n                    key={category.value}\n                    type=\"button\"\n                    onClick={() => handleFilterChange('category', category.value)}\n                    className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                      filters.category === category.value\n                        ? 'bg-blue-50 border-blue-200 text-blue-700'\n                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                    }`}\n                  >\n                    {category.label}\n                    {category.count && (\n                      <span className=\"ml-1 text-xs text-gray-500\">\n                        ({category.count})\n                      </span>\n                    )}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 难度筛选 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              难度等级\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                type=\"button\"\n                onClick={() => handleFilterChange('difficulty', undefined)}\n                className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                  !filters.difficulty\n                    ? 'bg-blue-50 border-blue-200 text-blue-700'\n                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                全部难度\n              </button>\n              {DIFFICULTY_OPTIONS.map((difficulty) => (\n                <button\n                  key={difficulty.value}\n                  type=\"button\"\n                  onClick={() => handleFilterChange('difficulty', difficulty.value)}\n                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${\n                    filters.difficulty === difficulty.value\n                      ? 'bg-blue-50 border-blue-200 text-blue-700'\n                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'\n                  }`}\n                >\n                  <span className={`inline-block w-2 h-2 rounded-full mr-2 ${difficulty.color?.split(' ')[0] || 'bg-gray-300'}`}></span>\n                  {difficulty.label}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* 标签筛选 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              标签\n            </label>\n            \n            {/* 已选标签 */}\n            {filters.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {filters.tags.map((tag) => {\n                  const tagOption = availableTags.find(t => t.value === tag);\n                  return (\n                    <span\n                      key={tag}\n                      className=\"inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\"\n                    >\n                      <Tag className=\"w-3 h-3\" />\n                      {tagOption?.label || tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => handleTagRemove(tag)}\n                        className=\"ml-1 hover:text-blue-600\"\n                      >\n                        <X className=\"w-3 h-3\" />\n                      </button>\n                    </span>\n                  );\n                })}\n              </div>\n            )}\n\n            {/* 标签搜索 */}\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={tagSearchQuery}\n                onChange={(e) => setTagSearchQuery(e.target.value)}\n                onFocus={() => setShowTagDropdown(true)}\n                onBlur={() => setTimeout(() => setShowTagDropdown(false), 200)}\n                placeholder=\"搜索标签...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n              \n              {/* 标签下拉列表 */}\n              {showTagDropdown && filteredTags.length > 0 && (\n                <div className=\"absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto\">\n                  {filteredTags.slice(0, 10).map((tag) => (\n                    <button\n                      key={tag.value}\n                      type=\"button\"\n                      onClick={() => handleTagAdd(tag.value)}\n                      className=\"w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2\"\n                    >\n                      <Tag className=\"w-4 h-4 text-gray-400\" />\n                      <span>{tag.label}</span>\n                      {tag.count && (\n                        <span className=\"ml-auto text-xs text-gray-500\">\n                          {tag.count}\n                        </span>\n                      )}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 排序选项 */}\n          <div className=\"flex items-center gap-4 pt-2 border-t border-gray-200\">\n            <div className=\"flex items-center gap-2\">\n              <Settings className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">排序:</span>\n            </div>\n            \n            <div className=\"flex items-center gap-2\">\n              {SORT_OPTIONS.map((option) => (\n                <button\n                  key={option.value}\n                  type=\"button\"\n                  onClick={() => handleFilterChange('sortBy', option.value)}\n                  className={`flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors ${\n                    filters.sortBy === option.value\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-100'\n                  }`}\n                >\n                  {option.icon}\n                  {option.label}\n                </button>\n              ))}\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}\n              className=\"flex items-center gap-1 px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors\"\n              title={filters.sortOrder === 'asc' ? '升序' : '降序'}\n            >\n              <span className={`transform transition-transform ${filters.sortOrder === 'asc' ? 'rotate-180' : ''}`}>\n                ↓\n              </span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,wBAAwB;AACxB,0BAA0B;AAE1B;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;AAmDA,OAAO;AACP,MAAM,qBAAqC;IACzC;QAAE,OAAO;QAAY,OAAO;QAAM,OAAO;IAA8B;IACvE;QAAE,OAAO;QAAgB,OAAO;QAAM,OAAO;IAAgC;IAC7E;QAAE,OAAO;QAAY,OAAO;QAAM,OAAO;IAA0B;IACnE;QAAE,OAAO;QAAU,OAAO;QAAM,OAAO;IAAgC;CACxE;AAED,OAAO;AACP,MAAM,eAA+B;IACnC;QAAE,OAAO;QAAa,OAAO;QAAO,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;IACvE;QAAE,OAAO;QAAQ,OAAO;QAAQ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IACvE;QAAE,OAAO;QAAS,OAAO;QAAM,oBAAM,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAAa;CAClE;AAEc,SAAS,sBAAsB,EAC5C,OAAO,EACP,eAAe,EACf,aAAa,EAAE,EACf,gBAAgB,EAAE,EAClB,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,gBAAgB,EAChB,kBAAkB,IAAI,EACtB,cAAc,CAAC,EACY;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,UAAU;IACV,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC,KAAwB;YAC9D,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE,CAAC,IAAI,EAAE;YAAM;YAC9C,gBAAgB;QAClB;gEAAG;QAAC;QAAS;KAAgB;IAE7B,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW;gBACpC,MAAM,UAAU;uBAAI,QAAQ,IAAI;oBAAE;iBAAS;gBAC3C,mBAAmB,QAAQ;YAC7B;YACA,kBAAkB;YAClB,mBAAmB;QACrB;0DAAG;QAAC,QAAQ,IAAI;QAAE;KAAmB;IAErC,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACnC,MAAM,UAAU,QAAQ,IAAI,CAAC,MAAM;8EAAC,CAAA,MAAO,QAAQ;;YACnD,mBAAmB,QAAQ;QAC7B;6DAAG;QAAC,QAAQ,IAAI;QAAE;KAAmB;IAErC,UAAU;IACV,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACjC,gBAAgB;gBACd,MAAM,EAAE;gBACR,QAAQ;gBACR,WAAW;YACb;QACF;4DAAG;QAAC;KAAgB;IAEpB,UAAU;IACV,MAAM,uBAAuB;QAC3B,MAAM,cAAc,CAAC;QACrB,cAAc;QACd;IACF;IAEA,SAAS;IACT,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,MACxC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC3D,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK;IAGlC,aAAa;IACb,MAAM,mBAAmB,QAAQ,QAAQ,IACjB,QAAQ,UAAU,IAClB,QAAQ,IAAI,CAAC,MAAM,GAAG,KACtB,QAAQ,MAAM,KAAK,eACnB,QAAQ,SAAS,KAAK;IAE9C,qBACE,6LAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAEjF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,6LAAC,uNAAA,CAAA,cAAW;wCACV,WAAW,CAAC,0CAA0C,EACpD,aAAa,eAAe,IAC5B;;;;;;;;;;;;4BAIL,kCACC,6LAAC;gCAAK,WAAU;;oCACb;wCACC,QAAQ,QAAQ,GAAG,IAAI;wCACvB,QAAQ,UAAU,GAAG,IAAI;wCACzB,QAAQ,IAAI,CAAC,MAAM;wCAClB,QAAQ,MAAM,KAAK,eAAe,QAAQ,SAAS,KAAK,SAAU,IAAI;qCACxE,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;oCAAG;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;4BACZ,iCACC,6LAAC;gCAAK,WAAU;;oCACb;oCAAY;;;;;;;4BAIhB,kCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAQN,4BACC,6LAAC;gBAAI,WAAU;;oBAEZ,WAAW,MAAM,GAAG,mBACnB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,YAAY;wCAC9C,WAAW,CAAC,sDAAsD,EAChE,CAAC,QAAQ,QAAQ,GACb,6CACA,2DACJ;kDACH;;;;;;oCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,mBAAmB,YAAY,SAAS,KAAK;4CAC5D,WAAW,CAAC,sDAAsD,EAChE,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAC/B,6CACA,2DACJ;;gDAED,SAAS,KAAK;gDACd,SAAS,KAAK,kBACb,6LAAC;oDAAK,WAAU;;wDAA6B;wDACzC,SAAS,KAAK;wDAAC;;;;;;;;2CAZhB,SAAS,KAAK;;;;;;;;;;;;;;;;;kCAsB7B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,cAAc;wCAChD,WAAW,CAAC,sDAAsD,EAChE,CAAC,QAAQ,UAAU,GACf,6CACA,2DACJ;kDACH;;;;;;oCAGA,mBAAmB,GAAG,CAAC,CAAC,2BACvB,6LAAC;4CAEC,MAAK;4CACL,SAAS,IAAM,mBAAmB,cAAc,WAAW,KAAK;4CAChE,WAAW,CAAC,sDAAsD,EAChE,QAAQ,UAAU,KAAK,WAAW,KAAK,GACnC,6CACA,2DACJ;;8DAEF,6LAAC;oDAAK,WAAW,CAAC,uCAAuC,EAAE,WAAW,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,eAAe;;;;;;gDAC5G,WAAW,KAAK;;2CAVZ,WAAW,KAAK;;;;;;;;;;;;;;;;;kCAiB7B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;4BAK/D,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;oCACjB,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oCACtD,qBACE,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,WAAW,SAAS;0DACrB,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAVV;;;;;gCAcX;;;;;;0CAKJ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,SAAS,IAAM,mBAAmB;wCAClC,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;wCAC1D,aAAY;wCACZ,WAAU;;;;;;oCAIX,mBAAmB,aAAa,MAAM,GAAG,mBACxC,6LAAC;wCAAI,WAAU;kDACZ,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,oBAC9B,6LAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,IAAI,KAAK;gDACrC,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAM,IAAI,KAAK;;;;;;oDACf,IAAI,KAAK,kBACR,6LAAC;wDAAK,WAAU;kEACb,IAAI,KAAK;;;;;;;+CATT,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAoB1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,mBAAmB,UAAU,OAAO,KAAK;wCACxD,WAAW,CAAC,uEAAuE,EACjF,QAAQ,MAAM,KAAK,OAAO,KAAK,GAC3B,8BACA,mCACJ;;4CAED,OAAO,IAAI;4CACX,OAAO,KAAK;;uCAVR,OAAO,KAAK;;;;;;;;;;0CAevB,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,mBAAmB,aAAa,QAAQ,SAAS,KAAK,QAAQ,SAAS;gCACtF,WAAU;gCACV,OAAO,QAAQ,SAAS,KAAK,QAAQ,OAAO;0CAE5C,cAAA,6LAAC;oCAAK,WAAW,CAAC,+BAA+B,EAAE,QAAQ,SAAS,KAAK,QAAQ,eAAe,IAAI;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpH;GA9SwB;KAAA", "debugId": null}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/SearchResultItem.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 搜索结果项组件\n// 提供搜索结果高亮显示、相关性分数、快速操作功能\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport {\n  FileText,\n  Tag,\n  Calendar,\n  Star,\n  Eye,\n  Bookmark,\n  Share,\n  ChevronRight\n} from 'lucide-react';\n\n// 搜索结果项类型\ninterface SearchResultItemData {\n  id: string;\n  title: string;\n  description?: string;\n  content?: string;\n  category?: {\n    id: string;\n    name: string;\n    icon?: string;\n    color?: string;\n  };\n  tags?: string[];\n  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert';\n  last_updated: string;\n  created_at: string;\n  view_count?: number;\n  relevanceScore?: number;\n  highlight?: {\n    title?: string;\n    description?: string;\n    content?: string;\n  };\n}\n\n// 组件属性\ninterface SearchResultItemProps {\n  item: SearchResultItemData;\n  query?: string;\n  onBookmark?: (id: string, bookmarked: boolean) => void;\n  onShare?: (item: SearchResultItemData) => void;\n  showRelevanceScore?: boolean;\n  showActions?: boolean;\n  className?: string;\n  isBookmarked?: boolean;\n}\n\n// 难度配置\nconst DIFFICULTY_CONFIG = {\n  beginner: { label: '初级', color: 'bg-green-100 text-green-800', icon: '●' },\n  intermediate: { label: '中级', color: 'bg-yellow-100 text-yellow-800', icon: '●●' },\n  advanced: { label: '高级', color: 'bg-orange-100 text-orange-800', icon: '●●●' },\n  expert: { label: '专家', color: 'bg-red-100 text-red-800', icon: '●●●●' }\n};\n\nexport default function SearchResultItem({\n  item,\n  query,\n  onBookmark,\n  onShare,\n  showRelevanceScore = false,\n  showActions = true,\n  className = '',\n  isBookmarked = false\n}: SearchResultItemProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  // 处理高亮文本渲染\n  const renderHighlightedText = (text: string, highlightedText?: string) => {\n    if (!highlightedText || !query) {\n      return <span>{text}</span>;\n    }\n\n    // 如果有高亮文本，直接使用（已包含<mark>标签）\n    return (\n      <span \n        dangerouslySetInnerHTML={{ __html: highlightedText }}\n        className=\"[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded\"\n      />\n    );\n  };\n\n  // 格式化时间\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) return '今天';\n    if (diffDays === 1) return '昨天';\n    if (diffDays < 7) return `${diffDays}天前`;\n    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;\n    return `${Math.floor(diffDays / 30)}月前`;\n  };\n\n  // 处理收藏\n  const handleBookmark = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onBookmark?.(item.id, !isBookmarked);\n  };\n\n  // 处理分享\n  const handleShare = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onShare?.(item);\n  };\n\n  // 获取难度配置\n  const difficultyConfig = item.difficulty ? DIFFICULTY_CONFIG[item.difficulty] : null;\n\n  return (\n    <div\n      className={`group bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 ${className}`}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <Link href={`/knowledge/${item.id}`} className=\"block p-6\">\n        {/* 头部信息 */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex-1 min-w-0\">\n            {/* 标题 */}\n            <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2\">\n              {renderHighlightedText(item.title, item.highlight?.title)}\n            </h3>\n\n            {/* 元信息 */}\n            <div className=\"flex items-center gap-4 text-sm text-gray-500 mb-3\">\n              {/* 分类 */}\n              {item.category && (\n                <div className=\"flex items-center gap-1\">\n                  <div \n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: item.category.color || '#6B7280' }}\n                  />\n                  <span>{item.category.name}</span>\n                </div>\n              )}\n\n              {/* 难度 */}\n              {difficultyConfig && (\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${difficultyConfig.color}`}>\n                  {difficultyConfig.icon} {difficultyConfig.label}\n                </span>\n              )}\n\n              {/* 更新时间 */}\n              <div className=\"flex items-center gap-1\">\n                <Calendar className=\"w-4 h-4\" />\n                <span>{formatDate(item.last_updated)}</span>\n              </div>\n\n              {/* 浏览次数 */}\n              {item.view_count && item.view_count > 0 && (\n                <div className=\"flex items-center gap-1\">\n                  <Eye className=\"w-4 h-4\" />\n                  <span>{item.view_count}</span>\n                </div>\n              )}\n\n              {/* 相关性分数 */}\n              {showRelevanceScore && item.relevanceScore && (\n                <div className=\"flex items-center gap-1\">\n                  <Star className=\"w-4 h-4\" />\n                  <span>相关性: {Math.round(item.relevanceScore)}</span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          {showActions && isHovered && (\n            <div className=\"flex items-center gap-2 ml-4\">\n              <button\n                type=\"button\"\n                onClick={handleBookmark}\n                className=\"p-2 text-gray-400 hover:text-yellow-500 transition-colors\"\n                title={isBookmarked ? '取消收藏' : '收藏文章'}\n              >\n                {isBookmarked ? (\n                  <Bookmark className=\"w-5 h-5 text-yellow-500 fill-current\" />\n                ) : (\n                  <Bookmark className=\"w-5 h-5\" />\n                )}\n              </button>\n              \n              <button\n                type=\"button\"\n                onClick={handleShare}\n                className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\"\n                title=\"分享文章\"\n              >\n                <Share className=\"w-5 h-5\" />\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* 描述 */}\n        {item.description && (\n          <p className=\"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2\">\n            {renderHighlightedText(item.description, item.highlight?.description)}\n          </p>\n        )}\n\n        {/* 内容预览 */}\n        {item.highlight?.content && (\n          <div className=\"bg-gray-50 border-l-4 border-blue-500 p-3 mb-4\">\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\n              <span \n                dangerouslySetInnerHTML={{ __html: item.highlight.content }}\n                className=\"[&_mark]:bg-yellow-200 [&_mark]:px-1 [&_mark]:rounded\"\n              />\n            </p>\n          </div>\n        )}\n\n        {/* 标签 */}\n        {item.tags && item.tags.length > 0 && (\n          <div className=\"flex items-center gap-2 mb-4\">\n            <Tag className=\"w-4 h-4 text-gray-400\" />\n            <div className=\"flex flex-wrap gap-2\">\n              {item.tags.slice(0, 5).map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full hover:bg-gray-200 transition-colors\"\n                >\n                  {tag}\n                </span>\n              ))}\n              {item.tags.length > 5 && (\n                <span className=\"px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full\">\n                  +{item.tags.length - 5}\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* 底部 */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2 text-xs text-gray-400\">\n            <FileText className=\"w-4 h-4\" />\n            <span>知识库文章</span>\n          </div>\n\n          <div className=\"flex items-center gap-1 text-blue-600 group-hover:text-blue-700 transition-colors\">\n            <span className=\"text-sm font-medium\">查看详情</span>\n            <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\n          </div>\n        </div>\n      </Link>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,uBAAuB;AACvB,0BAA0B;AAE1B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;AAuDA,OAAO;AACP,MAAM,oBAAoB;IACxB,UAAU;QAAE,OAAO;QAAM,OAAO;QAA+B,MAAM;IAAI;IACzE,cAAc;QAAE,OAAO;QAAM,OAAO;QAAiC,MAAM;IAAK;IAChF,UAAU;QAAE,OAAO;QAAM,OAAO;QAAiC,MAAM;IAAM;IAC7E,QAAQ;QAAE,OAAO;QAAM,OAAO;QAA2B,MAAM;IAAO;AACxE;AAEe,SAAS,iBAAiB,EACvC,IAAI,EACJ,KAAK,EACL,UAAU,EACV,OAAO,EACP,qBAAqB,KAAK,EAC1B,cAAc,IAAI,EAClB,YAAY,EAAE,EACd,eAAe,KAAK,EACE;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,WAAW;IACX,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAC9B,qBAAO,6LAAC;0BAAM;;;;;;QAChB;QAEA,4BAA4B;QAC5B,qBACE,6LAAC;YACC,yBAAyB;gBAAE,QAAQ;YAAgB;YACnD,WAAU;;;;;;IAGhB;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAEzD,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;QACxC,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QACzD,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC;IACzC;IAEA,OAAO;IACP,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,aAAa,KAAK,EAAE,EAAE,CAAC;IACzB;IAEA,OAAO;IACP,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,mBAAmB,KAAK,UAAU,GAAG,iBAAiB,CAAC,KAAK,UAAU,CAAC,GAAG;IAEhF,qBACE,6LAAC;QACC,WAAW,CAAC,6FAA6F,EAAE,WAAW;QACtH,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;YAAE,WAAU;;8BAE7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAG,WAAU;8CACX,sBAAsB,KAAK,KAAK,EAAE,KAAK,SAAS,EAAE;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;wCAEZ,KAAK,QAAQ,kBACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,KAAK,QAAQ,CAAC,KAAK,IAAI;oDAAU;;;;;;8DAE7D,6LAAC;8DAAM,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;wCAK5B,kCACC,6LAAC;4CAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,KAAK,EAAE;;gDACpF,iBAAiB,IAAI;gDAAC;gDAAE,iBAAiB,KAAK;;;;;;;sDAKnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAM,WAAW,KAAK,YAAY;;;;;;;;;;;;wCAIpC,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,mBACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;8DAAM,KAAK,UAAU;;;;;;;;;;;;wCAKzB,sBAAsB,KAAK,cAAc,kBACxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;wDAAK;wDAAM,KAAK,KAAK,CAAC,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;wBAOjD,eAAe,2BACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAO,eAAe,SAAS;8CAE9B,6BACC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAIxB,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOxB,KAAK,WAAW,kBACf,6LAAC;oBAAE,WAAU;8BACV,sBAAsB,KAAK,WAAW,EAAE,KAAK,SAAS,EAAE;;;;;;gBAK5D,KAAK,SAAS,EAAE,yBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACX,cAAA,6LAAC;4BACC,yBAAyB;gCAAE,QAAQ,KAAK,SAAS,CAAC,OAAO;4BAAC;4BAC1D,WAAU;;;;;;;;;;;;;;;;gBAOjB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;oCAAK,WAAU;;wCAA2D;wCACvE,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;8BAQ/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;sCAGR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GAzMwB;KAAA", "debugId": null}}, {"offset": {"line": 1962, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/IntelligentSearch.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - 智能搜索主组件\n// 整合搜索输入、建议、筛选、历史、结果等所有搜索功能\n\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Search, X } from 'lucide-react';\nimport SearchSuggestions from './SearchSuggestions';\nimport AdvancedSearchFilters from './AdvancedSearchFilters';\nimport SearchHistory from './SearchHistory';\nimport SearchResultItem from './SearchResultItem';\nimport { useSearchHistory } from '@/hooks/useSearchHistory';\nimport { searchApi } from '@/lib/api/knowledge';\n\n// 搜索状态类型\ninterface SearchState {\n  query: string;\n  isSearching: boolean;\n  results: any[];\n  suggestions: any[];\n  showSuggestions: boolean;\n  showHistory: boolean;\n  showFilters: boolean;\n  filters: {\n    category?: string;\n    difficulty?: string;\n    tags: string[];\n    sortBy: 'relevance' | 'date' | 'title';\n    sortOrder: 'asc' | 'desc';\n  };\n  error?: string;\n  totalResults: number;\n}\n\n// 组件属性\ninterface IntelligentSearchProps {\n  placeholder?: string;\n  autoFocus?: boolean;\n  showAdvancedFilters?: boolean;\n  showSearchHistory?: boolean;\n  maxResults?: number;\n  className?: string;\n  onResultSelect?: (result: any) => void;\n  categories?: Array<{ value: string; label: string; count?: number }>;\n  availableTags?: Array<{ value: string; label: string; count?: number }>;\n}\n\nexport default function IntelligentSearch({\n  placeholder = '搜索知识库...',\n  autoFocus = false,\n  showAdvancedFilters = true,\n  showSearchHistory = true,\n  maxResults = 20,\n  className = '',\n  onResultSelect,\n  categories = [],\n  availableTags = []\n}: IntelligentSearchProps) {\n  // 状态管理\n  const [searchState, setSearchState] = useState<SearchState>({\n    query: '',\n    isSearching: false,\n    results: [],\n    suggestions: [],\n    showSuggestions: false,\n    showHistory: false,\n    showFilters: false,\n    filters: {\n      tags: [],\n      sortBy: 'relevance',\n      sortOrder: 'desc'\n    },\n    totalResults: 0\n  });\n\n  // 搜索历史Hook\n  const {\n    addToHistory,\n    recentHistory,\n    popularHistory,\n    clearHistory,\n    removeFromHistory,\n    getSearchCount,\n    totalSearches,\n    uniqueSearches\n  } = useSearchHistory();\n\n  // 引用\n  const searchInputRef = useRef<HTMLInputElement>(null);\n  const searchTimeoutRef = useRef<NodeJS.Timeout>();\n\n  // 执行搜索\n  const performSearch = useCallback(async (query: string, filters?: any) => {\n    if (!query.trim()) {\n      setSearchState(prev => ({\n        ...prev,\n        results: [],\n        totalResults: 0,\n        showSuggestions: false\n      }));\n      return;\n    }\n\n    setSearchState(prev => ({\n      ...prev,\n      isSearching: true,\n      error: undefined\n    }));\n\n    try {\n      // 使用传入的filters或当前状态的filters\n      const currentFilters = filters || searchState.filters;\n\n      // 调用搜索API\n      const response = await searchApi.search({\n        query,\n        category: currentFilters.category,\n        difficulty: currentFilters.difficulty,\n        tags: currentFilters.tags,\n        sortBy: currentFilters.sortBy,\n        sortOrder: currentFilters.sortOrder,\n        limit: maxResults\n      });\n\n      console.log('=== 前端搜索响应调试 ===');\n      console.log('API响应成功:', response.success);\n      console.log('API返回数据:', response.data);\n      console.log('数据数量:', response.data?.length || 0);\n      console.log('第一条数据:', response.data?.[0]);\n\n      if (response.success) {\n        // 添加到搜索历史\n        addToHistory(query);\n\n        setSearchState(prev => ({\n          ...prev,\n          results: response.data || [],\n          totalResults: response.data?.length || 0,\n          isSearching: false,\n          showSuggestions: false,\n          showHistory: false\n        }));\n\n        console.log('搜索状态已更新，结果数量:', response.data?.length || 0);\n      } else {\n        console.error('搜索失败:', response.error);\n        throw new Error(response.error || '搜索失败');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSearchState(prev => ({\n        ...prev,\n        error: error instanceof Error ? error.message : '搜索出错，请稍后重试',\n        isSearching: false,\n        results: [],\n        totalResults: 0\n      }));\n    }\n  }, [maxResults, addToHistory]); // 移除searchState.filters依赖\n\n  // 处理搜索输入变化\n  const handleSearchChange = useCallback((value: string) => {\n    setSearchState(prev => ({\n      ...prev,\n      query: value,\n      showSuggestions: value.length >= 2,\n      showHistory: value.length === 0\n    }));\n\n    // 清除之前的搜索定时器\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // 如果查询为空，清空结果\n    if (!value.trim()) {\n      setSearchState(prev => ({\n        ...prev,\n        results: [],\n        totalResults: 0,\n        showSuggestions: false\n      }));\n      return;\n    }\n\n    // 延迟执行搜索 - 只有当输入长度>=2时才搜索\n    if (value.trim().length >= 2) {\n      searchTimeoutRef.current = setTimeout(() => {\n        performSearch(value);\n      }, 800); // 增加延迟到800ms，减少频繁搜索\n    }\n  }, [performSearch]);\n\n  // 处理搜索提交\n  const handleSearchSubmit = useCallback((query: string) => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    performSearch(query);\n  }, [performSearch]);\n\n  // 处理建议选择\n  const handleSuggestionSelect = useCallback((suggestion: any) => {\n    const query = suggestion.text || suggestion.query;\n    setSearchState(prev => ({ ...prev, query }));\n    handleSearchSubmit(query);\n  }, [handleSearchSubmit]);\n\n  // 关闭建议\n  const handleCloseSuggestions = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showSuggestions: false }));\n  }, []);\n\n  // 关闭历史\n  const handleCloseHistory = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showHistory: false }));\n  }, []);\n\n  // 处理历史选择\n  const handleHistorySelect = useCallback((query: string) => {\n    setSearchState(prev => ({ ...prev, query }));\n    handleSearchSubmit(query);\n  }, [handleSearchSubmit]);\n\n  // 处理筛选器变化\n  const handleFiltersChange = useCallback((filters: any) => {\n    setSearchState(prev => {\n      const newState = { ...prev, filters };\n\n      // 如果有查询，重新搜索\n      if (prev.query.trim()) {\n        // 使用setTimeout避免在setState中直接调用performSearch\n        setTimeout(() => {\n          performSearch(prev.query, filters);\n        }, 0);\n      }\n\n      return newState;\n    });\n  }, [performSearch]);\n\n  // 清空搜索\n  const handleClearSearch = useCallback(() => {\n    setSearchState(prev => ({\n      ...prev,\n      query: '',\n      results: [],\n      totalResults: 0,\n      showSuggestions: false,\n      showHistory: true,\n      error: undefined\n    }));\n    searchInputRef.current?.focus();\n  }, []);\n\n  // 切换筛选器显示\n  const toggleFilters = useCallback(() => {\n    setSearchState(prev => ({ ...prev, showFilters: !prev.showFilters }));\n  }, []);\n\n  // 组件挂载时自动聚焦\n  useEffect(() => {\n    if (autoFocus && searchInputRef.current) {\n      searchInputRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  // 清理定时器\n  useEffect(() => {\n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <div className={`w-full max-w-4xl mx-auto ${className}`}>\n      {/* 搜索输入区域 */}\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Search className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          \n          <input\n            ref={searchInputRef}\n            type=\"text\"\n            value={searchState.query}\n            onChange={(e) => handleSearchChange(e.target.value)}\n            onKeyDown={(e) => {\n              if (e.key === 'Enter') {\n                handleSearchSubmit(searchState.query);\n              }\n            }}\n            placeholder={placeholder}\n            className=\"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n          />\n\n          {searchState.query && (\n            <button\n              type=\"button\"\n              onClick={handleClearSearch}\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-5 w-5\" />\n            </button>\n          )}\n        </div>\n\n        {/* 搜索建议 */}\n        {searchState.showSuggestions && (\n          <div className=\"absolute top-full left-0 right-0 z-20 mt-1\">\n            <SearchSuggestions\n              query={searchState.query}\n              onSelect={handleSuggestionSelect}\n              onClose={handleCloseSuggestions}\n            />\n          </div>\n        )}\n\n        {/* 搜索历史 */}\n        {searchState.showHistory && showSearchHistory && (\n          <div className=\"absolute top-full left-0 right-0 z-20 mt-1\">\n            <SearchHistory\n              onSelect={handleHistorySelect}\n              onClose={handleCloseHistory}\n              maxItems={8}\n              showStats={true}\n            />\n          </div>\n        )}\n      </div>\n\n      {/* 高级筛选器 */}\n      {showAdvancedFilters && (\n        <div className=\"mt-4\">\n          <AdvancedSearchFilters\n            filters={searchState.filters}\n            onFiltersChange={handleFiltersChange}\n            categories={categories}\n            availableTags={availableTags}\n            isCollapsed={!searchState.showFilters}\n            onToggleCollapse={toggleFilters}\n            showResultCount={searchState.results.length > 0}\n            resultCount={searchState.totalResults}\n          />\n        </div>\n      )}\n\n      {/* 搜索状态和结果 */}\n      <div className=\"mt-6\">\n        {/* 加载状态 */}\n        {searchState.isSearching && (\n          <div className=\"text-center py-8\">\n            <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-2 text-gray-600\">搜索中...</p>\n          </div>\n        )}\n\n        {/* 错误状态 */}\n        {searchState.error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 text-center\">\n            <p className=\"text-red-600\">{searchState.error}</p>\n          </div>\n        )}\n\n        {/* 搜索结果 */}\n        {!searchState.isSearching && !searchState.error && searchState.results.length > 0 && (\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                搜索结果 ({searchState.totalResults})\n              </h2>\n            </div>\n            \n            <div className=\"space-y-4\">\n              {searchState.results.map((result, index) => (\n                <SearchResultItem\n                  key={result.id || index}\n                  item={result}\n                  query={searchState.query}\n                  showRelevanceScore={true}\n                  onBookmark={(id, bookmarked) => {\n                    // TODO: 实现收藏功能\n                    console.log('Bookmark:', id, bookmarked);\n                  }}\n                  onShare={(item) => {\n                    // TODO: 实现分享功能\n                    console.log('Share:', item);\n                  }}\n                />\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 无结果状态 */}\n        {!searchState.isSearching && !searchState.error && searchState.query && searchState.results.length === 0 && (\n          <div className=\"text-center py-8\">\n            <Search className=\"mx-auto h-12 w-12 text-gray-300\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">未找到相关结果</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              尝试使用不同的关键词或调整筛选条件\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,uBAAuB;AACvB,4BAA4B;AAE5B;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;AA+Ce,SAAS,kBAAkB,EACxC,cAAc,UAAU,EACxB,YAAY,KAAK,EACjB,sBAAsB,IAAI,EAC1B,oBAAoB,IAAI,EACxB,aAAa,EAAE,EACf,YAAY,EAAE,EACd,cAAc,EACd,aAAa,EAAE,EACf,gBAAgB,EAAE,EACK;;IACvB,OAAO;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,OAAO;QACP,aAAa;QACb,SAAS,EAAE;QACX,aAAa,EAAE;QACf,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,SAAS;YACP,MAAM,EAAE;YACR,QAAQ;YACR,WAAW;QACb;QACA,cAAc;IAChB;IAEA,WAAW;IACX,MAAM,EACJ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD;IAEnB,KAAK;IACL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE9B,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO,OAAe;YACtD,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB;oEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,SAAS,EAAE;4BACX,cAAc;4BACd,iBAAiB;wBACnB,CAAC;;gBACD;YACF;YAEA;gEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,aAAa;wBACb,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,iBAAiB,WAAW,YAAY,OAAO;gBAErD,UAAU;gBACV,MAAM,WAAW,MAAM,+IAAA,CAAA,YAAS,CAAC,MAAM,CAAC;oBACtC;oBACA,UAAU,eAAe,QAAQ;oBACjC,YAAY,eAAe,UAAU;oBACrC,MAAM,eAAe,IAAI;oBACzB,QAAQ,eAAe,MAAM;oBAC7B,WAAW,eAAe,SAAS;oBACnC,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;gBACxC,QAAQ,GAAG,CAAC,YAAY,SAAS,IAAI;gBACrC,QAAQ,GAAG,CAAC,SAAS,SAAS,IAAI,EAAE,UAAU;gBAC9C,QAAQ,GAAG,CAAC,UAAU,SAAS,IAAI,EAAE,CAAC,EAAE;gBAExC,IAAI,SAAS,OAAO,EAAE;oBACpB,UAAU;oBACV,aAAa;oBAEb;wEAAe,CAAA,OAAQ,CAAC;gCACtB,GAAG,IAAI;gCACP,SAAS,SAAS,IAAI,IAAI,EAAE;gCAC5B,cAAc,SAAS,IAAI,EAAE,UAAU;gCACvC,aAAa;gCACb,iBAAiB;gCACjB,aAAa;4BACf,CAAC;;oBAED,QAAQ,GAAG,CAAC,iBAAiB,SAAS,IAAI,EAAE,UAAU;gBACxD,OAAO;oBACL,QAAQ,KAAK,CAAC,SAAS,SAAS,KAAK;oBACrC,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B;oEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;4BAChD,aAAa;4BACb,SAAS,EAAE;4BACX,cAAc;wBAChB,CAAC;;YACH;QACF;uDAAG;QAAC;QAAY;KAAa,GAAG,0BAA0B;IAE1D,WAAW;IACX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC;qEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,iBAAiB,MAAM,MAAM,IAAI;wBACjC,aAAa,MAAM,MAAM,KAAK;oBAChC,CAAC;;YAED,aAAa;YACb,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,cAAc;YACd,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB;yEAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,SAAS,EAAE;4BACX,cAAc;4BACd,iBAAiB;wBACnB,CAAC;;gBACD;YACF;YAEA,0BAA0B;YAC1B,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG;gBAC5B,iBAAiB,OAAO,GAAG;yEAAW;wBACpC,cAAc;oBAChB;wEAAG,MAAM,oBAAoB;YAC/B;QACF;4DAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YACA,cAAc;QAChB;4DAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YAC1C,MAAM,QAAQ,WAAW,IAAI,IAAI,WAAW,KAAK;YACjD;yEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAM,CAAC;;YAC1C,mBAAmB;QACrB;gEAAG;QAAC;KAAmB;IAEvB,OAAO;IACP,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACzC;yEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAM,CAAC;;QAC7D;gEAAG,EAAE;IAEL,OAAO;IACP,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC;qEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAM,CAAC;;QACzD;4DAAG,EAAE;IAEL,SAAS;IACT,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAM,CAAC;;YAC1C,mBAAmB;QACrB;6DAAG;QAAC;KAAmB;IAEvB,UAAU;IACV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAe,CAAA;oBACb,MAAM,WAAW;wBAAE,GAAG,IAAI;wBAAE;oBAAQ;oBAEpC,aAAa;oBACb,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI;wBACrB,4CAA4C;wBAC5C;kFAAW;gCACT,cAAc,KAAK,KAAK,EAAE;4BAC5B;iFAAG;oBACL;oBAEA,OAAO;gBACT;;QACF;6DAAG;QAAC;KAAc;IAElB,OAAO;IACP,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACpC;oEAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,SAAS,EAAE;wBACX,cAAc;wBACd,iBAAiB;wBACjB,aAAa;wBACb,OAAO;oBACT,CAAC;;YACD,eAAe,OAAO,EAAE;QAC1B;2DAAG,EAAE;IAEL,UAAU;IACV,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAChC;gEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa,CAAC,KAAK,WAAW;oBAAC,CAAC;;QACrE;uDAAG,EAAE;IAEL,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa,eAAe,OAAO,EAAE;gBACvC,eAAe,OAAO,CAAC,KAAK;YAC9B;QACF;sCAAG;QAAC;KAAU;IAEd,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;+CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BAErD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,OAAO,YAAY,KAAK;gCACxB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,SAAS;wCACrB,mBAAmB,YAAY,KAAK;oCACtC;gCACF;gCACA,aAAa;gCACb,WAAU;;;;;;4BAGX,YAAY,KAAK,kBAChB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMlB,YAAY,eAAe,kBAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qKAAA,CAAA,UAAiB;4BAChB,OAAO,YAAY,KAAK;4BACxB,UAAU;4BACV,SAAS;;;;;;;;;;;oBAMd,YAAY,WAAW,IAAI,mCAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iKAAA,CAAA,UAAa;4BACZ,UAAU;4BACV,SAAS;4BACT,UAAU;4BACV,WAAW;;;;;;;;;;;;;;;;;YAOlB,qCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yKAAA,CAAA,UAAqB;oBACpB,SAAS,YAAY,OAAO;oBAC5B,iBAAiB;oBACjB,YAAY;oBACZ,eAAe;oBACf,aAAa,CAAC,YAAY,WAAW;oBACrC,kBAAkB;oBAClB,iBAAiB,YAAY,OAAO,CAAC,MAAM,GAAG;oBAC9C,aAAa,YAAY,YAAY;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,WAAW,kBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;oBAKrC,YAAY,KAAK,kBAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB,YAAY,KAAK;;;;;;;;;;;oBAKjD,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,OAAO,CAAC,MAAM,GAAG,mBAC9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAsC;wCAC3C,YAAY,YAAY;wCAAC;;;;;;;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAChC,6LAAC,oKAAA,CAAA,UAAgB;wCAEf,MAAM;wCACN,OAAO,YAAY,KAAK;wCACxB,oBAAoB;wCACpB,YAAY,CAAC,IAAI;4CACf,eAAe;4CACf,QAAQ,GAAG,CAAC,aAAa,IAAI;wCAC/B;wCACA,SAAS,CAAC;4CACR,eAAe;4CACf,QAAQ,GAAG,CAAC,UAAU;wCACxB;uCAXK,OAAO,EAAE,IAAI;;;;;;;;;;;;;;;;oBAmB3B,CAAC,YAAY,WAAW,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,IAAI,YAAY,OAAO,CAAC,MAAM,KAAK,mBACrG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GA3WwB;;QAsClB,iJAAA,CAAA,mBAAgB;;;KAtCE", "debugId": null}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Breadcrumb面包屑导航组件\n// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getBreadcrumbs } from '@/lib/navigation';\nimport { BreadcrumbProps } from '@/lib/types';\n\nconst Breadcrumb: React.FC<BreadcrumbProps> = React.memo(({\n  pathname,\n  maxItems = 5,\n  className,\n  ...props\n}) => {\n  // 获取面包屑数据\n  const breadcrumbs = React.useMemo(() => getBreadcrumbs(pathname), [pathname]);\n\n  // 如果只有首页，不显示面包屑\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  // 处理超长路径的截断\n  const displayBreadcrumbs = breadcrumbs.length > maxItems\n    ? [\n        breadcrumbs[0], // 首页\n        { name: '...', href: '#', isEllipsis: true },\n        ...breadcrumbs.slice(-2) // 最后两项\n      ]\n    : breadcrumbs;\n\n  return (\n    <motion.nav\n      initial={{ opacity: 0, y: -10 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n      aria-label=\"面包屑导航\"\n      className={cn(\n        'flex items-center space-x-1 text-sm',\n        'py-2 px-1',\n        className\n      )}\n      {...props}\n    >\n      <ol className=\"flex items-center space-x-1 md:space-x-2\">\n        {displayBreadcrumbs.map((item, index) => {\n          const isLast = index === displayBreadcrumbs.length - 1;\n          const isEllipsis = 'isEllipsis' in item && item.isEllipsis;\n          const isHome = item.href === '/';\n\n          return (\n            <li key={`${item.href}-${index}`} className=\"flex items-center\">\n              {/* 分隔符 */}\n              {index > 0 && (\n                <ChevronRight \n                  className=\"w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0\" \n                  aria-hidden=\"true\"\n                />\n              )}\n\n              {/* 面包屑项 */}\n              {isEllipsis ? (\n                <span className=\"text-gray-400 px-1\">...</span>\n              ) : isLast ? (\n                // 当前页面（不可点击）\n                <span\n                  className={cn(\n                    'font-medium text-mysql-primary',\n                    'px-2 py-1 rounded-md',\n                    'bg-mysql-primary/5',\n                    'flex items-center space-x-1'\n                  )}\n                  aria-current=\"page\"\n                >\n                  {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                  <span className={cn(\n                    'truncate max-w-[80px] md:max-w-[120px]',\n                    isHome && 'hidden md:inline'\n                  )}>\n                    {item.name}\n                  </span>\n                </span>\n              ) : (\n                // 可点击的面包屑项\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'text-gray-600 hover:text-mysql-primary',\n                      'px-2 py-1 rounded-md',\n                      'hover:bg-mysql-primary/5',\n                      'transition-all duration-200',\n                      'flex items-center space-x-1',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                    <span className={cn(\n                      'truncate max-w-[80px] md:max-w-[120px]',\n                      isHome && 'hidden md:inline'\n                    )}>\n                      {item.name}\n                    </span>\n                  </Link>\n                </motion.div>\n              )}\n            </li>\n          );\n        })}\n      </ol>\n    </motion.nav>\n  );\n});\n\nBreadcrumb.displayName = 'Breadcrumb';\n\nexport default Breadcrumb;\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,mCAAmC;AAEnC;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAVA;;;;;;;AAaA,MAAM,2BAAwC,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC,CAAC,EACxD,QAAQ,EACR,WAAW,CAAC,EACZ,SAAS,EACT,GAAG,OACJ;;IACC,UAAU;IACV,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,OAAO;2CAAC,IAAM,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD,EAAE;0CAAW;QAAC;KAAS;IAE5E,gBAAgB;IAChB,IAAI,YAAY,MAAM,IAAI,GAAG;QAC3B,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,qBAAqB,YAAY,MAAM,GAAG,WAC5C;QACE,WAAW,CAAC,EAAE;QACd;YAAE,MAAM;YAAO,MAAM;YAAK,YAAY;QAAK;WACxC,YAAY,KAAK,CAAC,CAAC,GAAG,OAAO;KACjC,GACD;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,cAAW;QACX,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,uCACA,aACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM;gBAC7B,MAAM,SAAS,UAAU,mBAAmB,MAAM,GAAG;gBACrD,MAAM,aAAa,gBAAgB,QAAQ,KAAK,UAAU;gBAC1D,MAAM,SAAS,KAAK,IAAI,KAAK;gBAE7B,qBACE,6LAAC;oBAAiC,WAAU;;wBAEzC,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;4BACX,WAAU;4BACV,eAAY;;;;;;wBAKf,2BACC,6LAAC;4BAAK,WAAU;sCAAqB;;;;;mCACnC,SACF,aAAa;sCACb,6LAAC;4BACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,kCACA,wBACA,sBACA;4BAEF,gBAAa;;gCAEZ,wBAAU,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAC3B,6LAAC;oCAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;8CAET,KAAK,IAAI;;;;;;;;;;;mCAId,WAAW;sCACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,0CACA,wBACA,4BACA,+BACA,+BACA;;oCAGD,wBAAU,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAC3B,6LAAC;wCAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;kDAET,KAAK,IAAI;;;;;;;;;;;;;;;;;;mBArDX,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;YA4DpC;;;;;;;;;;;AAIR;;AAEA,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 2719, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/knowledge/KnowledgePageClient.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - KnowledgePageClient客户端组件\n// 处理知识库首页的交互功能和状态管理\n\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport {\n  Clock,\n  Star,\n  ArrowRight\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { useKnowledgeData } from '@/hooks/useKnowledgeData';\nimport { KnowledgeItem } from '@/lib/types';\nimport KnowledgeCard from '@/components/knowledge/KnowledgeCard';\nimport IntelligentSearch from '@/components/knowledge/IntelligentSearch';\nimport Breadcrumb from '@/components/ui/Breadcrumb';\nimport { StateWrapper } from '@/components/ui/StateComponents';\n\nexport default function KnowledgePageClient() {\n  const router = useRouter();\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // 使用 useKnowledgeData Hook 获取真实数据\n  const {\n    data: dbArticles,\n    loading,\n    error,\n    retry\n  } = useKnowledgeData('articles', {\n    includeCodeExamples: true,\n    includeRelated: true\n  }, {\n    autoFetch: true, // 启用自动获取\n    cacheTime: 5 * 60 * 1000, // 5分钟缓存\n    progressiveLoading: true, // 启用渐进加载\n    prioritizeBasicData: true, // 优先加载基础数据\n    debug: process.env.NODE_ENV === 'development'\n  });\n\n  // 数据处理 - useKnowledgeData 已经返回了正确格式的数据\n  const frontendArticles = useMemo(() => {\n    if (!dbArticles || dbArticles.length === 0) return [];\n    return dbArticles as KnowledgeItem[];\n  }, [dbArticles]);\n\n  // 计算热门文章（基于相关项目数量）\n  const popularItems = useMemo(() => {\n    return frontendArticles\n      .sort((a, b) => a.order_index - b.order_index)\n      .slice(0, 6);\n  }, [frontendArticles]);\n\n  // 计算最近更新的文章\n  const recentItems = useMemo(() => {\n    return frontendArticles\n      .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())\n      .slice(0, 6);\n  }, [frontendArticles]);\n\n  // 页面加载状态管理\n  useEffect(() => {\n    if (!loading) {\n      setIsLoaded(true);\n    }\n  }, [loading]);\n\n  // 处理知识点选择\n  const handleKnowledgeSelect = (item: KnowledgeItem) => {\n    // 跳转到知识点详情页面\n    router.push(`/knowledge/${item.category_id}/${item.id}`);\n  };\n\n  // 处理搜索结果选择\n  const handleSearchResultSelect = (result: any) => {\n    // 跳转到文章详情页面\n    router.push(`/knowledge/${result.id}`);\n  };\n\n  // 模拟分类和标签数据（实际项目中应该从API获取）\n  const searchCategories = [\n    { value: '1', label: 'MySQL基础', count: 25 },\n    { value: '2', label: '查询优化', count: 18 },\n    { value: '3', label: '索引设计', count: 22 },\n    { value: '4', label: '存储引擎', count: 15 },\n    { value: '5', label: '备份恢复', count: 12 }\n  ];\n\n  const searchTags = [\n    { value: 'select', label: 'SELECT查询', count: 45 },\n    { value: 'index', label: '索引优化', count: 38 },\n    { value: 'performance', label: '性能调优', count: 32 },\n    { value: 'innodb', label: 'InnoDB', count: 28 },\n    { value: 'backup', label: '数据备份', count: 20 },\n    { value: 'replication', label: '主从复制', count: 15 },\n    { value: 'transaction', label: '事务处理', count: 25 },\n    { value: 'lock', label: '锁机制', count: 18 }\n  ];\n\n  return (\n    <div className=\"flex flex-col min-h-full bg-white\" data-testid=\"knowledge-page\">\n      {/* 面包屑导航 */}\n      <div className=\"flex-shrink-0 border-b border-mysql-border bg-white\">\n        <div className=\"px-6 py-4\">\n          <Breadcrumb pathname=\"/knowledge\" />\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"px-6 py-8\">\n          <StateWrapper\n            loading={loading}\n            error={error || undefined}\n            isEmpty={!loading && !error && popularItems.length === 0 && recentItems.length === 0}\n            loadingProps={{\n              message: '正在加载知识库数据...',\n              variant: 'skeleton',\n              itemCount: 6\n            }}\n            errorProps={{\n              title: '加载失败',\n              error: error || '无法加载知识库数据，请检查网络连接或稍后重试',\n              onRetry: retry,\n              variant: 'network'\n            }}\n            emptyProps={{\n              title: '暂无知识库内容',\n              message: '知识库正在建设中，敬请期待更多精彩内容',\n              action: {\n                label: '刷新页面',\n                onClick: () => window.location.reload()\n              }\n            }}\n          >\n            {/* 智能搜索区域 */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}\n              transition={{ duration: 0.6, delay: 0.1, ease: \"easeOut\" }}\n              className=\"mb-12\"\n            >\n              <div className=\"text-center mb-8\">\n                <h1 className=\"text-4xl font-bold text-mysql-text mb-4\">\n                  MySQL 知识库\n                </h1>\n                <p className=\"text-lg text-mysql-text-light max-w-2xl mx-auto\">\n                  探索全面的MySQL知识体系，从基础概念到高级优化技巧\n                </p>\n              </div>\n\n              <IntelligentSearch\n                placeholder=\"搜索MySQL知识点、教程、最佳实践...\"\n                autoFocus={false}\n                showAdvancedFilters={true}\n                showSearchHistory={true}\n                maxResults={20}\n                onResultSelect={handleSearchResultSelect}\n                categories={searchCategories}\n                availableTags={searchTags}\n                className=\"max-w-4xl mx-auto\"\n              />\n            </motion.div>\n\n            {/* 热门推荐区域 */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}\n              transition={{ duration: 0.6, delay: 0.2, ease: \"easeOut\" }}\n              className=\"mb-12\"\n              data-testid=\"popular-articles\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <Star className=\"w-6 h-6 text-mysql-primary mr-3\" />\n                <h2 className=\"text-2xl font-bold text-mysql-text\">\n                  热门推荐\n                </h2>\n              </div>\n              <p className=\"text-mysql-text-light mb-8\">\n                最受欢迎的MySQL知识点，快速提升你的技能\n              </p>\n\n              {popularItems.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {popularItems.map((item, index) => (\n                    <motion.div\n                      key={item.id}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1, ease: \"easeOut\" }}\n                      data-testid=\"popular-article\"\n                    >\n                      <KnowledgeCard\n                        item={item}\n                        displayMode=\"grid\"\n                        onClick={() => handleKnowledgeSelect(item)}\n                      />\n                    </motion.div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-mysql-text-light\">暂无热门推荐内容</p>\n                </div>\n              )}\n\n                {/* 查看更多按钮 */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                  transition={{ duration: 0.6, delay: 0.5, ease: \"easeOut\" }}\n                  className=\"text-center mt-8\"\n                >\n                  <button\n                    type=\"button\"\n                    onClick={() => router.push('/knowledge/categories')}\n                    className={cn(\n                      'inline-flex items-center px-6 py-3 rounded-lg',\n                      'bg-mysql-primary text-white border border-mysql-primary',\n                      'hover:bg-mysql-primary-dark hover:border-mysql-primary-dark',\n                      'transition-all duration-200 ease-out',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    <span className=\"mr-2\">查看所有知识点</span>\n                    <ArrowRight className=\"w-4 h-4\" />\n                  </button>\n                </motion.div>\n              </motion.div>\n\n              {/* 最近更新区域 */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}\n                transition={{ duration: 0.6, delay: 0.6, ease: \"easeOut\" }}\n                data-testid=\"recent-articles\"\n              >\n                <div className=\"flex items-center mb-6\">\n                  <Clock className=\"w-6 h-6 text-mysql-primary mr-3\" />\n                  <h2 className=\"text-2xl font-bold text-mysql-text\">\n                    最近更新\n                  </h2>\n                </div>\n                <p className=\"text-mysql-text-light mb-8\">\n                  最新添加和更新的MySQL知识内容\n                </p>\n\n                {recentItems.length > 0 ? (\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                    {recentItems.map((item, index) => (\n                      <motion.div\n                        key={item.id}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}\n                        transition={{ duration: 0.4, delay: 0.7 + index * 0.1, ease: \"easeOut\" }}\n                        data-testid=\"recent-article\"\n                      >\n                        <KnowledgeCard\n                          item={item}\n                          displayMode=\"grid\"\n                          onClick={() => handleKnowledgeSelect(item)}\n                        />\n                      </motion.div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-mysql-text-light\">暂无最近更新内容</p>\n                  </div>\n                )}\n              </motion.div>\n          </StateWrapper>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAwCW;;AAtCX,wCAAwC;AACxC,oBAAoB;AAEpB;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAEA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,kCAAkC;IAClC,MAAM,EACJ,MAAM,UAAU,EAChB,OAAO,EACP,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;QAC/B,qBAAqB;QACrB,gBAAgB;IAClB,GAAG;QACD,WAAW;QACX,WAAW,IAAI,KAAK;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,OAAO,oDAAyB;IAClC;IAEA,uCAAuC;IACvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YAC/B,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG,OAAO,EAAE;YACrD,OAAO;QACT;wDAAG;QAAC;KAAW;IAEf,mBAAmB;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAC3B,OAAO,iBACJ,IAAI;6DAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;4DAC5C,KAAK,CAAC,GAAG;QACd;oDAAG;QAAC;KAAiB;IAErB,YAAY;IACZ,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC1B,OAAO,iBACJ,IAAI;4DAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;2DACpF,KAAK,CAAC,GAAG;QACd;mDAAG;QAAC;KAAiB;IAErB,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,YAAY;YACd;QACF;wCAAG;QAAC;KAAQ;IAEZ,UAAU;IACV,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;IACzD;IAEA,WAAW;IACX,MAAM,2BAA2B,CAAC;QAChC,YAAY;QACZ,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE;IACvC;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAK,OAAO;YAAW,OAAO;QAAG;QAC1C;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;QACvC;YAAE,OAAO;YAAK,OAAO;YAAQ,OAAO;QAAG;KACxC;IAED,MAAM,aAAa;QACjB;YAAE,OAAO;YAAU,OAAO;YAAY,OAAO;QAAG;QAChD;YAAE,OAAO;YAAS,OAAO;YAAQ,OAAO;QAAG;QAC3C;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAU,OAAO;YAAU,OAAO;QAAG;QAC9C;YAAE,OAAO;YAAU,OAAO;YAAQ,OAAO;QAAG;QAC5C;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAe,OAAO;YAAQ,OAAO;QAAG;QACjD;YAAE,OAAO;YAAQ,OAAO;YAAO,OAAO;QAAG;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;QAAoC,eAAY;;0BAE7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uJAAA,CAAA,UAAU;wBAAC,UAAS;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4JAAA,CAAA,eAAY;wBACX,SAAS;wBACT,OAAO,SAAS;wBAChB,SAAS,CAAC,WAAW,CAAC,SAAS,aAAa,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK;wBACnF,cAAc;4BACZ,SAAS;4BACT,SAAS;4BACT,WAAW;wBACb;wBACA,YAAY;4BACV,OAAO;4BACP,OAAO,SAAS;4BAChB,SAAS;4BACT,SAAS;wBACX;wBACA,YAAY;4BACV,OAAO;4BACP,SAAS;4BACT,QAAQ;gCACN,OAAO;gCACP,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACvC;wBACF;;0CAGA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS,WAAW,IAAI;oCAAG,GAAG,WAAW,IAAI;gCAAG;gCAC3D,YAAY;oCAAE,UAAU;oCAAK,OAAO;oCAAK,MAAM;gCAAU;gCACzD,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA0C;;;;;;0DAGxD,6LAAC;gDAAE,WAAU;0DAAkD;;;;;;;;;;;;kDAKjE,6LAAC,qKAAA,CAAA,UAAiB;wCAChB,aAAY;wCACZ,WAAW;wCACX,qBAAqB;wCACrB,mBAAmB;wCACnB,YAAY;wCACZ,gBAAgB;wCAChB,YAAY;wCACZ,eAAe;wCACf,WAAU;;;;;;;;;;;;0CAKd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS,WAAW,IAAI;oCAAG,GAAG,WAAW,IAAI;gCAAG;gCAC3D,YAAY;oCAAE,UAAU;oCAAK,OAAO;oCAAK,MAAM;gCAAU;gCACzD,WAAU;gCACV,eAAY;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;kDAIrD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;oCAIzC,aAAa,MAAM,GAAG,kBACrB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS,WAAW,IAAI;oDAAG,GAAG,WAAW,IAAI;gDAAG;gDAC3D,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;oDAAK,MAAM;gDAAU;gDACvE,eAAY;0DAEZ,cAAA,6LAAC,iKAAA,CAAA,UAAa;oDACZ,MAAM;oDACN,aAAY;oDACZ,SAAS,IAAM,sBAAsB;;;;;;+CATlC,KAAK,EAAE;;;;;;;;;6DAelB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAKvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS,WAAW,IAAI;4CAAG,GAAG,WAAW,IAAI;wCAAG;wCAC3D,YAAY;4CAAE,UAAU;4CAAK,OAAO;4CAAK,MAAM;wCAAU;wCACzD,WAAU;kDAEV,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iDACA,2DACA,+DACA,wCACA;;8DAGF,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS,WAAW,IAAI;oCAAG,GAAG,WAAW,IAAI;gCAAG;gCAC3D,YAAY;oCAAE,UAAU;oCAAK,OAAO;oCAAK,MAAM;gCAAU;gCACzD,eAAY;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;kDAIrD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;oCAIzC,YAAY,MAAM,GAAG,kBACpB,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS,WAAW,IAAI;oDAAG,GAAG,WAAW,IAAI;gDAAG;gDAC3D,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;oDAAK,MAAM;gDAAU;gDACvE,eAAY;0DAEZ,cAAA,6LAAC,iKAAA,CAAA,UAAa;oDACZ,MAAM;oDACN,aAAY;oDACZ,SAAS,IAAM,sBAAsB;;;;;;+CATlC,KAAK,EAAE;;;;;;;;;6DAelB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GAlQwB;;QACP,qIAAA,CAAA,YAAS;QAUpB,iJAAA,CAAA,mBAAgB;;;KAXE", "debugId": null}}]}