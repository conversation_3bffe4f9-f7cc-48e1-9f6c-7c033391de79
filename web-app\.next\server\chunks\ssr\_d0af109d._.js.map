{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/web-app/src/components/layout/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/web-app/src/components/layout/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/constants.ts"], "sourcesContent": ["// MySQLAi.de - 常量配置文件\n// 包含项目中使用的所有常量、配置和静态数据\n\nimport { ThemeColors, PageMetadata } from './types';\n\n// 网站基本信息\nexport const SITE_CONFIG = {\n  name: 'MySQLAi.de',\n  title: 'MySQL智能分析专家',\n  description: '专业的数据库知识分享与项目管理平台',\n  url: 'https://mysqlai.de',\n  author: 'MySQLAi Team',\n  keywords: ['MySQL', '数据库', 'AI分析', '项目管理', '知识分享', '性能优化'],\n};\n\n// MySQL主题色彩配置\nexport const THEME_COLORS: ThemeColors = {\n  primary: '#00758F',        // MySQL官方蓝\n  primaryDark: '#003545',    // 深蓝色\n  primaryLight: '#E6F3F7',   // 浅蓝色\n  accent: '#0066CC',         // 强调色\n  text: '#2D3748',           // 主文字色\n  textLight: '#718096',      // 浅文字色\n  border: '#E2E8F0',         // 边框色\n  success: '#38A169',        // 成功色\n  warning: '#D69E2E',        // 警告色\n  error: '#E53E3E',          // 错误色\n} as const;\n\n// Chen ER图标准黑色主题配置\nexport const CHEN_ER_COLORS = {\n  primary: '#000000',        // 黑色 - 实体边框\n  primaryDark: '#000000',    // 黑色 - 深色变体\n  primaryLight: '#FFFFFF',   // 白色 - 浅色背景\n  accent: '#000000',         // 黑色 - 属性边框\n  text: '#000000',           // 黑色 - 文字色\n  textLight: '#000000',      // 黑色 - 浅文字色\n  border: '#000000',         // 黑色 - 边框色\n  success: '#000000',        // 黑色 - 关系边框\n  warning: '#000000',        // 黑色 - 主键标记\n  error: '#000000',          // 黑色 - 错误色\n  white: '#FFFFFF',          // 白色 - 填充色\n  background: '#FFFFFF',     // 白色 - 背景色\n} as const;\n\n// 注意：导航菜单配置已移至 @/lib/navigation.ts 中的 MAIN_NAVIGATION\n\n// 页面元数据配置\nexport const PAGE_METADATA: Record<string, PageMetadata> = {\n  home: {\n    title: `${SITE_CONFIG.title} - ${SITE_CONFIG.description}`,\n    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',\n    keywords: [...SITE_CONFIG.keywords, '首页', '主页'],\n  },\n  knowledge: {\n    title: `MySQL知识库 - ${SITE_CONFIG.name}`,\n    description: '丰富的MySQL知识库，包含数据库优化、性能调优、最佳实践等专业内容。',\n    keywords: [...SITE_CONFIG.keywords, '知识库', '教程', '最佳实践'],\n  },\n  projects: {\n    title: `项目管理 - ${SITE_CONFIG.name}`,\n    description: '专业的项目管理工具，支持任务跟踪、进度管理、团队协作。',\n    keywords: [...SITE_CONFIG.keywords, '项目管理', '任务跟踪', '团队协作'],\n  },\n  reports: {\n    title: `报告展示 - ${SITE_CONFIG.name}`,\n    description: '支持图片、视频的多媒体项目报告展示平台。',\n    keywords: [...SITE_CONFIG.keywords, '报告展示', '多媒体', '数据可视化'],\n  },\n  about: {\n    title: `关于我们 - ${SITE_CONFIG.name}`,\n    description: '了解MySQLAi.de团队，我们的使命是为用户提供最专业的MySQL解决方案。',\n    keywords: [...SITE_CONFIG.keywords, '关于我们', '团队介绍', '公司简介'],\n  },\n  contact: {\n    title: `联系我们 - ${SITE_CONFIG.name}`,\n    description: '联系MySQLAi.de团队，获取专业的MySQL咨询和技术支持。',\n    keywords: [...SITE_CONFIG.keywords, '联系我们', '技术支持', '咨询服务'],\n  },\n  // 法律声明页面元数据\n  terms: {\n    title: `服务条款 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台服务使用条款和用户协议，明确用户权利义务，保障双方合法权益。',\n    keywords: [...SITE_CONFIG.keywords, '服务条款', '用户协议', '使用条款', '服务协议', '法律声明'],\n  },\n  privacy: {\n    title: `隐私政策 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台用户隐私保护政策，详细说明个人信息收集、使用、保护措施。',\n    keywords: [...SITE_CONFIG.keywords, '隐私政策', '个人信息保护', '数据保护', '隐私保护', '信息安全'],\n  },\n  disclaimer: {\n    title: `免责声明 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台服务免责条款和责任限制说明，明确服务范围和责任界限。',\n    keywords: [...SITE_CONFIG.keywords, '免责声明', '责任限制', '法律免责', '服务限制', '风险提示'],\n  },\n  cookies: {\n    title: `Cookie政策 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台Cookie使用说明和管理指南，保障用户知情权和选择权。',\n    keywords: [...SITE_CONFIG.keywords, 'Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理', '用户隐私'],\n  },\n  // 工具页面元数据\n  tools: {\n    title: `MySQL工具集 - ${SITE_CONFIG.name}`,\n    description: '专业的MySQL工具集合，包含ER图生成、数据库安装配置等实用工具，提升数据库开发效率。',\n    keywords: [...SITE_CONFIG.keywords, 'MySQL工具', '数据库工具', 'ER图生成', 'MySQL安装', '开发工具'],\n  },\n  'tools-er-diagram': {\n    title: `ER图生成工具 - ${SITE_CONFIG.name}`,\n    description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式，提升数据库设计效率。',\n    keywords: [...SITE_CONFIG.keywords, 'ER图生成', '数据库关系图', '数据库设计', '可视化工具', '数据库建模'],\n  },\n  'tools-mysql-installer': {\n    title: `MySQL安装工具 - ${SITE_CONFIG.name}`,\n    description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置，简化数据库部署流程。',\n    keywords: [...SITE_CONFIG.keywords, 'MySQL安装', '数据库安装', '自动配置', '版本管理', '数据库部署'],\n  },\n} as const;\n\n// 功能特性配置\nexport const FEATURES_DATA = [\n  {\n    title: 'MySQL知识库',\n    description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',\n    icon: 'Database',\n    features: [\n      '数据库性能优化',\n      '查询语句调优',\n      '索引设计最佳实践',\n      '架构设计指南',\n    ],\n  },\n  {\n    title: '项目管理',\n    description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',\n    icon: 'FolderOpen',\n    features: [\n      '任务分配与跟踪',\n      '项目进度管理',\n      '团队协作工具',\n      '时间管理优化',\n    ],\n  },\n  {\n    title: '报告展示',\n    description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',\n    icon: 'BarChart3',\n    features: [\n      '多媒体报告支持',\n      '数据可视化图表',\n      '实时数据展示',\n      '自定义报告模板',\n    ],\n  },\n] as const;\n\n// 专业特性配置\nexport const ABOUT_FEATURES = [\n  {\n    title: '智能分析',\n    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',\n    icon: 'Brain',\n  },\n  {\n    title: '专业咨询',\n    description: '资深数据库专家团队，提供一对一的专业咨询服务。',\n    icon: 'Users',\n  },\n  {\n    title: '高效管理',\n    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',\n    icon: 'Zap',\n  },\n  {\n    title: '透明报告',\n    description: '详细的项目报告和数据分析，确保项目进展透明可控。',\n    icon: 'FileText',\n  },\n  {\n    title: '7x24支持',\n    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',\n    icon: 'Clock',\n  },\n] as const;\n\n// 优势展示配置\nexport const ADVANTAGES_DATA = [\n  {\n    title: '🌍 #1 MySQL专家',\n    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',\n    details: '覆盖全球8个地区，超过5万用户信赖',\n    icon: '🌍',\n  },\n  {\n    title: '📝 兼容性与支持',\n    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',\n    details: '支持MySQL 5.7到8.0的所有主流版本',\n    icon: '📝',\n  },\n  {\n    title: '💰 灵活计费',\n    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',\n    details: '透明计费，性价比最高的MySQL服务',\n    icon: '💰',\n  },\n  {\n    title: '⚡ 全球布局',\n    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',\n    details: '全球用户享受一致的高速服务体验',\n    icon: '⚡',\n  },\n  {\n    title: '⏰ 服务保障',\n    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',\n    details: '专业运维团队，99.9%服务可用性保证',\n    icon: '⏰',\n  },\n  {\n    title: '🎈 透明计费',\n    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',\n    details: '无隐藏费用，按实际使用量计费',\n    icon: '🎈',\n  },\n] as const;\n\n// 联系方式配置\nexport const CONTACT_INFO = {\n  supportHours: '7×24小时全天候支持',\n  email: '<EMAIL>',\n  phone: '+86 ************',\n  address: '中国 · 北京 · 朝阳区',\n  socialLinks: [\n    {\n      name: 'GitHub',\n      href: 'https://github.com/mysqlai',\n      icon: 'Github',\n    },\n    {\n      name: '微信',\n      href: '#',\n      icon: 'MessageCircle',\n    },\n    {\n      name: 'QQ群',\n      href: '#',\n      icon: 'Users',\n    },\n  ],\n} as const;\n\n// 页脚配置\nexport const FOOTER_SECTIONS = [\n  {\n    title: '产品服务',\n    links: [\n      { name: 'MySQL优化', href: '/services/optimization' },\n      { name: '性能调优', href: '/services/tuning' },\n      { name: '架构设计', href: '/services/architecture' },\n      { name: '数据迁移', href: '/services/migration' },\n    ],\n  },\n  {\n    title: '解决方案',\n    links: [\n      { name: '企业级方案', href: '/solutions/enterprise' },\n      { name: '云数据库', href: '/solutions/cloud' },\n      { name: '高可用架构', href: '/solutions/ha' },\n      { name: '灾备方案', href: '/solutions/disaster-recovery' },\n    ],\n  },\n  {\n    title: '学习资源',\n    links: [\n      { name: '技术博客', href: '/blog' },\n      { name: '视频教程', href: '/tutorials' },\n      { name: 'API文档', href: '/docs' },\n      { name: '最佳实践', href: '/best-practices' },\n    ],\n  },\n  {\n    title: '关于我们',\n    links: [\n      { name: '公司介绍', href: '/about' },\n      { name: '团队成员', href: '/team' },\n      { name: '招聘信息', href: '/careers' },\n      { name: '联系我们', href: '/contact' },\n    ],\n  },\n] as const;\n\nexport const FOOTER_LEGAL_LINKS = [\n  { name: '服务条款', href: '/terms' },\n  { name: '隐私政策', href: '/privacy' },\n  { name: '免责声明', href: '/disclaimer' },\n] as const;\n\n// 动画配置\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.3,\n    slow: 0.5,\n  },\n  easing: {\n    easeInOut: [0.4, 0, 0.2, 1],\n    easeOut: [0, 0, 0.2, 1],\n    easeIn: [0.4, 0, 1, 1],\n  },\n  delay: {\n    none: 0,\n    short: 0.1,\n    medium: 0.2,\n    long: 0.3,\n  },\n} as const;\n\n// 响应式断点配置\nexport const BREAKPOINTS = {\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n  '2xl': '1536px',\n} as const;\n\n// 错误消息配置\nexport const ERROR_MESSAGES = {\n  required: '此字段为必填项',\n  email: '请输入有效的邮箱地址',\n  phone: '请输入有效的手机号码',\n  minLength: (min: number) => `最少需要${min}个字符`,\n  maxLength: (max: number) => `最多允许${max}个字符`,\n  network: '网络连接失败，请稍后重试',\n  server: '服务器错误，请联系技术支持',\n  unknown: '未知错误，请稍后重试',\n} as const;\n\n// 成功消息配置\nexport const SUCCESS_MESSAGES = {\n  formSubmit: '表单提交成功！',\n  dataSaved: '数据保存成功！',\n  emailSent: '邮件发送成功！',\n  copied: '已复制到剪贴板',\n} as const;\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,uBAAuB;;;;;;;;;;;;;;;;;AAKhB,MAAM,cAAc;IACzB,MAAM;IACN,OAAO;IACP,aAAa;IACb,KAAK;IACL,QAAQ;IACR,UAAU;QAAC;QAAS;QAAO;QAAQ;QAAQ;QAAQ;KAAO;AAC5D;AAGO,MAAM,eAA4B;IACvC,SAAS;IACT,aAAa;IACb,cAAc;IACd,QAAQ;IACR,MAAM;IACN,WAAW;IACX,QAAQ;IACR,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,SAAS;IACT,aAAa;IACb,cAAc;IACd,QAAQ;IACR,MAAM;IACN,WAAW;IACX,QAAQ;IACR,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,YAAY;AACd;AAKO,MAAM,gBAA8C;IACzD,MAAM;QACJ,OAAO,GAAG,YAAY,KAAK,CAAC,GAAG,EAAE,YAAY,WAAW,EAAE;QAC1D,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAM;SAAK;IACjD;IACA,WAAW;QACT,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAO;YAAM;SAAO;IAC1D;IACA,UAAU;QACR,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAO;SAAQ;IAC7D;IACA,OAAO;QACL,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,YAAY;IACZ,OAAO;QACL,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;IAC7E;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAU;YAAQ;YAAQ;SAAO;IAC/E;IACA,YAAY;QACV,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;IAC7E;IACA,SAAS;QACP,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAY;YAAY;YAAY;YAAY;SAAO;IAC7F;IACA,UAAU;IACV,OAAO;QACL,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAW;YAAS;YAAS;YAAW;SAAO;IACrF;IACA,oBAAoB;QAClB,OAAO,CAAC,UAAU,EAAE,YAAY,IAAI,EAAE;QACtC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAS;YAAU;YAAS;YAAS;SAAQ;IACnF;IACA,yBAAyB;QACvB,OAAO,CAAC,YAAY,EAAE,YAAY,IAAI,EAAE;QACxC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAW;YAAS;YAAQ;YAAQ;SAAQ;IAClF;AACF;AAGO,MAAM,gBAAgB;IAC3B;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,iBAAiB;IAC5B;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,eAAe;IAC1B,cAAc;IACd,OAAO;IACP,OAAO;IACP,SAAS;IACT,aAAa;QACX;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;AACH;AAGO,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAyB;YAClD;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;YACzC;gBAAE,MAAM;gBAAQ,MAAM;YAAyB;YAC/C;gBAAE,MAAM;gBAAQ,MAAM;YAAsB;SAC7C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAS,MAAM;YAAwB;YAC/C;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;YACzC;gBAAE,MAAM;gBAAS,MAAM;YAAgB;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAA+B;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAa;YACnC;gBAAE,MAAM;gBAAS,MAAM;YAAQ;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAkB;SACzC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAW;SAClC;IACH;CACD;AAEM,MAAM,qBAAqB;IAChC;QAAE,MAAM;QAAQ,MAAM;IAAS;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAW;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAc;CACrC;AAGM,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,QAAQ;QACN,WAAW;YAAC;YAAK;YAAG;YAAK;SAAE;QAC3B,SAAS;YAAC;YAAG;YAAG;YAAK;SAAE;QACvB,QAAQ;YAAC;YAAK;YAAG;YAAG;SAAE;IACxB;IACA,OAAO;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,OAAO;IACP,OAAO;IACP,WAAW,CAAC,MAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;IAC3C,WAAW,CAAC,MAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;IAC3C,SAAS;IACT,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,mBAAmB;IAC9B,YAAY;IACZ,WAAW;IACX,WAAW;IACX,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/metadata.ts"], "sourcesContent": ["// MySQLAi.de - SEO元数据配置\n// 专业的SEO优化配置，包含Open Graph、Twitter Cards等\n\nimport { Metadata } from 'next';\nimport { SITE_CONFIG } from '@/lib/constants';\n\n// 基础SEO配置\nexport const baseMetadata: Metadata = {\n  title: {\n    default: SITE_CONFIG.title,\n    template: `%s - ${SITE_CONFIG.name}`,\n  },\n  description: SITE_CONFIG.description,\n  keywords: SITE_CONFIG.keywords,\n  authors: [{ name: SITE_CONFIG.author }],\n  creator: SITE_CONFIG.author,\n  publisher: SITE_CONFIG.name,\n  \n  // 基础元数据\n  metadataBase: new URL(SITE_CONFIG.url),\n  alternates: {\n    canonical: '/',\n    languages: {\n      'zh-CN': '/zh-CN',\n      'en-US': '/en-US',\n    },\n  },\n  \n  // Open Graph配置\n  openGraph: {\n    type: 'website',\n    locale: 'zh_CN',\n    url: SITE_CONFIG.url,\n    title: SITE_CONFIG.title,\n    description: SITE_CONFIG.description,\n    siteName: SITE_CONFIG.name,\n    images: [\n      {\n        url: '/og-image.png',\n        width: 1200,\n        height: 630,\n        alt: `${SITE_CONFIG.name} - ${SITE_CONFIG.title}`,\n      },\n      {\n        url: '/og-image-square.png',\n        width: 1200,\n        height: 1200,\n        alt: `${SITE_CONFIG.name} Logo`,\n      },\n    ],\n  },\n  \n  // Twitter Cards配置\n  twitter: {\n    card: 'summary_large_image',\n    title: SITE_CONFIG.title,\n    description: SITE_CONFIG.description,\n    creator: '@mysqlai',\n    site: '@mysqlai',\n    images: ['/twitter-image.png'],\n  },\n  \n  // 应用程序配置\n  applicationName: SITE_CONFIG.name,\n  appleWebApp: {\n    capable: true,\n    title: SITE_CONFIG.name,\n    statusBarStyle: 'default',\n  },\n  \n  // 格式检测\n  formatDetection: {\n    telephone: false,\n    date: false,\n    address: false,\n    email: false,\n    url: false,\n  },\n  \n  // 图标配置\n  icons: {\n    icon: '/favicon.ico',\n  },\n  \n  // 清单文件\n  manifest: '/site.webmanifest',\n  \n  // 其他元数据\n  other: {\n    'msapplication-TileColor': '#00758F',\n    'msapplication-config': '/browserconfig.xml',\n    'theme-color': '#00758F',\n  },\n};\n\n// 首页专用元数据\nexport const homeMetadata: Metadata = {\n  ...baseMetadata,\n  title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,\n  description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。7×24小时专业技术支持。',\n  keywords: [\n    ...SITE_CONFIG.keywords,\n    'MySQL优化',\n    '数据库性能',\n    '智能分析',\n    'AI驱动',\n    '项目管理',\n    '报告展示',\n    '技术支持',\n    '企业级',\n    '专业服务',\n  ],\n  openGraph: {\n    ...baseMetadata.openGraph,\n    title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,\n    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',\n    url: SITE_CONFIG.url,\n  },\n  twitter: {\n    ...baseMetadata.twitter,\n    title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,\n    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',\n  },\n};\n\n// 结构化数据配置\nexport const structuredData = {\n  '@context': 'https://schema.org',\n  '@type': 'Organization',\n  name: SITE_CONFIG.name,\n  alternateName: SITE_CONFIG.title,\n  url: SITE_CONFIG.url,\n  logo: `${SITE_CONFIG.url}/logo.png`,\n  description: SITE_CONFIG.description,\n  foundingDate: '2020',\n  founder: {\n    '@type': 'Person',\n    name: 'MySQLAi Team',\n  },\n  contactPoint: {\n    '@type': 'ContactPoint',\n    telephone: '+86-************',\n    contactType: 'customer service',\n    availableLanguage: ['Chinese', 'English'],\n    areaServed: 'CN',\n    hoursAvailable: {\n      '@type': 'OpeningHoursSpecification',\n      dayOfWeek: [\n        'Monday',\n        'Tuesday', \n        'Wednesday',\n        'Thursday',\n        'Friday',\n        'Saturday',\n        'Sunday'\n      ],\n      opens: '00:00',\n      closes: '23:59',\n    },\n  },\n  address: {\n    '@type': 'PostalAddress',\n    addressLocality: '北京市',\n    addressRegion: '朝阳区',\n    addressCountry: 'CN',\n    streetAddress: '科技园区',\n  },\n  sameAs: [\n    'https://github.com/mysqlai',\n    'https://twitter.com/mysqlai',\n    'https://linkedin.com/company/mysqlai',\n  ],\n  offers: {\n    '@type': 'Offer',\n    category: 'Database Services',\n    description: 'MySQL数据库优化和管理服务',\n    areaServed: 'CN',\n  },\n  knowsAbout: [\n    'MySQL',\n    'Database Optimization',\n    'Performance Tuning',\n    'Project Management',\n    'AI Analysis',\n    'Technical Support',\n  ],\n  serviceType: [\n    'MySQL知识库',\n    '项目管理',\n    '报告展示',\n    'AI智能分析',\n    '技术支持',\n  ],\n};\n\n// 网站配置\nexport const siteConfig = {\n  name: SITE_CONFIG.name,\n  title: SITE_CONFIG.title,\n  description: SITE_CONFIG.description,\n  url: SITE_CONFIG.url,\n  ogImage: `${SITE_CONFIG.url}/og-image.png`,\n  links: {\n    twitter: 'https://twitter.com/mysqlai',\n    github: 'https://github.com/mysqlai',\n    linkedin: 'https://linkedin.com/company/mysqlai',\n  },\n  creator: SITE_CONFIG.author,\n};\n\n// 生成页面特定的元数据\nexport function generatePageMetadata(\n  title: string,\n  description: string,\n  path: string = '',\n  image?: string\n): Metadata {\n  const url = `${SITE_CONFIG.url}${path}`;\n  const ogImage = image || '/og-image.png';\n\n  return {\n    title,\n    description,\n    openGraph: {\n      title,\n      description,\n      url,\n      images: [\n        {\n          url: ogImage,\n          width: 1200,\n          height: 630,\n          alt: title,\n        },\n      ],\n    },\n    twitter: {\n      title,\n      description,\n      images: [ogImage],\n    },\n    alternates: {\n      canonical: url,\n    },\n  };\n}\n\n// 生成JSON-LD结构化数据\nexport function generateJsonLd(data: Record<string, unknown>) {\n  return {\n    __html: JSON.stringify(data),\n  };\n}\n"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,yCAAyC;;;;;;;;;AAGzC;;AAGO,MAAM,eAAyB;IACpC,OAAO;QACL,SAAS,qIAAA,CAAA,cAAW,CAAC,KAAK;QAC1B,UAAU,CAAC,KAAK,EAAE,qIAAA,CAAA,cAAW,CAAC,IAAI,EAAE;IACtC;IACA,aAAa,qIAAA,CAAA,cAAW,CAAC,WAAW;IACpC,UAAU,qIAAA,CAAA,cAAW,CAAC,QAAQ;IAC9B,SAAS;QAAC;YAAE,MAAM,qIAAA,CAAA,cAAW,CAAC,MAAM;QAAC;KAAE;IACvC,SAAS,qIAAA,CAAA,cAAW,CAAC,MAAM;IAC3B,WAAW,qIAAA,CAAA,cAAW,CAAC,IAAI;IAE3B,QAAQ;IACR,cAAc,IAAI,IAAI,qIAAA,CAAA,cAAW,CAAC,GAAG;IACrC,YAAY;QACV,WAAW;QACX,WAAW;YACT,SAAS;YACT,SAAS;QACX;IACF;IAEA,eAAe;IACf,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,qIAAA,CAAA,cAAW,CAAC,GAAG;QACpB,OAAO,qIAAA,CAAA,cAAW,CAAC,KAAK;QACxB,aAAa,qIAAA,CAAA,cAAW,CAAC,WAAW;QACpC,UAAU,qIAAA,CAAA,cAAW,CAAC,IAAI;QAC1B,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,qIAAA,CAAA,cAAW,CAAC,IAAI,CAAC,GAAG,EAAE,qIAAA,CAAA,cAAW,CAAC,KAAK,EAAE;YACnD;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,qIAAA,CAAA,cAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC;SACD;IACH;IAEA,kBAAkB;IAClB,SAAS;QACP,MAAM;QACN,OAAO,qIAAA,CAAA,cAAW,CAAC,KAAK;QACxB,aAAa,qIAAA,CAAA,cAAW,CAAC,WAAW;QACpC,SAAS;QACT,MAAM;QACN,QAAQ;YAAC;SAAqB;IAChC;IAEA,SAAS;IACT,iBAAiB,qIAAA,CAAA,cAAW,CAAC,IAAI;IACjC,aAAa;QACX,SAAS;QACT,OAAO,qIAAA,CAAA,cAAW,CAAC,IAAI;QACvB,gBAAgB;IAClB;IAEA,OAAO;IACP,iBAAiB;QACf,WAAW;QACX,MAAM;QACN,SAAS;QACT,OAAO;QACP,KAAK;IACP;IAEA,OAAO;IACP,OAAO;QACL,MAAM;IACR;IAEA,OAAO;IACP,UAAU;IAEV,QAAQ;IACR,OAAO;QACL,2BAA2B;QAC3B,wBAAwB;QACxB,eAAe;IACjB;AACF;AAGO,MAAM,eAAyB;IACpC,GAAG,YAAY;IACf,OAAO,GAAG,qIAAA,CAAA,cAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC;IAC9C,aAAa;IACb,UAAU;WACL,qIAAA,CAAA,cAAW,CAAC,QAAQ;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,GAAG,aAAa,SAAS;QACzB,OAAO,GAAG,qIAAA,CAAA,cAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAC9C,aAAa;QACb,KAAK,qIAAA,CAAA,cAAW,CAAC,GAAG;IACtB;IACA,SAAS;QACP,GAAG,aAAa,OAAO;QACvB,OAAO,GAAG,qIAAA,CAAA,cAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAC9C,aAAa;IACf;AACF;AAGO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,SAAS;IACT,MAAM,qIAAA,CAAA,cAAW,CAAC,IAAI;IACtB,eAAe,qIAAA,CAAA,cAAW,CAAC,KAAK;IAChC,KAAK,qIAAA,CAAA,cAAW,CAAC,GAAG;IACpB,MAAM,GAAG,qIAAA,CAAA,cAAW,CAAC,GAAG,CAAC,SAAS,CAAC;IACnC,aAAa,qIAAA,CAAA,cAAW,CAAC,WAAW;IACpC,cAAc;IACd,SAAS;QACP,SAAS;QACT,MAAM;IACR;IACA,cAAc;QACZ,SAAS;QACT,WAAW;QACX,aAAa;QACb,mBAAmB;YAAC;YAAW;SAAU;QACzC,YAAY;QACZ,gBAAgB;YACd,SAAS;YACT,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,QAAQ;QACV;IACF;IACA,SAAS;QACP,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,eAAe;IACjB;IACA,QAAQ;QACN;QACA;QACA;KACD;IACD,QAAQ;QACN,SAAS;QACT,UAAU;QACV,aAAa;QACb,YAAY;IACd;IACA,YAAY;QACV;QACA;QACA;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,aAAa;IACxB,MAAM,qIAAA,CAAA,cAAW,CAAC,IAAI;IACtB,OAAO,qIAAA,CAAA,cAAW,CAAC,KAAK;IACxB,aAAa,qIAAA,CAAA,cAAW,CAAC,WAAW;IACpC,KAAK,qIAAA,CAAA,cAAW,CAAC,GAAG;IACpB,SAAS,GAAG,qIAAA,CAAA,cAAW,CAAC,GAAG,CAAC,aAAa,CAAC;IAC1C,OAAO;QACL,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA,SAAS,qIAAA,CAAA,cAAW,CAAC,MAAM;AAC7B;AAGO,SAAS,qBACd,KAAa,EACb,WAAmB,EACnB,OAAe,EAAE,EACjB,KAAc;IAEd,MAAM,MAAM,GAAG,qIAAA,CAAA,cAAW,CAAC,GAAG,GAAG,MAAM;IACvC,MAAM,UAAU,SAAS;IAEzB,OAAO;QACL;QACA;QACA,WAAW;YACT;YACA;YACA;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,QAAQ;gBAAC;aAAQ;QACnB;QACA,YAAY;YACV,WAAW;QACb;IACF;AACF;AAGO,SAAS,eAAe,IAA6B;IAC1D,OAAO;QACL,QAAQ,KAAK,SAAS,CAAC;IACzB;AACF", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\nimport { homeMetadata, structuredData, generateJsonLd } from \"./metadata\";\n\n// 字体配置已移至globals.css\n\nexport const metadata: Metadata = homeMetadata;\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\" className=\"scroll-smooth\">\n      <head>\n        {/* 结构化数据 */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={generateJsonLd(structuredData)}\n        />\n        {/* 预连接到外部资源 - 移除Google Fonts */}\n        {/* DNS预取 */}\n        <link rel=\"dns-prefetch\" href=\"//github.com\" />\n        <link rel=\"dns-prefetch\" href=\"//twitter.com\" />\n        <link rel=\"dns-prefetch\" href=\"//linkedin.com\" />\n      </head>\n      <body\n        className=\"antialiased bg-white text-mysql-text\"\n        suppressHydrationWarning={true}\n      >\n        <Header />\n        <main className=\"pt-16 lg:pt-20 relative\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;;;;AAIO,MAAM,WAAqB,oIAAA,CAAA,eAAY;AAE/B,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAQ,WAAU;;0BAC3B,8OAAC;;kCAEC,8OAAC;wBACC,MAAK;wBACL,yBAAyB,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAAE,oIAAA,CAAA,iBAAc;;;;;;kCAIxD,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;;;;;;;0BAEhC,8OAAC;gBACC,WAAU;gBACV,0BAA0B;;kCAE1B,8OAAC,oJAAA,CAAA,UAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,oJAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}