{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Breadcrumb面包屑导航组件\n// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getBreadcrumbs } from '@/lib/navigation';\nimport { BreadcrumbProps } from '@/lib/types';\n\nconst Breadcrumb: React.FC<BreadcrumbProps> = React.memo(({\n  pathname,\n  maxItems = 5,\n  className,\n  ...props\n}) => {\n  // 获取面包屑数据\n  const breadcrumbs = React.useMemo(() => getBreadcrumbs(pathname), [pathname]);\n\n  // 如果只有首页，不显示面包屑\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  // 处理超长路径的截断\n  const displayBreadcrumbs = breadcrumbs.length > maxItems\n    ? [\n        breadcrumbs[0], // 首页\n        { name: '...', href: '#', isEllipsis: true },\n        ...breadcrumbs.slice(-2) // 最后两项\n      ]\n    : breadcrumbs;\n\n  return (\n    <motion.nav\n      initial={{ opacity: 0, y: -10 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n      aria-label=\"面包屑导航\"\n      className={cn(\n        'flex items-center space-x-1 text-sm',\n        'py-2 px-1',\n        className\n      )}\n      {...props}\n    >\n      <ol className=\"flex items-center space-x-1 md:space-x-2\">\n        {displayBreadcrumbs.map((item, index) => {\n          const isLast = index === displayBreadcrumbs.length - 1;\n          const isEllipsis = 'isEllipsis' in item && item.isEllipsis;\n          const isHome = item.href === '/';\n\n          return (\n            <li key={`${item.href}-${index}`} className=\"flex items-center\">\n              {/* 分隔符 */}\n              {index > 0 && (\n                <ChevronRight \n                  className=\"w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0\" \n                  aria-hidden=\"true\"\n                />\n              )}\n\n              {/* 面包屑项 */}\n              {isEllipsis ? (\n                <span className=\"text-gray-400 px-1\">...</span>\n              ) : isLast ? (\n                // 当前页面（不可点击）\n                <span\n                  className={cn(\n                    'font-medium text-mysql-primary',\n                    'px-2 py-1 rounded-md',\n                    'bg-mysql-primary/5',\n                    'flex items-center space-x-1'\n                  )}\n                  aria-current=\"page\"\n                >\n                  {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                  <span className={cn(\n                    'truncate max-w-[80px] md:max-w-[120px]',\n                    isHome && 'hidden md:inline'\n                  )}>\n                    {item.name}\n                  </span>\n                </span>\n              ) : (\n                // 可点击的面包屑项\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'text-gray-600 hover:text-mysql-primary',\n                      'px-2 py-1 rounded-md',\n                      'hover:bg-mysql-primary/5',\n                      'transition-all duration-200',\n                      'flex items-center space-x-1',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                    <span className={cn(\n                      'truncate max-w-[80px] md:max-w-[120px]',\n                      isHome && 'hidden md:inline'\n                    )}>\n                      {item.name}\n                    </span>\n                  </Link>\n                </motion.div>\n              )}\n            </li>\n          );\n        })}\n      </ol>\n    </motion.nav>\n  );\n});\n\nBreadcrumb.displayName = 'Breadcrumb';\n\nexport default Breadcrumb;\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,mCAAmC;AAEnC;AACA;AACA;AACA;AAAA;AACA;AACA;AAVA;;;;;;;;AAaA,MAAM,2BAAwC,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EACxD,QAAQ,EACR,WAAW,CAAC,EACZ,SAAS,EACT,GAAG,OACJ;IACC,UAAU;IACV,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QAAC;KAAS;IAE5E,gBAAgB;IAChB,IAAI,YAAY,MAAM,IAAI,GAAG;QAC3B,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,qBAAqB,YAAY,MAAM,GAAG,WAC5C;QACE,WAAW,CAAC,EAAE;QACd;YAAE,MAAM;YAAO,MAAM;YAAK,YAAY;QAAK;WACxC,YAAY,KAAK,CAAC,CAAC,GAAG,OAAO;KACjC,GACD;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,cAAW;QACX,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,uCACA,aACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM;gBAC7B,MAAM,SAAS,UAAU,mBAAmB,MAAM,GAAG;gBACrD,MAAM,aAAa,gBAAgB,QAAQ,KAAK,UAAU;gBAC1D,MAAM,SAAS,KAAK,IAAI,KAAK;gBAE7B,qBACE,8OAAC;oBAAiC,WAAU;;wBAEzC,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;4BACX,WAAU;4BACV,eAAY;;;;;;wBAKf,2BACC,8OAAC;4BAAK,WAAU;sCAAqB;;;;;mCACnC,SACF,aAAa;sCACb,8OAAC;4BACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,kCACA,wBACA,sBACA;4BAEF,gBAAa;;gCAEZ,wBAAU,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;8CAET,KAAK,IAAI;;;;;;;;;;;mCAId,WAAW;sCACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0CACA,wBACA,4BACA,+BACA,+BACA;;oCAGD,wBAAU,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;kDAET,KAAK,IAAI;;;;;;;;;;;;;;;;;;mBArDX,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;YA4DpC;;;;;;;;;;;AAIR;AAEA,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/ContactPageLayout.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - ContactPageLayout联系页面布局组件\n// 参考LegalPageLayout设计，为联系我们页面提供统一的布局结构和视觉风格\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, Phone, Mail, MessageCircle, ChevronRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Breadcrumb from '@/components/ui/Breadcrumb';\n\ninterface ContactPageLayoutProps {\n  title: string;\n  description: string;\n  pathname: string;\n  className?: string;\n}\n\n// 联系方式配置\nconst contactMethods = [\n  {\n    id: 'phone',\n    title: '电话支持',\n    contact: '+86 ************',\n    icon: Phone,\n    available: '7×24小时',\n    href: 'tel:+8640088899999',\n    description: '紧急问题请直接拨打，我们的专家团队将在5分钟内响应',\n  },\n  {\n    id: 'email',\n    title: '邮件支持',\n    contact: '<EMAIL>',\n    icon: Mail,\n    available: '24小时内回复',\n    href: 'mailto:<EMAIL>',\n    description: '详细技术咨询和解决方案，我们会在24小时内回复',\n  },\n  {\n    id: 'chat',\n    title: '在线客服',\n    contact: '点击开始对话',\n    icon: MessageCircle,\n    available: '工作日 9:00-18:00',\n    href: '#',\n    description: '实时沟通，快速响应您的技术需求和问题',\n  },\n];\n\n// 相关页面链接\nconst relatedPages = [\n  {\n    title: '服务条款',\n    description: '了解我们的服务条款和用户协议',\n    href: '/terms',\n  },\n  {\n    title: '隐私政策',\n    description: '查看我们的隐私保护政策',\n    href: '/privacy',\n  },\n];\n\nconst ContactPageLayout: React.FC<ContactPageLayoutProps> = React.memo(({\n  title,\n  description,\n  pathname,\n  className,\n  ...props\n}) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n      className={cn(\n        'min-h-screen bg-gradient-to-br from-gray-50 to-white',\n        className\n      )}\n      {...props}\n    >\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        \n        {/* 面包屑导航 */}\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1, duration: 0.3 }}\n          className=\"mb-8\"\n        >\n          <Breadcrumb pathname={pathname} />\n        </motion.div>\n\n        {/* 返回按钮 */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.2, duration: 0.3 }}\n          className=\"mb-8\"\n        >\n          <Link\n            href=\"/\"\n            className={cn(\n              'inline-flex items-center space-x-2',\n              'text-mysql-primary hover:text-mysql-primary-dark',\n              'transition-colors duration-200',\n              'group'\n            )}\n          >\n            <ArrowLeft className=\"w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200\" />\n            <span className=\"text-sm font-medium\">返回首页</span>\n          </Link>\n        </motion.div>\n\n        {/* 页面标题区域 */}\n        <motion.header\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2, duration: 0.4 }}\n          className=\"mb-12\"\n        >\n          <div className=\"text-center\">\n            <h1 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              {title}\n            </h1>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              {description}\n            </p>\n          </div>\n        </motion.header>\n\n        {/* 主要内容区域 */}\n        <motion.main\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3, duration: 0.4 }}\n          className={cn(\n            'bg-white rounded-xl shadow-lg',\n            'border border-gray-200',\n            'p-6 md:p-8 lg:p-12',\n            'mb-12'\n          )}\n        >\n          {/* 联系方式网格 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n            {contactMethods.map((method, index) => {\n              const IconComponent = method.icon;\n\n              return (\n                <motion.div\n                  key={method.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 + index * 0.1, duration: 0.3 }}\n                >\n                  <a\n                    href={method.href}\n                    className={cn(\n                      'block p-6 rounded-lg border border-gray-200',\n                      'hover:border-mysql-primary hover:shadow-md',\n                      'transition-all duration-200',\n                      'group h-full'\n                    )}\n                  >\n                    {/* 图标区域 */}\n                    <div className=\"flex items-center justify-center mb-4\">\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-mysql-primary rounded-lg group-hover:scale-110 transition-transform duration-200\">\n                        <IconComponent className=\"w-6 h-6 text-white\" />\n                      </div>\n                    </div>\n\n                    {/* 内容区域 */}\n                    <div className=\"text-center\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-mysql-primary transition-colors duration-200\">\n                        {method.title}\n                      </h3>\n                      <div className=\"text-mysql-primary font-medium mb-1\">\n                        {method.contact}\n                      </div>\n                      <div className=\"text-sm text-gray-500 mb-3\">\n                        {method.available}\n                      </div>\n                      <p className=\"text-sm text-gray-600 leading-relaxed\">\n                        {method.description}\n                      </p>\n                    </div>\n                  </a>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          {/* 技术支持承诺 */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.7, duration: 0.4 }}\n            className=\"bg-gradient-to-r from-mysql-primary/5 to-mysql-primary-dark/5 rounded-lg p-6 text-center\"\n          >\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n              专业技术支持承诺\n            </h3>\n            <p className=\"text-gray-700 leading-relaxed\">\n              我们拥有15年+数据库经验的专家团队，为您提供最专业的MySQL解决方案。\n              紧急问题5分钟内响应，一般咨询24小时内回复，全年无休的技术支持。\n            </p>\n          </motion.div>\n        </motion.main>\n\n        {/* 相关页面导航 */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4, duration: 0.4 }}\n          className=\"mb-12\"\n        >\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n            相关信息\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {relatedPages.map((page) => (\n              <motion.div\n                key={page.href}\n                whileHover={{ scale: 1.02, y: -2 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Link\n                  href={page.href}\n                  className={cn(\n                    'block p-4 rounded-lg border border-gray-200',\n                    'bg-white hover:bg-gray-50',\n                    'transition-all duration-200',\n                    'group'\n                  )}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"font-medium text-gray-900 group-hover:text-mysql-primary transition-colors duration-200\">\n                        {page.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mt-1\">\n                        {page.description}\n                      </p>\n                    </div>\n                    <ChevronRight className=\"w-5 h-5 text-gray-400 group-hover:text-mysql-primary group-hover:translate-x-1 transition-all duration-200\" />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* 底部提示信息 */}\n        <motion.footer\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5, duration: 0.4 }}\n          className={cn(\n            'text-center py-8 px-6',\n            'bg-gray-50 rounded-lg',\n            'border border-gray-200'\n          )}\n        >\n          <p className=\"text-gray-600 text-sm leading-relaxed\">\n            我们致力于为每一位用户提供最优质的MySQL技术支持服务。\n            如有任何疑问，请随时通过以上方式与我们联系。\n          </p>\n          <p className=\"text-gray-500 text-xs mt-2\">\n            MySQLAi.de - 您身边的MySQL专家\n          </p>\n        </motion.footer>\n      </div>\n    </motion.div>\n  );\n});\n\nContactPageLayout.displayName = 'ContactPageLayout';\n\nexport default ContactPageLayout;\n"], "names": [], "mappings": ";;;;AAEA,yCAAyC;AACzC,4CAA4C;AAE5C;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAVA;;;;;;;;AAmBA,SAAS;AACT,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM,oMAAA,CAAA,QAAK;QACX,WAAW;QACX,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM,kMAAA,CAAA,OAAI;QACV,WAAW;QACX,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,MAAM,wNAAA,CAAA,gBAAa;QACnB,WAAW;QACX,MAAM;QACN,aAAa;IACf;CACD;AAED,SAAS;AACT,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAED,MAAM,kCAAsD,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EACtE,KAAK,EACL,WAAW,EACX,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,oJAAA,CAAA,UAAU;wBAAC,UAAU;;;;;;;;;;;8BAIxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sCACA,oDACA,kCACA;;0CAGF,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;8BAK1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAMP,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iCACA,0BACA,sBACA;;sCAIF,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ;gCAC3B,MAAM,gBAAgB,OAAO,IAAI;gCAEjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;wCAAK,UAAU;oCAAI;8CAEtD,cAAA,8OAAC;wCACC,MAAM,OAAO,IAAI;wCACjB,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+CACA,8CACA,+BACA;;0DAIF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAc,WAAU;;;;;;;;;;;;;;;;0DAK7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEACZ,OAAO,SAAS;;;;;;kEAEnB,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAjCpB,OAAO,EAAE;;;;;4BAuCpB;;;;;;sCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAQjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;oBACb,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+CACA,6BACA,+BACA;kDAGF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;8DAGrB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAtBvB,KAAK,IAAI;;;;;;;;;;;;;;;;8BA+BtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yBACA,yBACA;;sCAGF,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAIrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAEA,kBAAkB,WAAW,GAAG;uCAEjB", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "file": "message-circle.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n];\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('message-circle', __iconNode);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}