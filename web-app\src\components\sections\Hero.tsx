'use client';

// MySQLAi.de - Hero英雄区域组件
// 网站核心价值展示区域，包含主标题、副标题、描述和CTA按钮

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Play, Database, Zap, Shield, GitBranch, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SITE_CONFIG } from '@/lib/constants';
import { HeroProps } from '@/lib/types';

interface HeroComponentProps {
  className?: string;
}

export default function Hero({ className }: HeroComponentProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // 打字机动画文本
  const typewriterText = "MySQL智能分析专家";
  
  return (
    <section
      className={cn(
        'relative min-h-screen flex items-center justify-center overflow-hidden',
        'bg-gradient-to-br from-white via-mysql-primary-light to-mysql-primary-light/50',
        className
      )}
    >
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 渐变圆形装饰 */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-mysql-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-mysql-accent/10 rounded-full blur-3xl animate-pulse delay-1000" />
        
        {/* 浮动图标装饰 */}
        <motion.div
          className="absolute top-20 left-20 text-mysql-primary/20"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <Database size={48} />
        </motion.div>
        
        <motion.div
          className="absolute top-40 right-32 text-mysql-accent/20"
          animate={{
            y: [0, 15, 0],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
        >
          <Zap size={36} />
        </motion.div>
        
        <motion.div
          className="absolute bottom-32 left-32 text-mysql-primary/20"
          animate={{
            y: [0, -10, 0],
            rotate: [0, 3, 0],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        >
          <Shield size={42} />
        </motion.div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* 主标题 - 打字机效果 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="mb-6"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-mysql-text leading-tight">
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="inline-block"
              >
                {typewriterText.split('').map((char, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{
                      duration: 0.1,
                      delay: 0.5 + index * 0.1,
                    }}
                    className="inline-block"
                  >
                    {char}
                  </motion.span>
                ))}
              </motion.span>
            </h1>
          </motion.div>

          {/* 副标题 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 1.5, ease: "easeOut" }}
            className="mb-8"
          >
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-medium text-mysql-primary leading-relaxed">
              专业的数据库知识分享与项目管理平台
            </h2>
          </motion.div>

          {/* 描述文字 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 2, ease: "easeOut" }}
            className="mb-12"
          >
            <p className="text-lg sm:text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed">
              AI驱动的MySQL优化建议，专业的项目任务管理，支持多媒体的报告展示。
              让数据库性能优化变得简单高效，助力企业数字化转型。
            </p>
          </motion.div>

          {/* CTA按钮组 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 2.5, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            {/* 主要CTA按钮 */}
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'group inline-flex items-center px-8 py-4 bg-mysql-primary text-white',
                'text-lg font-semibold rounded-xl shadow-lg',
                'hover:bg-mysql-primary-dark hover:shadow-xl',
                'transition-all duration-300 ease-out',
                'focus:outline-none focus:ring-4 focus:ring-mysql-primary/30'
              )}
            >
              <span>开始探索</span>
              <ArrowRight 
                className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" 
              />
            </motion.button>

            {/* 次要CTA按钮 */}
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'group inline-flex items-center px-8 py-4 bg-white text-mysql-primary',
                'text-lg font-semibold rounded-xl shadow-lg border-2 border-mysql-primary',
                'hover:bg-mysql-primary hover:text-white hover:shadow-xl',
                'transition-all duration-300 ease-out',
                'focus:outline-none focus:ring-4 focus:ring-mysql-primary/30'
              )}
            >
              <Play className="mr-2 w-5 h-5" />
              <span>查看案例</span>
            </motion.button>
          </motion.div>

          {/* 工具快速入口 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 2.8, ease: "easeOut" }}
            className="mt-12 flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <div className="text-center mb-4 sm:mb-0">
              <p className="text-sm text-mysql-text-light font-medium">快速工具</p>
            </div>

            {/* ER图生成工具卡片 */}
            <motion.a
              href="/tools/er-diagram"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'group flex items-center space-x-3 px-6 py-3 bg-white/80 backdrop-blur-sm',
                'border border-mysql-border rounded-lg shadow-md',
                'hover:bg-white hover:shadow-lg hover:border-mysql-primary/30',
                'transition-all duration-300 ease-out',
                'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
              )}
            >
              <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors duration-300">
                <GitBranch className="w-4 h-4 text-purple-600" />
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
                  ER图生成
                </p>
                <p className="text-xs text-mysql-text-light">
                  数据库关系图
                </p>
              </div>
            </motion.a>

            {/* MySQL安装工具卡片 */}
            <motion.a
              href="/tools/mysql-installer"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                'group flex items-center space-x-3 px-6 py-3 bg-white/80 backdrop-blur-sm',
                'border border-mysql-border rounded-lg shadow-md',
                'hover:bg-white hover:shadow-lg hover:border-mysql-primary/30',
                'transition-all duration-300 ease-out',
                'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
              )}
            >
              <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors duration-300">
                <Download className="w-4 h-4 text-orange-600" />
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
                  MySQL安装
                </p>
                <p className="text-xs text-mysql-text-light">
                  一键安装配置
                </p>
              </div>
            </motion.a>
          </motion.div>

          {/* 特性标签 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 3, ease: "easeOut" }}
            className="mt-16 flex flex-wrap justify-center gap-6 text-sm text-mysql-text-light"
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-mysql-success rounded-full"></div>
              <span>7×24小时技术支持</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-mysql-success rounded-full"></div>
              <span>AI智能分析</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-mysql-success rounded-full"></div>
              <span>企业级安全保障</span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 底部渐变过渡 */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white to-transparent"></div>
    </section>
  );
}
