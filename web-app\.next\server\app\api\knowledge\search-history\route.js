/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/knowledge/search-history/route";
exports.ids = ["app/api/knowledge/search-history/route"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fknowledge%2Fsearch-history%2Froute&page=%2Fapi%2Fknowledge%2Fsearch-history%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fknowledge%2Fsearch-history%2Froute.ts&appDir=D%3A%5CMysqlAi.De%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMysqlAi.De%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fknowledge%2Fsearch-history%2Froute&page=%2Fapi%2Fknowledge%2Fsearch-history%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fknowledge%2Fsearch-history%2Froute.ts&appDir=D%3A%5CMysqlAi.De%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMysqlAi.De%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MysqlAi_De_web_app_src_app_api_knowledge_search_history_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/knowledge/search-history/route.ts */ \"(rsc)/./src/app/api/knowledge/search-history/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/knowledge/search-history/route\",\n        pathname: \"/api/knowledge/search-history\",\n        filename: \"route\",\n        bundlePath: \"app/api/knowledge/search-history/route\"\n    },\n    resolvedPagePath: \"D:\\\\MysqlAi.De\\\\web-app\\\\src\\\\app\\\\api\\\\knowledge\\\\search-history\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_MysqlAi_De_web_app_src_app_api_knowledge_search_history_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fknowledge%2Fsearch-history%2Froute&page=%2Fapi%2Fknowledge%2Fsearch-history%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fknowledge%2Fsearch-history%2Froute.ts&appDir=D%3A%5CMysqlAi.De%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMysqlAi.De%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/knowledge/search-history/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/knowledge/search-history/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n// MySQLAi.de - 搜索历史API路由\n// 提供搜索历史的CRUD操作\n\n\n// GET - 获取搜索历史\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const query = searchParams.get('query');\n        let queryBuilder = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('search_history').select('*').order('created_at', {\n            ascending: false\n        });\n        // 如果提供了查询参数，过滤结果\n        if (query) {\n            queryBuilder = queryBuilder.ilike('query', `%${query}%`);\n        }\n        // 应用分页\n        queryBuilder = queryBuilder.range(offset, offset + limit - 1);\n        const { data: searchHistory, error } = await queryBuilder;\n        if (error) {\n            console.error('获取搜索历史失败:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '获取搜索历史失败'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: searchHistory || [],\n            total: searchHistory?.length || 0\n        });\n    } catch (error) {\n        console.error('API错误:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - 添加搜索历史记录\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { query, results_count = 0 } = body;\n        if (!query || typeof query !== 'string' || query.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '搜索查询不能为空'\n            }, {\n                status: 400\n            });\n        }\n        // 获取客户端信息\n        const ip_address = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1';\n        const user_agent = request.headers.get('user-agent') || '';\n        // 检查是否已存在相同的搜索记录（最近1小时内）\n        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();\n        const { data: existingRecord } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('search_history').select('id').eq('query', query.trim()).eq('ip_address', ip_address).gte('created_at', oneHourAgo).limit(1);\n        // 如果最近1小时内已有相同搜索，不重复记录\n        if (existingRecord && existingRecord.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: '搜索记录已存在',\n                data: {\n                    id: existingRecord[0].id\n                }\n            });\n        }\n        // 插入新的搜索记录\n        const { data: newRecord, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('search_history').insert({\n            query: query.trim(),\n            results_count,\n            ip_address,\n            user_agent\n        }).select().single();\n        if (error) {\n            console.error('保存搜索历史失败:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '保存搜索历史失败'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newRecord,\n            message: '搜索历史已保存'\n        });\n    } catch (error) {\n        console.error('API错误:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - 清除搜索历史\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        const clearAll = searchParams.get('clearAll') === 'true';\n        if (clearAll) {\n            // 清除所有搜索历史（可选择性地基于IP地址）\n            const ip_address = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1';\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('search_history').delete().eq('ip_address', ip_address);\n            if (error) {\n                console.error('清除搜索历史失败:', error);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '清除搜索历史失败'\n                }, {\n                    status: 500\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: '搜索历史已清除'\n            });\n        }\n        if (id) {\n            // 删除特定的搜索记录\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('search_history').delete().eq('id', id);\n            if (error) {\n                console.error('删除搜索记录失败:', error);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '删除搜索记录失败'\n                }, {\n                    status: 500\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: '搜索记录已删除'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '请提供要删除的记录ID或设置clearAll=true'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('API错误:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/knowledge/search-history/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../node_modules/@supabase/supabase-js/dist/module/index.js\");\n// MySQLAi.de - Supabase 客户端配置\n// 提供类型安全的 Supabase 客户端实例\n\n// 环境变量验证 - 构建时使用占位符\nconst supabaseUrl = \"https://rlwppfewjevxzhqeupdq.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJsd3BwZmV3amV2eHpocWV1cGRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMDY1NjksImV4cCI6MjA2Njc4MjU2OX0.gDrcpdWqA8gdUIBw-5OLN2iBSkeHIDvmNdLwV67NJvc\" || 0;\n// 仅在客户端检查环境变量\nif (false) {}\n// 创建 Supabase 客户端实例\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    },\n    realtime: {\n        params: {\n            eventsPerSecond: 10\n        }\n    }\n});\n// 服务端客户端（用于服务端操作）\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// 认证相关工具函数\nconst auth = {\n    // 获取当前用户\n    getCurrentUser: async ()=>{\n        const { data: { user }, error } = await supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    },\n    // 登录\n    signIn: async (email, password)=>{\n        return await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n    },\n    // 注册\n    signUp: async (email, password, metadata)=>{\n        return await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: metadata\n            }\n        });\n    },\n    // 登出\n    signOut: async ()=>{\n        return await supabase.auth.signOut();\n    },\n    // 重置密码\n    resetPassword: async (email)=>{\n        return await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n    }\n};\n// 数据库操作工具函数\nconst db = {\n    // 知识库文章\n    articles: {\n        getAll: ()=>supabase.from('knowledge_articles').select('*'),\n        getById: (id)=>supabase.from('knowledge_articles').select('*').eq('id', id).single(),\n        getByCategory: (categoryId)=>supabase.from('knowledge_articles').select('*').eq('category_id', categoryId),\n        search: (query)=>supabase.from('knowledge_articles').select('*').textSearch('title,content', query)\n    },\n    // ER图项目\n    erProjects: {\n        getAll: ()=>supabase.from('er_projects').select('*'),\n        getById: (id)=>supabase.from('er_projects').select('*').eq('id', id).single(),\n        getByUser: (userId)=>supabase.from('er_projects').select('*').eq('user_id', userId),\n        create: (project)=>supabase.from('er_projects').insert(project),\n        update: (id, updates)=>supabase.from('er_projects').update(updates).eq('id', id),\n        delete: (id)=>supabase.from('er_projects').delete().eq('id', id)\n    },\n    // 用户收藏\n    favorites: {\n        getByUser: (userId)=>supabase.from('user_favorites').select('*, knowledge_articles(*)').eq('user_id', userId),\n        add: (userId, articleId)=>supabase.from('user_favorites').insert({\n                user_id: userId,\n                article_id: articleId\n            }),\n        remove: (userId, articleId)=>supabase.from('user_favorites').delete().eq('user_id', userId).eq('article_id', articleId)\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDhCQUE4QjtBQUM5Qix5QkFBeUI7QUFFNEI7QUFHckQsb0JBQW9CO0FBQ3BCLE1BQU1DLGNBQWNDLDBDQUFvQyxJQUFJLENBQTZCO0FBQ3pGLE1BQU1HLGtCQUFrQkgsa05BQXlDLElBQUksQ0FBNko7QUFFbE8sY0FBYztBQUNkLElBQUksS0FBd0csRUFBRyxFQUU5RztBQUVELG9CQUFvQjtBQUNiLE1BQU1RLFdBQVdWLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VNLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtJQUNBQyxVQUFVO1FBQ1JDLFFBQVE7WUFDTkMsaUJBQWlCO1FBQ25CO0lBQ0Y7QUFDRixHQUFHO0FBRUgsa0JBQWtCO0FBQ1gsTUFBTUMsZ0JBQWdCbEIsbUVBQVlBLENBQ3ZDQyxhQUNBQyxRQUFRQyxHQUFHLENBQUNnQix5QkFBeUIsSUFBSWQsaUJBQ3pDO0lBQ0VNLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7SUFDbEI7QUFDRixHQUNBO0FBRUYsV0FBVztBQUNKLE1BQU1GLE9BQU87SUFDbEIsU0FBUztJQUNUUyxnQkFBZ0I7UUFDZCxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1iLFNBQVNDLElBQUksQ0FBQ2EsT0FBTztRQUM3RCxPQUFPO1lBQUVGO1lBQU1DO1FBQU07SUFDdkI7SUFFQSxLQUFLO0lBQ0xFLFFBQVEsT0FBT0MsT0FBZUM7UUFDNUIsT0FBTyxNQUFNakIsU0FBU0MsSUFBSSxDQUFDaUIsa0JBQWtCLENBQUM7WUFBRUY7WUFBT0M7UUFBUztJQUNsRTtJQUVBLEtBQUs7SUFDTEUsUUFBUSxPQUFPSCxPQUFlQyxVQUFrQkc7UUFDOUMsT0FBTyxNQUFNcEIsU0FBU0MsSUFBSSxDQUFDa0IsTUFBTSxDQUFDO1lBQ2hDSDtZQUNBQztZQUNBSSxTQUFTO2dCQUNQVixNQUFNUztZQUNSO1FBQ0Y7SUFDRjtJQUVBLEtBQUs7SUFDTEUsU0FBUztRQUNQLE9BQU8sTUFBTXRCLFNBQVNDLElBQUksQ0FBQ3FCLE9BQU87SUFDcEM7SUFFQSxPQUFPO0lBQ1BDLGVBQWUsT0FBT1A7UUFDcEIsT0FBTyxNQUFNaEIsU0FBU0MsSUFBSSxDQUFDdUIscUJBQXFCLENBQUNSLE9BQU87WUFDdERTLFlBQVksR0FBR0MsT0FBT0MsUUFBUSxDQUFDQyxNQUFNLENBQUMsb0JBQW9CLENBQUM7UUFDN0Q7SUFDRjtBQUNGLEVBQUU7QUFFRixZQUFZO0FBQ0wsTUFBTUMsS0FBSztJQUNoQixRQUFRO0lBQ1JDLFVBQVU7UUFDUkMsUUFBUSxJQUFNL0IsU0FBU2dDLElBQUksQ0FBQyxzQkFBc0JDLE1BQU0sQ0FBQztRQUN6REMsU0FBUyxDQUFDQyxLQUFlbkMsU0FBU2dDLElBQUksQ0FBQyxzQkFBc0JDLE1BQU0sQ0FBQyxLQUFLRyxFQUFFLENBQUMsTUFBTUQsSUFBSUUsTUFBTTtRQUM1RkMsZUFBZSxDQUFDQyxhQUF1QnZDLFNBQVNnQyxJQUFJLENBQUMsc0JBQXNCQyxNQUFNLENBQUMsS0FBS0csRUFBRSxDQUFDLGVBQWVHO1FBQ3pHQyxRQUFRLENBQUNDLFFBQWtCekMsU0FBU2dDLElBQUksQ0FBQyxzQkFBc0JDLE1BQU0sQ0FBQyxLQUFLUyxVQUFVLENBQUMsaUJBQWlCRDtJQUN6RztJQUVBLFFBQVE7SUFDUkUsWUFBWTtRQUNWWixRQUFRLElBQU0vQixTQUFTZ0MsSUFBSSxDQUFDLGVBQWVDLE1BQU0sQ0FBQztRQUNsREMsU0FBUyxDQUFDQyxLQUFlbkMsU0FBU2dDLElBQUksQ0FBQyxlQUFlQyxNQUFNLENBQUMsS0FBS0csRUFBRSxDQUFDLE1BQU1ELElBQUlFLE1BQU07UUFDckZPLFdBQVcsQ0FBQ0MsU0FBbUI3QyxTQUFTZ0MsSUFBSSxDQUFDLGVBQWVDLE1BQU0sQ0FBQyxLQUFLRyxFQUFFLENBQUMsV0FBV1M7UUFDdEZDLFFBQVEsQ0FBQ0MsVUFBaUIvQyxTQUFTZ0MsSUFBSSxDQUFDLGVBQWVnQixNQUFNLENBQUNEO1FBQzlERSxRQUFRLENBQUNkLElBQVllLFVBQWlCbEQsU0FBU2dDLElBQUksQ0FBQyxlQUFlaUIsTUFBTSxDQUFDQyxTQUFTZCxFQUFFLENBQUMsTUFBTUQ7UUFDNUZnQixRQUFRLENBQUNoQixLQUFlbkMsU0FBU2dDLElBQUksQ0FBQyxlQUFlbUIsTUFBTSxHQUFHZixFQUFFLENBQUMsTUFBTUQ7SUFDekU7SUFFQSxPQUFPO0lBQ1BpQixXQUFXO1FBQ1RSLFdBQVcsQ0FBQ0MsU0FBbUI3QyxTQUFTZ0MsSUFBSSxDQUFDLGtCQUFrQkMsTUFBTSxDQUFDLDRCQUE0QkcsRUFBRSxDQUFDLFdBQVdTO1FBQ2hIUSxLQUFLLENBQUNSLFFBQWdCUyxZQUFzQnRELFNBQVNnQyxJQUFJLENBQUMsa0JBQWtCZ0IsTUFBTSxDQUFDO2dCQUFFTyxTQUFTVjtnQkFBUVcsWUFBWUY7WUFBVTtRQUM1SEcsUUFBUSxDQUFDWixRQUFnQlMsWUFBc0J0RCxTQUFTZ0MsSUFBSSxDQUFDLGtCQUFrQm1CLE1BQU0sR0FBR2YsRUFBRSxDQUFDLFdBQVdTLFFBQVFULEVBQUUsQ0FBQyxjQUFja0I7SUFDakk7QUFDRixFQUFFO0FBRUYsaUVBQWV0RCxRQUFRQSxFQUFDIiwic291cmNlcyI6WyJEOlxcTXlzcWxBaS5EZVxcd2ViLWFwcFxcc3JjXFxsaWJcXHN1cGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE15U1FMQWkuZGUgLSBTdXBhYmFzZSDlrqLmiLfnq6/phY3nva5cbi8vIOaPkOS+m+exu+Wei+WuieWFqOeahCBTdXBhYmFzZSDlrqLmiLfnq6/lrp7kvotcblxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcbmltcG9ydCB0eXBlIHsgRGF0YWJhc2UgfSBmcm9tICcuL2RhdGFiYXNlLnR5cGVzJztcblxuLy8g546v5aKD5Y+Y6YeP6aqM6K+BIC0g5p6E5bu65pe25L2/55So5Y2g5L2N56ymXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCB8fCAnaHR0cHM6Ly9leGFtcGxlLnN1cGFiYXNlLmNvJztcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIHx8ICdleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUp6ZFhCaFltRnpaU0lzSW5KbFppSTZJbVY0WVcxd2JHVWlMQ0p5YjJ4bElqb2lZVzV2YmlJc0ltbGhkQ0k2TVRZME1qUTBOalF3TUN3aVpYaHdJam94T1RVNE1ESXlOREF3ZlEuZXhhbXBsZSc7XG5cbi8vIOS7heWcqOWuouaIt+err+ajgOafpeeOr+Wig+WPmOmHj1xuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIChzdXBhYmFzZVVybC5pbmNsdWRlcygnZXhhbXBsZScpIHx8IHN1cGFiYXNlQW5vbktleS5pbmNsdWRlcygnZXhhbXBsZScpKSkge1xuICBjb25zb2xlLndhcm4oJ+ivt+mFjee9ruato+ehrueahCBTdXBhYmFzZSDnjq/looPlj5jph48nKTtcbn1cblxuLy8g5Yib5bu6IFN1cGFiYXNlIOWuouaIt+err+WunuS+i1xuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50PERhdGFiYXNlPihzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5LCB7XG4gIGF1dGg6IHtcbiAgICBhdXRvUmVmcmVzaFRva2VuOiB0cnVlLFxuICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZVxuICB9LFxuICByZWFsdGltZToge1xuICAgIHBhcmFtczoge1xuICAgICAgZXZlbnRzUGVyU2Vjb25kOiAxMFxuICAgIH1cbiAgfVxufSk7XG5cbi8vIOacjeWKoeerr+WuouaIt+err++8iOeUqOS6juacjeWKoeerr+aTjeS9nO+8iVxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlQWRtaW4gPSBjcmVhdGVDbGllbnQ8RGF0YWJhc2U+KFxuICBzdXBhYmFzZVVybCxcbiAgcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSB8fCBzdXBhYmFzZUFub25LZXksXG4gIHtcbiAgICBhdXRoOiB7XG4gICAgICBhdXRvUmVmcmVzaFRva2VuOiBmYWxzZSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgIH1cbiAgfVxuKTtcblxuLy8g6K6k6K+B55u45YWz5bel5YW35Ye95pWwXG5leHBvcnQgY29uc3QgYXV0aCA9IHtcbiAgLy8g6I635Y+W5b2T5YmN55So5oi3XG4gIGdldEN1cnJlbnRVc2VyOiBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIHJldHVybiB7IHVzZXIsIGVycm9yIH07XG4gIH0sXG5cbiAgLy8g55m75b2VXG4gIHNpZ25JbjogYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoeyBlbWFpbCwgcGFzc3dvcmQgfSk7XG4gIH0sXG5cbiAgLy8g5rOo5YaMXG4gIHNpZ25VcDogYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PikgPT4ge1xuICAgIHJldHVybiBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25VcCh7XG4gICAgICBlbWFpbCxcbiAgICAgIHBhc3N3b3JkLFxuICAgICAgb3B0aW9uczoge1xuICAgICAgICBkYXRhOiBtZXRhZGF0YVxuICAgICAgfVxuICAgIH0pO1xuICB9LFxuXG4gIC8vIOeZu+WHulxuICBzaWduT3V0OiBhc3luYyAoKSA9PiB7XG4gICAgcmV0dXJuIGF3YWl0IHN1cGFiYXNlLmF1dGguc2lnbk91dCgpO1xuICB9LFxuXG4gIC8vIOmHjee9ruWvhueggVxuICByZXNldFBhc3N3b3JkOiBhc3luYyAoZW1haWw6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBhd2FpdCBzdXBhYmFzZS5hdXRoLnJlc2V0UGFzc3dvcmRGb3JFbWFpbChlbWFpbCwge1xuICAgICAgcmVkaXJlY3RUbzogYCR7d2luZG93LmxvY2F0aW9uLm9yaWdpbn0vYXV0aC9yZXNldC1wYXNzd29yZGBcbiAgICB9KTtcbiAgfVxufTtcblxuLy8g5pWw5o2u5bqT5pON5L2c5bel5YW35Ye95pWwXG5leHBvcnQgY29uc3QgZGIgPSB7XG4gIC8vIOefpeivhuW6k+aWh+eroFxuICBhcnRpY2xlczoge1xuICAgIGdldEFsbDogKCkgPT4gc3VwYWJhc2UuZnJvbSgna25vd2xlZGdlX2FydGljbGVzJykuc2VsZWN0KCcqJyksXG4gICAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpID0+IHN1cGFiYXNlLmZyb20oJ2tub3dsZWRnZV9hcnRpY2xlcycpLnNlbGVjdCgnKicpLmVxKCdpZCcsIGlkKS5zaW5nbGUoKSxcbiAgICBnZXRCeUNhdGVnb3J5OiAoY2F0ZWdvcnlJZDogc3RyaW5nKSA9PiBzdXBhYmFzZS5mcm9tKCdrbm93bGVkZ2VfYXJ0aWNsZXMnKS5zZWxlY3QoJyonKS5lcSgnY2F0ZWdvcnlfaWQnLCBjYXRlZ29yeUlkKSxcbiAgICBzZWFyY2g6IChxdWVyeTogc3RyaW5nKSA9PiBzdXBhYmFzZS5mcm9tKCdrbm93bGVkZ2VfYXJ0aWNsZXMnKS5zZWxlY3QoJyonKS50ZXh0U2VhcmNoKCd0aXRsZSxjb250ZW50JywgcXVlcnkpXG4gIH0sXG5cbiAgLy8gRVLlm77pobnnm65cbiAgZXJQcm9qZWN0czoge1xuICAgIGdldEFsbDogKCkgPT4gc3VwYWJhc2UuZnJvbSgnZXJfcHJvamVjdHMnKS5zZWxlY3QoJyonKSxcbiAgICBnZXRCeUlkOiAoaWQ6IHN0cmluZykgPT4gc3VwYWJhc2UuZnJvbSgnZXJfcHJvamVjdHMnKS5zZWxlY3QoJyonKS5lcSgnaWQnLCBpZCkuc2luZ2xlKCksXG4gICAgZ2V0QnlVc2VyOiAodXNlcklkOiBzdHJpbmcpID0+IHN1cGFiYXNlLmZyb20oJ2VyX3Byb2plY3RzJykuc2VsZWN0KCcqJykuZXEoJ3VzZXJfaWQnLCB1c2VySWQpLFxuICAgIGNyZWF0ZTogKHByb2plY3Q6IGFueSkgPT4gc3VwYWJhc2UuZnJvbSgnZXJfcHJvamVjdHMnKS5pbnNlcnQocHJvamVjdCksXG4gICAgdXBkYXRlOiAoaWQ6IHN0cmluZywgdXBkYXRlczogYW55KSA9PiBzdXBhYmFzZS5mcm9tKCdlcl9wcm9qZWN0cycpLnVwZGF0ZSh1cGRhdGVzKS5lcSgnaWQnLCBpZCksXG4gICAgZGVsZXRlOiAoaWQ6IHN0cmluZykgPT4gc3VwYWJhc2UuZnJvbSgnZXJfcHJvamVjdHMnKS5kZWxldGUoKS5lcSgnaWQnLCBpZClcbiAgfSxcblxuICAvLyDnlKjmiLfmlLbol49cbiAgZmF2b3JpdGVzOiB7XG4gICAgZ2V0QnlVc2VyOiAodXNlcklkOiBzdHJpbmcpID0+IHN1cGFiYXNlLmZyb20oJ3VzZXJfZmF2b3JpdGVzJykuc2VsZWN0KCcqLCBrbm93bGVkZ2VfYXJ0aWNsZXMoKiknKS5lcSgndXNlcl9pZCcsIHVzZXJJZCksXG4gICAgYWRkOiAodXNlcklkOiBzdHJpbmcsIGFydGljbGVJZDogc3RyaW5nKSA9PiBzdXBhYmFzZS5mcm9tKCd1c2VyX2Zhdm9yaXRlcycpLmluc2VydCh7IHVzZXJfaWQ6IHVzZXJJZCwgYXJ0aWNsZV9pZDogYXJ0aWNsZUlkIH0pLFxuICAgIHJlbW92ZTogKHVzZXJJZDogc3RyaW5nLCBhcnRpY2xlSWQ6IHN0cmluZykgPT4gc3VwYWJhc2UuZnJvbSgndXNlcl9mYXZvcml0ZXMnKS5kZWxldGUoKS5lcSgndXNlcl9pZCcsIHVzZXJJZCkuZXEoJ2FydGljbGVfaWQnLCBhcnRpY2xlSWQpXG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IHN1cGFiYXNlO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiaW5jbHVkZXMiLCJjb25zb2xlIiwid2FybiIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCIsInJlYWx0aW1lIiwicGFyYW1zIiwiZXZlbnRzUGVyU2Vjb25kIiwic3VwYWJhc2VBZG1pbiIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJnZXRDdXJyZW50VXNlciIsImRhdGEiLCJ1c2VyIiwiZXJyb3IiLCJnZXRVc2VyIiwic2lnbkluIiwiZW1haWwiLCJwYXNzd29yZCIsInNpZ25JbldpdGhQYXNzd29yZCIsInNpZ25VcCIsIm1ldGFkYXRhIiwib3B0aW9ucyIsInNpZ25PdXQiLCJyZXNldFBhc3N3b3JkIiwicmVzZXRQYXNzd29yZEZvckVtYWlsIiwicmVkaXJlY3RUbyIsIndpbmRvdyIsImxvY2F0aW9uIiwib3JpZ2luIiwiZGIiLCJhcnRpY2xlcyIsImdldEFsbCIsImZyb20iLCJzZWxlY3QiLCJnZXRCeUlkIiwiaWQiLCJlcSIsInNpbmdsZSIsImdldEJ5Q2F0ZWdvcnkiLCJjYXRlZ29yeUlkIiwic2VhcmNoIiwicXVlcnkiLCJ0ZXh0U2VhcmNoIiwiZXJQcm9qZWN0cyIsImdldEJ5VXNlciIsInVzZXJJZCIsImNyZWF0ZSIsInByb2plY3QiLCJpbnNlcnQiLCJ1cGRhdGUiLCJ1cGRhdGVzIiwiZGVsZXRlIiwiZmF2b3JpdGVzIiwiYWRkIiwiYXJ0aWNsZUlkIiwidXNlcl9pZCIsImFydGljbGVfaWQiLCJyZW1vdmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?7f96":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?d1cc":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fknowledge%2Fsearch-history%2Froute&page=%2Fapi%2Fknowledge%2Fsearch-history%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fknowledge%2Fsearch-history%2Froute.ts&appDir=D%3A%5CMysqlAi.De%5Cweb-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMysqlAi.De%5Cweb-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();