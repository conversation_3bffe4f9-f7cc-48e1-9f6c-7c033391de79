{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/legal.ts"], "sourcesContent": ["// MySQLAi.de - 法律条款数据管理文件\n// 包含服务条款、隐私政策、免责声明、<PERSON>ie政策的完整内容和管理系统\n\n// 法律页面类型定义\nexport type LegalPageType = 'terms' | 'privacy' | 'disclaimer' | 'cookies';\n\n// 法律条款章节接口\nexport interface LegalSection {\n  id: string;\n  title: string;\n  content: string;\n  subsections?: LegalSection[];\n}\n\n// 法律页面内容接口\nexport interface LegalPageContent {\n  type: LegalPageType;\n  title: string;\n  description: string;\n  lastUpdated: string;\n  version: string;\n  sections: LegalSection[];\n}\n\n// 法律页面元数据接口\nexport interface LegalPageMeta {\n  type: LegalPageType;\n  title: string;\n  description: string;\n  keywords: string[];\n  lastUpdated: string;\n  version: string;\n}\n\n// 服务条款内容\nexport const TERMS_CONTENT: LegalPageContent = {\n  type: 'terms',\n  title: '服务条款',\n  description: 'MySQLAi.de平台服务使用条款和用户协议',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'acceptance',\n      title: '1. 条款接受',\n      content: '欢迎使用MySQLAi.de（以下简称\"本平台\"）提供的MySQL智能分析服务。通过访问或使用本平台的任何服务，您表示同意遵守本服务条款（以下简称\"本条款\"）。如果您不同意本条款的任何部分，请不要使用本平台的服务。'\n    },\n    {\n      id: 'service-description',\n      title: '2. 服务描述',\n      content: 'MySQLAi.de是一个专业的MySQL数据库智能分析平台，为用户提供以下服务：',\n      subsections: [\n        {\n          id: 'service-mysql-analysis',\n          title: '2.1 MySQL智能分析',\n          content: '基于AI技术的数据库性能分析、优化建议和智能诊断服务。'\n        },\n        {\n          id: 'service-project-management',\n          title: '2.2 项目管理',\n          content: '提供数据库项目的任务管理、进度跟踪和团队协作功能。'\n        },\n        {\n          id: 'service-report-display',\n          title: '2.3 报告展示',\n          content: '支持多媒体格式的项目报告生成、展示和分享功能。'\n        },\n        {\n          id: 'service-technical-support',\n          title: '2.4 技术支持',\n          content: '7×24小时专业技术支持服务，包括在线咨询、远程协助等。'\n        }\n      ]\n    },\n    {\n      id: 'user-obligations',\n      title: '3. 用户义务',\n      content: '使用本平台服务时，您需要遵守以下义务：',\n      subsections: [\n        {\n          id: 'legal-compliance',\n          title: '3.1 法律合规',\n          content: '遵守中华人民共和国相关法律法规，不得利用本平台从事违法活动。'\n        },\n        {\n          id: 'account-security',\n          title: '3.2 账户安全',\n          content: '妥善保管账户信息，对账户下的所有活动承担责任。'\n        },\n        {\n          id: 'data-accuracy',\n          title: '3.3 数据准确性',\n          content: '确保提供给本平台的数据信息真实、准确、完整。'\n        },\n        {\n          id: 'proper-usage',\n          title: '3.4 合理使用',\n          content: '合理使用平台资源，不得恶意攻击、滥用或干扰平台正常运行。'\n        }\n      ]\n    },\n    {\n      id: 'intellectual-property',\n      title: '4. 知识产权',\n      content: '本平台的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。未经授权，不得复制、传播、展示、镜像、上传、下载使用。'\n    },\n    {\n      id: 'privacy-protection',\n      title: '5. 隐私保护',\n      content: '我们重视用户隐私保护，具体的隐私保护措施请参阅《隐私政策》。我们承诺按照相关法律法规和本平台隐私政策处理用户个人信息。'\n    },\n    {\n      id: 'service-availability',\n      title: '6. 服务可用性',\n      content: '我们努力确保服务的连续性和稳定性，但不保证服务不会中断。因系统维护、升级或不可抗力等原因导致的服务中断，我们将尽快恢复服务。'\n    },\n    {\n      id: 'limitation-of-liability',\n      title: '7. 责任限制',\n      content: '在法律允许的最大范围内，本平台对因使用或无法使用本服务而导致的任何直接、间接、偶然、特殊或后果性损害不承担责任。'\n    },\n    {\n      id: 'terms-modification',\n      title: '8. 条款修改',\n      content: '我们保留随时修改本条款的权利。修改后的条款将在本平台公布，继续使用本服务即表示您接受修改后的条款。'\n    },\n    {\n      id: 'governing-law',\n      title: '9. 适用法律',\n      content: '本条款的解释和执行适用中华人民共和国法律。如发生争议，应通过友好协商解决；协商不成的，提交本平台所在地人民法院管辖。'\n    },\n    {\n      id: 'contact-information',\n      title: '10. 联系方式',\n      content: '如您对本条款有任何疑问，请通过以下方式联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 隐私政策内容\nexport const PRIVACY_CONTENT: LegalPageContent = {\n  type: 'privacy',\n  title: '隐私政策',\n  description: 'MySQLAi.de平台用户隐私保护政策和个人信息处理规则',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'introduction',\n      title: '1. 引言',\n      content: 'MySQLAi.de（以下简称\"我们\"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'\n    },\n    {\n      id: 'information-collection',\n      title: '2. 我们收集的信息',\n      content: '为了向您提供更好的服务，我们可能会收集以下类型的信息：',\n      subsections: [\n        {\n          id: 'account-information',\n          title: '2.1 账户信息',\n          content: '当您注册账户时，我们会收集您的用户名、邮箱地址、手机号码等基本信息。'\n        },\n        {\n          id: 'usage-information',\n          title: '2.2 使用信息',\n          content: '您使用我们服务时产生的信息，包括访问时间、使用功能、操作记录等。'\n        },\n        {\n          id: 'device-information',\n          title: '2.3 设备信息',\n          content: '您使用的设备信息，包括设备型号、操作系统、浏览器类型、IP地址等。'\n        },\n        {\n          id: 'database-information',\n          title: '2.4 数据库信息',\n          content: '为提供MySQL分析服务，我们可能需要访问您的数据库结构信息（不包含敏感业务数据）。'\n        }\n      ]\n    },\n    {\n      id: 'information-usage',\n      title: '3. 信息使用目的',\n      content: '我们收集和使用您的个人信息主要用于以下目的：',\n      subsections: [\n        {\n          id: 'service-provision',\n          title: '3.1 服务提供',\n          content: '为您提供MySQL智能分析、项目管理、报告展示等核心服务。'\n        },\n        {\n          id: 'service-improvement',\n          title: '3.2 服务改进',\n          content: '分析用户使用习惯，优化产品功能和用户体验。'\n        },\n        {\n          id: 'security-protection',\n          title: '3.3 安全保护',\n          content: '保护您的账户安全，防范欺诈、滥用等风险。'\n        },\n        {\n          id: 'customer-support',\n          title: '3.4 客户支持',\n          content: '为您提供技术支持和客户服务。'\n        }\n      ]\n    },\n    {\n      id: 'information-sharing',\n      title: '4. 信息共享',\n      content: '我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：',\n      subsections: [\n        {\n          id: 'legal-requirement',\n          title: '4.1 法律要求',\n          content: '根据法律法规、法律程序、政府要求或司法裁定需要披露。'\n        },\n        {\n          id: 'user-consent',\n          title: '4.2 用户同意',\n          content: '获得您的明确同意后，与第三方共享特定信息。'\n        },\n        {\n          id: 'service-providers',\n          title: '4.3 服务提供商',\n          content: '与我们的服务提供商共享必要信息，以便他们为我们提供服务。'\n        }\n      ]\n    },\n    {\n      id: 'information-security',\n      title: '5. 信息安全',\n      content: '我们采用行业标准的安全措施保护您的个人信息：',\n      subsections: [\n        {\n          id: 'encryption',\n          title: '5.1 数据加密',\n          content: '使用SSL/TLS加密技术保护数据传输安全。'\n        },\n        {\n          id: 'access-control',\n          title: '5.2 访问控制',\n          content: '严格限制员工对个人信息的访问权限。'\n        },\n        {\n          id: 'security-monitoring',\n          title: '5.3 安全监控',\n          content: '建立完善的安全监控和应急响应机制。'\n        }\n      ]\n    },\n    {\n      id: 'user-rights',\n      title: '6. 您的权利',\n      content: '您对自己的个人信息享有以下权利：',\n      subsections: [\n        {\n          id: 'access-right',\n          title: '6.1 知情权',\n          content: '您有权了解我们收集、使用您个人信息的情况。'\n        },\n        {\n          id: 'correction-right',\n          title: '6.2 更正权',\n          content: '您有权要求我们更正或补充您的个人信息。'\n        },\n        {\n          id: 'deletion-right',\n          title: '6.3 删除权',\n          content: '在特定情况下，您有权要求我们删除您的个人信息。'\n        }\n      ]\n    },\n    {\n      id: 'policy-updates',\n      title: '7. 政策更新',\n      content: '我们可能会不时更新本隐私政策。更新后的政策将在本平台公布，并通过适当方式通知您。'\n    },\n    {\n      id: 'contact-us',\n      title: '8. 联系我们',\n      content: '如您对本隐私政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 免责声明内容\nexport const DISCLAIMER_CONTENT: LegalPageContent = {\n  type: 'disclaimer',\n  title: '免责声明',\n  description: 'MySQLAi.de平台服务免责条款和责任限制说明',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'general-disclaimer',\n      title: '1. 一般免责',\n      content: 'MySQLAi.de平台（以下简称\"本平台\"）提供的所有信息、建议和服务仅供参考，不构成任何形式的保证。用户使用本平台服务的风险由用户自行承担。'\n    },\n    {\n      id: 'service-limitations',\n      title: '2. 服务限制',\n      content: '本平台的服务存在以下限制：',\n      subsections: [\n        {\n          id: 'analysis-accuracy',\n          title: '2.1 分析准确性',\n          content: 'MySQL智能分析结果基于算法和数据模型，可能存在误差，不保证100%准确。'\n        },\n        {\n          id: 'service-availability',\n          title: '2.2 服务可用性',\n          content: '服务可能因维护、升级、网络故障等原因暂时中断，我们不承担因此造成的损失。'\n        },\n        {\n          id: 'data-security',\n          title: '2.3 数据安全',\n          content: '虽然我们采取安全措施保护数据，但无法保证绝对安全，不承担因数据泄露造成的损失。'\n        }\n      ]\n    },\n    {\n      id: 'user-responsibility',\n      title: '3. 用户责任',\n      content: '用户在使用本平台服务时应承担以下责任：',\n      subsections: [\n        {\n          id: 'data-backup',\n          title: '3.1 数据备份',\n          content: '用户应自行备份重要数据，本平台不承担数据丢失的责任。'\n        },\n        {\n          id: 'decision-making',\n          title: '3.2 决策责任',\n          content: '基于本平台分析结果做出的业务决策，责任由用户自行承担。'\n        },\n        {\n          id: 'compliance',\n          title: '3.3 合规使用',\n          content: '用户应确保使用本平台服务符合相关法律法规要求。'\n        }\n      ]\n    },\n    {\n      id: 'third-party-services',\n      title: '4. 第三方服务',\n      content: '本平台可能包含第三方服务链接，对于第三方服务的内容、隐私政策或做法，我们不承担任何责任。'\n    },\n    {\n      id: 'liability-limitation',\n      title: '5. 责任限制',\n      content: '在法律允许的最大范围内，本平台对任何直接、间接、偶然、特殊、后果性或惩罚性损害不承担责任，包括但不限于利润损失、数据丢失、业务中断等。'\n    },\n    {\n      id: 'indemnification',\n      title: '6. 赔偿',\n      content: '用户同意就因违反本免责声明或使用本平台服务而产生的任何索赔、损失或费用，向本平台提供赔偿和保护。'\n    },\n    {\n      id: 'governing-law',\n      title: '7. 适用法律',\n      content: '本免责声明受中华人民共和国法律管辖。任何争议应通过友好协商解决，协商不成的提交本平台所在地人民法院管辖。'\n    }\n  ]\n};\n\n// Cookie政策内容\nexport const COOKIES_CONTENT: LegalPageContent = {\n  type: 'cookies',\n  title: 'Cookie政策',\n  description: 'MySQLAi.de平台Cookie使用说明和管理指南',\n  lastUpdated: '2025-06-28',\n  version: '1.0',\n  sections: [\n    {\n      id: 'what-are-cookies',\n      title: '1. 什么是Cookie',\n      content: 'Cookie是网站存储在您设备上的小型文本文件，用于记住您的偏好设置和改善您的浏览体验。Cookie不会损害您的设备或文件。'\n    },\n    {\n      id: 'cookie-types',\n      title: '2. Cookie类型',\n      content: '我们使用以下类型的Cookie：',\n      subsections: [\n        {\n          id: 'essential-cookies',\n          title: '2.1 必要Cookie',\n          content: '这些Cookie对网站正常运行是必需的，包括用户身份验证、安全防护等功能。'\n        },\n        {\n          id: 'functional-cookies',\n          title: '2.2 功能Cookie',\n          content: '这些Cookie用于记住您的偏好设置，如语言选择、主题设置等，以提供个性化体验。'\n        },\n        {\n          id: 'analytics-cookies',\n          title: '2.3 分析Cookie',\n          content: '这些Cookie帮助我们了解用户如何使用网站，以便改进网站性能和用户体验。'\n        },\n        {\n          id: 'marketing-cookies',\n          title: '2.4 营销Cookie',\n          content: '这些Cookie用于跟踪用户在网站上的活动，以便提供相关的广告和营销内容。'\n        }\n      ]\n    },\n    {\n      id: 'cookie-usage',\n      title: '3. Cookie使用目的',\n      content: '我们使用Cookie的主要目的包括：',\n      subsections: [\n        {\n          id: 'user-authentication',\n          title: '3.1 用户认证',\n          content: '保持您的登录状态，确保账户安全。'\n        },\n        {\n          id: 'preference-storage',\n          title: '3.2 偏好存储',\n          content: '记住您的设置和偏好，提供个性化服务。'\n        },\n        {\n          id: 'performance-analysis',\n          title: '3.3 性能分析',\n          content: '分析网站使用情况，优化网站性能。'\n        },\n        {\n          id: 'security-protection',\n          title: '3.4 安全保护',\n          content: '防范恶意攻击，保护网站和用户安全。'\n        }\n      ]\n    },\n    {\n      id: 'cookie-management',\n      title: '4. Cookie管理',\n      content: '您可以通过以下方式管理Cookie：',\n      subsections: [\n        {\n          id: 'browser-settings',\n          title: '4.1 浏览器设置',\n          content: '大多数浏览器允许您控制Cookie设置，包括接受、拒绝或删除Cookie。'\n        },\n        {\n          id: 'platform-settings',\n          title: '4.2 平台设置',\n          content: '您可以在账户设置中管理某些Cookie偏好。'\n        },\n        {\n          id: 'opt-out',\n          title: '4.3 退出选择',\n          content: '您可以选择退出某些非必要Cookie，但这可能影响网站功能。'\n        }\n      ]\n    },\n    {\n      id: 'third-party-cookies',\n      title: '5. 第三方Cookie',\n      content: '我们的网站可能包含第三方服务提供商设置的Cookie，如分析工具、社交媒体插件等。这些第三方Cookie受其各自隐私政策约束。'\n    },\n    {\n      id: 'policy-updates',\n      title: '6. 政策更新',\n      content: '我们可能会不时更新本Cookie政策。更新后的政策将在网站上公布，建议您定期查看。'\n    },\n    {\n      id: 'contact-information',\n      title: '7. 联系我们',\n      content: '如您对本Cookie政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'\n    }\n  ]\n};\n\n// 所有法律内容的集合\nexport const LEGAL_CONTENTS = {\n  terms: TERMS_CONTENT,\n  privacy: PRIVACY_CONTENT,\n  disclaimer: DISCLAIMER_CONTENT,\n  cookies: COOKIES_CONTENT,\n} as const;\n\n// 工具函数：获取指定类型的法律条款内容\nexport function getLegalContent(type: LegalPageType): LegalPageContent {\n  return LEGAL_CONTENTS[type];\n}\n\n// 工具函数：获取法律页面元数据\nexport function getLegalPageMeta(type: LegalPageType): LegalPageMeta {\n  const content = getLegalContent(type);\n  return {\n    type: content.type,\n    title: content.title,\n    description: content.description,\n    keywords: generateKeywords(type),\n    lastUpdated: content.lastUpdated,\n    version: content.version,\n  };\n}\n\n// 工具函数：生成法律页面关键词\nfunction generateKeywords(type: LegalPageType): string[] {\n  const baseKeywords = ['MySQLAi.de', 'MySQL', '数据库', '法律声明'];\n\n  const typeKeywords = {\n    terms: ['服务条款', '用户协议', '使用条款', '服务协议'],\n    privacy: ['隐私政策', '个人信息保护', '数据保护', '隐私保护'],\n    disclaimer: ['免责声明', '责任限制', '法律免责', '服务限制'],\n    cookies: ['Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理'],\n  };\n\n  return [...baseKeywords, ...typeKeywords[type]];\n}\n\n// 工具函数：获取所有法律页面类型\nexport function getAllLegalPageTypes(): LegalPageType[] {\n  return ['terms', 'privacy', 'disclaimer', 'cookies'];\n}\n\n// 工具函数：获取法律页面导航链接\nexport function getLegalNavigationLinks(currentType?: LegalPageType) {\n  return getAllLegalPageTypes()\n    .filter(type => type !== currentType)\n    .map(type => {\n      const meta = getLegalPageMeta(type);\n      return {\n        type,\n        title: meta.title,\n        href: `/${type}`,\n        description: meta.description,\n      };\n    });\n}\n\n// 工具函数：格式化最后更新时间\nexport function formatLastUpdated(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n// 工具函数：检查法律页面类型是否有效\nexport function isValidLegalPageType(type: string): type is LegalPageType {\n  return getAllLegalPageTypes().includes(type as LegalPageType);\n}\n"], "names": [], "mappings": "AAAA,0BAA0B;AAC1B,sCAAsC;AAEtC,WAAW;;;;;;;;;;;;;;AAgCJ,MAAM,gBAAkC;IAC7C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,kBAAoC;IAC/C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,qBAAuC;IAClD,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,kBAAoC;IAC/C,MAAM;IACN,OAAO;IACP,aAAa;IACb,aAAa;IACb,SAAS;IACT,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,SAAS;gBACX;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;QACX;KACD;AACH;AAGO,MAAM,iBAAiB;IAC5B,OAAO;IACP,SAAS;IACT,YAAY;IACZ,SAAS;AACX;AAGO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,cAAc,CAAC,KAAK;AAC7B;AAGO,SAAS,iBAAiB,IAAmB;IAClD,MAAM,UAAU,gBAAgB;IAChC,OAAO;QACL,MAAM,QAAQ,IAAI;QAClB,OAAO,QAAQ,KAAK;QACpB,aAAa,QAAQ,WAAW;QAChC,UAAU,iBAAiB;QAC3B,aAAa,QAAQ,WAAW;QAChC,SAAS,QAAQ,OAAO;IAC1B;AACF;AAEA,iBAAiB;AACjB,SAAS,iBAAiB,IAAmB;IAC3C,MAAM,eAAe;QAAC;QAAc;QAAS;QAAO;KAAO;IAE3D,MAAM,eAAe;QACnB,OAAO;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QACvC,SAAS;YAAC;YAAQ;YAAU;YAAQ;SAAO;QAC3C,YAAY;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC5C,SAAS;YAAC;YAAY;YAAY;YAAY;SAAW;IAC3D;IAEA,OAAO;WAAI;WAAiB,YAAY,CAAC,KAAK;KAAC;AACjD;AAGO,SAAS;IACd,OAAO;QAAC;QAAS;QAAW;QAAc;KAAU;AACtD;AAGO,SAAS,wBAAwB,WAA2B;IACjE,OAAO,uBACJ,MAAM,CAAC,CAAA,OAAQ,SAAS,aACxB,GAAG,CAAC,CAAA;QACH,MAAM,OAAO,iBAAiB;QAC9B,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,MAAM,CAAC,CAAC,EAAE,MAAM;YAChB,aAAa,KAAK,WAAW;QAC/B;IACF;AACJ;AAGO,SAAS,kBAAkB,UAAkB;IAClD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO,uBAAuB,QAAQ,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Breadcrumb面包屑导航组件\n// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getBreadcrumbs } from '@/lib/navigation';\nimport { BreadcrumbProps } from '@/lib/types';\n\nconst Breadcrumb: React.FC<BreadcrumbProps> = React.memo(({\n  pathname,\n  maxItems = 5,\n  className,\n  ...props\n}) => {\n  // 获取面包屑数据\n  const breadcrumbs = React.useMemo(() => getBreadcrumbs(pathname), [pathname]);\n\n  // 如果只有首页，不显示面包屑\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  // 处理超长路径的截断\n  const displayBreadcrumbs = breadcrumbs.length > maxItems\n    ? [\n        breadcrumbs[0], // 首页\n        { name: '...', href: '#', isEllipsis: true },\n        ...breadcrumbs.slice(-2) // 最后两项\n      ]\n    : breadcrumbs;\n\n  return (\n    <motion.nav\n      initial={{ opacity: 0, y: -10 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n      aria-label=\"面包屑导航\"\n      className={cn(\n        'flex items-center space-x-1 text-sm',\n        'py-2 px-1',\n        className\n      )}\n      {...props}\n    >\n      <ol className=\"flex items-center space-x-1 md:space-x-2\">\n        {displayBreadcrumbs.map((item, index) => {\n          const isLast = index === displayBreadcrumbs.length - 1;\n          const isEllipsis = 'isEllipsis' in item && item.isEllipsis;\n          const isHome = item.href === '/';\n\n          return (\n            <li key={`${item.href}-${index}`} className=\"flex items-center\">\n              {/* 分隔符 */}\n              {index > 0 && (\n                <ChevronRight \n                  className=\"w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0\" \n                  aria-hidden=\"true\"\n                />\n              )}\n\n              {/* 面包屑项 */}\n              {isEllipsis ? (\n                <span className=\"text-gray-400 px-1\">...</span>\n              ) : isLast ? (\n                // 当前页面（不可点击）\n                <span\n                  className={cn(\n                    'font-medium text-mysql-primary',\n                    'px-2 py-1 rounded-md',\n                    'bg-mysql-primary/5',\n                    'flex items-center space-x-1'\n                  )}\n                  aria-current=\"page\"\n                >\n                  {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                  <span className={cn(\n                    'truncate max-w-[80px] md:max-w-[120px]',\n                    isHome && 'hidden md:inline'\n                  )}>\n                    {item.name}\n                  </span>\n                </span>\n              ) : (\n                // 可点击的面包屑项\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'text-gray-600 hover:text-mysql-primary',\n                      'px-2 py-1 rounded-md',\n                      'hover:bg-mysql-primary/5',\n                      'transition-all duration-200',\n                      'flex items-center space-x-1',\n                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'\n                    )}\n                  >\n                    {isHome && <Home className=\"w-3 h-3 md:w-4 md:h-4\" />}\n                    <span className={cn(\n                      'truncate max-w-[80px] md:max-w-[120px]',\n                      isHome && 'hidden md:inline'\n                    )}>\n                      {item.name}\n                    </span>\n                  </Link>\n                </motion.div>\n              )}\n            </li>\n          );\n        })}\n      </ol>\n    </motion.nav>\n  );\n});\n\nBreadcrumb.displayName = 'Breadcrumb';\n\nexport default Breadcrumb;\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,mCAAmC;AAEnC;AACA;AACA;AACA;AAAA;AACA;AACA;AAVA;;;;;;;;AAaA,MAAM,2BAAwC,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EACxD,QAAQ,EACR,WAAW,CAAC,EACZ,SAAS,EACT,GAAG,OACJ;IACC,UAAU;IACV,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QAAC;KAAS;IAE5E,gBAAgB;IAChB,IAAI,YAAY,MAAM,IAAI,GAAG;QAC3B,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,qBAAqB,YAAY,MAAM,GAAG,WAC5C;QACE,WAAW,CAAC,EAAE;QACd;YAAE,MAAM;YAAO,MAAM;YAAK,YAAY;QAAK;WACxC,YAAY,KAAK,CAAC,CAAC,GAAG,OAAO;KACjC,GACD;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,cAAW;QACX,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,uCACA,aACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM;gBAC7B,MAAM,SAAS,UAAU,mBAAmB,MAAM,GAAG;gBACrD,MAAM,aAAa,gBAAgB,QAAQ,KAAK,UAAU;gBAC1D,MAAM,SAAS,KAAK,IAAI,KAAK;gBAE7B,qBACE,8OAAC;oBAAiC,WAAU;;wBAEzC,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;4BACX,WAAU;4BACV,eAAY;;;;;;wBAKf,2BACC,8OAAC;4BAAK,WAAU;sCAAqB;;;;;mCACnC,SACF,aAAa;sCACb,8OAAC;4BACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,kCACA,wBACA,sBACA;4BAEF,gBAAa;;gCAEZ,wBAAU,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;8CAET,KAAK,IAAI;;;;;;;;;;;mCAId,WAAW;sCACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0CACA,wBACA,4BACA,+BACA,+BACA;;oCAGD,wBAAU,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAChB,0CACA,UAAU;kDAET,KAAK,IAAI;;;;;;;;;;;;;;;;;;mBArDX,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;YA4DpC;;;;;;;;;;;AAIR;AAEA,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/LegalPageLayout.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - LegalPageLayout法律页面布局组件\n// 为法律声明页面提供统一的布局结构和视觉风格\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ArrowLeft, Scale, Clock, ChevronRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { LegalPageProps } from '@/lib/types';\nimport { getLegalNavigationLinks, formatLastUpdated } from '@/lib/legal';\nimport Breadcrumb from '@/components/ui/Breadcrumb';\n\ninterface LegalPageLayoutProps extends LegalPageProps {\n  children: React.ReactNode;\n  pathname: string;\n}\n\nconst LegalPageLayout: React.FC<LegalPageLayoutProps> = React.memo(({\n  type,\n  title,\n  lastUpdated,\n  children,\n  pathname,\n  className,\n  ...props\n}) => {\n  // 获取其他法律页面的导航链接\n  const navigationLinks = React.useMemo(() => getLegalNavigationLinks(type), [type]);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n      className={cn(\n        'min-h-screen bg-gradient-to-br from-gray-50 to-white',\n        className\n      )}\n      {...props}\n    >\n      {/* 页面容器 */}\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        \n        {/* 面包屑导航 */}\n        <div className=\"mb-6\">\n          <Breadcrumb pathname={pathname} />\n        </div>\n\n        {/* 返回首页链接 */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.1, duration: 0.3 }}\n          className=\"mb-8\"\n        >\n          <Link\n            href=\"/\"\n            className={cn(\n              'inline-flex items-center space-x-2',\n              'text-mysql-primary hover:text-mysql-primary-dark',\n              'transition-colors duration-200',\n              'group'\n            )}\n          >\n            <ArrowLeft className=\"w-4 h-4 transition-transform group-hover:-translate-x-1\" />\n            <span className=\"text-sm font-medium\">返回首页</span>\n          </Link>\n        </motion.div>\n\n        {/* 页面标题区域 */}\n        <motion.header\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2, duration: 0.4 }}\n          className=\"mb-12 text-center\"\n        >\n          {/* 法律图标 */}\n          <div className=\"flex justify-center mb-6\">\n            <div className={cn(\n              'w-16 h-16 rounded-full',\n              'bg-mysql-primary/10 flex items-center justify-center',\n              'border-2 border-mysql-primary/20'\n            )}>\n              <Scale className=\"w-8 h-8 text-mysql-primary\" />\n            </div>\n          </div>\n\n          {/* 页面标题 */}\n          <h1 className={cn(\n            'text-3xl md:text-4xl lg:text-5xl font-bold',\n            'text-gray-900 mb-4',\n            'leading-tight'\n          )}>\n            {title}\n          </h1>\n\n          {/* 最后更新时间 */}\n          <div className=\"flex items-center justify-center space-x-2 text-gray-600\">\n            <Clock className=\"w-4 h-4\" />\n            <span className=\"text-sm\">\n              最后更新：{formatLastUpdated(lastUpdated)}\n            </span>\n          </div>\n        </motion.header>\n\n        {/* 主要内容区域 */}\n        <motion.main\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3, duration: 0.4 }}\n          className={cn(\n            'bg-white rounded-xl shadow-lg',\n            'border border-gray-200',\n            'p-6 md:p-8 lg:p-12',\n            'mb-12'\n          )}\n        >\n          <div className={cn(\n            'prose prose-lg max-w-none',\n            'prose-headings:text-gray-900 prose-headings:font-semibold',\n            'prose-p:text-gray-700 prose-p:leading-relaxed',\n            'prose-a:text-mysql-primary prose-a:no-underline hover:prose-a:underline',\n            'prose-strong:text-gray-900',\n            'prose-ul:text-gray-700 prose-ol:text-gray-700',\n            'prose-li:marker:text-mysql-primary'\n          )}>\n            {children}\n          </div>\n        </motion.main>\n\n        {/* 法律页面间导航 */}\n        {navigationLinks.length > 0 && (\n          <motion.section\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.4 }}\n            className=\"mb-12\"\n          >\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              其他法律声明\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {navigationLinks.map((link) => (\n                <motion.div\n                  key={link.type}\n                  whileHover={{ scale: 1.02, y: -2 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Link\n                    href={link.href}\n                    className={cn(\n                      'block p-4 rounded-lg border border-gray-200',\n                      'bg-white hover:bg-gray-50',\n                      'transition-all duration-200',\n                      'group'\n                    )}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900 group-hover:text-mysql-primary\">\n                          {link.title}\n                        </h3>\n                        <p className=\"text-sm text-gray-600 mt-1\">\n                          {link.description}\n                        </p>\n                      </div>\n                      <ChevronRight className=\"w-5 h-5 text-gray-400 group-hover:text-mysql-primary transition-colors\" />\n                    </div>\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </motion.section>\n        )}\n\n        {/* 底部提示信息 */}\n        <motion.footer\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5, duration: 0.4 }}\n          className={cn(\n            'text-center py-8 px-6',\n            'bg-gray-50 rounded-lg',\n            'border border-gray-200'\n          )}\n        >\n          <p className=\"text-gray-600 text-sm leading-relaxed\">\n            如您对本{title}有任何疑问，请通过\n            <Link \n              href=\"/contact\" \n              className=\"text-mysql-primary hover:text-mysql-primary-dark mx-1\"\n            >\n              联系我们\n            </Link>\n            页面与我们取得联系。\n          </p>\n          <p className=\"text-gray-500 text-xs mt-2\">\n            MySQLAi.de 致力于为用户提供透明、公正的服务条款\n          </p>\n        </motion.footer>\n      </div>\n    </motion.div>\n  );\n});\n\nLegalPageLayout.displayName = 'LegalPageLayout';\n\nexport default LegalPageLayout;\n"], "names": [], "mappings": ";;;;AAEA,uCAAuC;AACvC,wBAAwB;AAExB;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAZA;;;;;;;;;AAmBA,MAAM,gCAAkD,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAClE,IAAI,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,gBAAgB;IAChB,MAAM,kBAAkB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO;QAAC;KAAK;IAEjF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAGT,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oJAAA,CAAA,UAAU;wBAAC,UAAU;;;;;;;;;;;8BAIxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sCACA,oDACA,kCACA;;0CAGF,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;8BAK1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACf,0BACA,wDACA;0CAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAKrB,8OAAC;4BAAG,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACd,8CACA,sBACA;sCAEC;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;;wCAAU;wCAClB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;;;;;;;;;;;;;;;;;;;8BAM9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iCACA,0BACA,sBACA;8BAGF,cAAA,8OAAC;wBAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACf,6BACA,6DACA,iDACA,2EACA,8BACA,iDACA;kCAEC;;;;;;;;;;;gBAKJ,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;oBACb,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+CACA,6BACA,+BACA;kDAGF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;8DAGrB,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAtBvB,KAAK,IAAI;;;;;;;;;;;;;;;;8BAgCxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yBACA,yBACA;;sCAGF,8OAAC;4BAAE,WAAU;;gCAAwC;gCAC9C;gCAAM;8CACX,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAEM;;;;;;;sCAGT,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAEA,gBAAgB,WAAW,GAAG;uCAEf", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "file": "scale.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/scale.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: '7g6ntu' }],\n  ['path', { d: 'm2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: 'ijws7r' }],\n  ['path', { d: 'M7 21h10', key: '1b0cd5' }],\n  ['path', { d: 'M12 3v18', key: '108xh3' }],\n  ['path', { d: 'M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2', key: '3gwbw2' }],\n];\n\n/**\n * @component @name Scale\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0ibTIgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0iTTcgMjFoMTAiIC8+CiAgPHBhdGggZD0iTTEyIDN2MTgiIC8+CiAgPHBhdGggZD0iTTMgN2gyYzIgMCA1LTEgNy0yIDIgMSA1IDIgNyAyaDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scale\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Scale = createLucideIcon('scale', __iconNode);\n\nexport default Scale;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///D:/MysqlAi.De/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}