{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/utils.ts"], "sourcesContent": ["// MySQLAi.de - 工具函数库\n// 提供通用的工具函数和辅助方法\n\nimport { type ClassValue, clsx } from 'clsx';\nimport type { Database } from './database.types';\nimport type { KnowledgeCategory, KnowledgeItem, CodeExample } from './types';\n\n// 图标名称到 emoji 的映射\nconst ICON_MAP: Record<string, string> = {\n  'Database': '🗄️',\n  'Server': '🖥️',\n  'Table': '📋',\n  'Edit': '✏️',\n  'Search': '🔍',\n  'Settings': '⚙️',\n  'BookOpen': '📖',\n  'Code': '💻',\n  'FileText': '📄',\n  'FolderOpen': '📁',\n  'Users': '👥',\n  'Mail': '📧',\n  'Home': '🏠',\n  'BarChart3': '📊',\n  'Wrench': '🔧',\n  'GitBranch': '🌿',\n  'Download': '⬇️'\n};\n\n// 数据库类型别名\ntype DbKnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];\ntype DbKnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];\ntype DbCodeExample = Database['public']['Tables']['code_examples']['Row'];\n\n/**\n * 将图标名称转换为对应的 emoji\n */\nexport function getIconEmoji(iconName: string): string {\n  return ICON_MAP[iconName] || '📁';\n}\n\n// CSS类名合并工具（兼容Tailwind CSS）\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n// 延迟执行工具\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n// 格式化日期\nexport function formatDate(\n  date: Date | string,\n  options: Intl.DateTimeFormatOptions = {}\n): string {\n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    timeZone: 'Asia/Shanghai', // 中国标准时间\n  };\n  \n  const finalOptions = { ...defaultOptions, ...options };\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  return new Intl.DateTimeFormat('zh-CN', finalOptions).format(dateObj);\n}\n\n// 格式化数字\nexport function formatNumber(\n  num: number,\n  options: Intl.NumberFormatOptions = {}\n): string {\n  return new Intl.NumberFormat('zh-CN', options).format(num);\n}\n\n// 生成随机ID\nexport function generateId(prefix: string = 'id'): string {\n  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 验证手机号格式（中国大陆）\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^1[3-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n}\n\n// 截断文本\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// 首字母大写\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n// 转换为短横线命名\nexport function kebabCase(str: string): string {\n  return str\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\n    .replace(/[\\s_]+/g, '-')\n    .toLowerCase();\n}\n\n// 转换为驼峰命名\nexport function camelCase(str: string): string {\n  return str\n    .replace(/[-_\\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))\n    .replace(/^[A-Z]/, char => char.toLowerCase());\n}\n\n// 深度克隆对象\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n// 获取对象深层属性值\nexport function getNestedValue(obj: any, path: string, defaultValue: any = undefined): any {\n  const keys = path.split('.');\n  let result = obj;\n  \n  for (const key of keys) {\n    if (result === null || result === undefined || !(key in result)) {\n      return defaultValue;\n    }\n    result = result[key];\n  }\n  \n  return result;\n}\n\n// 设置对象深层属性值\nexport function setNestedValue(obj: any, path: string, value: any): void {\n  const keys = path.split('.');\n  const lastKey = keys.pop()!;\n  let current = obj;\n  \n  for (const key of keys) {\n    if (!(key in current) || typeof current[key] !== 'object') {\n      current[key] = {};\n    }\n    current = current[key];\n  }\n  \n  current[lastKey] = value;\n}\n\n// 数组去重\nexport function uniqueArray<T>(array: T[], key?: keyof T): T[] {\n  if (!key) {\n    return [...new Set(array)];\n  }\n  \n  const seen = new Set();\n  return array.filter(item => {\n    const value = item[key];\n    if (seen.has(value)) {\n      return false;\n    }\n    seen.add(value);\n    return true;\n  });\n}\n\n// 数组分组\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const groupKey = String(item[key]);\n    if (!groups[groupKey]) {\n      groups[groupKey] = [];\n    }\n    groups[groupKey].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < 768;\n}\n\n// 检查是否为暗色主题\nexport function isDarkMode(): boolean {\n  if (typeof window === 'undefined') return false;\n  return window.matchMedia('(prefers-color-scheme: dark)').matches;\n}\n\n// 平滑滚动到元素\nexport function scrollToElement(\n  elementId: string,\n  offset: number = 0,\n  behavior: ScrollBehavior = 'smooth'\n): void {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.offsetTop - offset;\n    window.scrollTo({\n      top: elementPosition,\n      behavior,\n    });\n  }\n}\n\n// 复制文本到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    console.error('复制失败:', err);\n    return false;\n  }\n}\n\n// 下载文件\nexport function downloadFile(url: string, filename: string): void {\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 生成颜色变体\nexport function generateColorVariants(baseColor: string): {\n  50: string;\n  100: string;\n  200: string;\n  300: string;\n  400: string;\n  500: string;\n  600: string;\n  700: string;\n  800: string;\n  900: string;\n} {\n  // 这是一个简化版本，实际项目中可能需要更复杂的颜色计算\n  return {\n    50: `${baseColor}0D`,   // 5% opacity\n    100: `${baseColor}1A`,  // 10% opacity\n    200: `${baseColor}33`,  // 20% opacity\n    300: `${baseColor}4D`,  // 30% opacity\n    400: `${baseColor}66`,  // 40% opacity\n    500: baseColor,         // base color\n    600: `${baseColor}CC`,  // 80% opacity\n    700: `${baseColor}B3`,  // 70% opacity\n    800: `${baseColor}99`,  // 60% opacity\n    900: `${baseColor}80`,  // 50% opacity\n  };\n}\n\n// ============================================================================\n// 数据库类型映射工具函数\n// ============================================================================\n\n/**\n * 将数据库的 knowledge_categories 行映射到前端 KnowledgeCategory 类型\n */\nexport function mapDatabaseCategoryToFrontend(dbCategory: DbKnowledgeCategory): KnowledgeCategory {\n  return {\n    id: dbCategory.id,\n    name: dbCategory.name,\n    description: dbCategory.description,\n    icon: dbCategory.icon,\n    color: dbCategory.color,\n    order_index: dbCategory.order_index,\n    created_at: dbCategory.created_at,\n  };\n}\n\n/**\n * 将前端 KnowledgeCategory 类型映射到数据库插入格式\n */\nexport function mapFrontendCategoryToDatabase(\n  category: Omit<KnowledgeCategory, 'id'> & { id?: string }\n): Database['public']['Tables']['knowledge_categories']['Insert'] {\n  return {\n    id: category.id || generateId('cat'),\n    name: category.name,\n    description: category.description,\n    icon: category.icon,\n    color: category.color,\n    order_index: category.order_index,\n  };\n}\n\n/**\n * 将数据库的 code_examples 行映射到前端 CodeExample 类型\n */\nexport function mapDatabaseCodeExampleToFrontend(dbCodeExample: DbCodeExample): CodeExample {\n  return {\n    id: dbCodeExample.id,\n    article_id: dbCodeExample.article_id,\n    title: dbCodeExample.title,\n    code: dbCodeExample.code,\n    language: dbCodeExample.language,\n    description: dbCodeExample.description,\n    order_index: dbCodeExample.order_index,\n    created_at: dbCodeExample.created_at,\n  };\n}\n\n/**\n * 将前端 CodeExample 类型映射到数据库插入格式\n */\nexport function mapFrontendCodeExampleToDatabase(\n  codeExample: Omit<CodeExample, 'id'> & { id?: string; articleId: string }\n): Database['public']['Tables']['code_examples']['Insert'] {\n  return {\n    id: codeExample.id || generateId('code'),\n    article_id: codeExample.articleId, // 字段名转换：articleId → article_id\n    title: codeExample.title,\n    code: codeExample.code,\n    language: codeExample.language,\n    description: codeExample.description || null,\n    order_index: 0, // 默认排序\n  };\n}\n\n/**\n * 将数据库的 knowledge_articles 行映射到前端 KnowledgeItem 类型\n * 注意：此函数不包含关联数据（codeExamples, relatedItems），需要单独查询\n */\nexport function mapDatabaseArticleToFrontend(\n  dbArticle: DbKnowledgeArticle\n): KnowledgeItem {\n  return {\n    id: dbArticle.id,\n    title: dbArticle.title,\n    description: dbArticle.description,\n    content: dbArticle.content,\n    category_id: dbArticle.category_id,\n    tags: dbArticle.tags,\n    difficulty: dbArticle.difficulty,\n    order_index: dbArticle.order_index,\n    last_updated: dbArticle.last_updated,\n    created_at: dbArticle.created_at,\n    updated_at: dbArticle.updated_at,\n  };\n}\n\n/**\n * 将前端 KnowledgeItem 类型映射到数据库插入格式\n */\nexport function mapFrontendArticleToDatabase(\n  article: Omit<KnowledgeItem, 'id'> & { id?: string }\n): Database['public']['Tables']['knowledge_articles']['Insert'] {\n  return {\n    id: article.id || generateId('article'),\n    title: article.title,\n    description: article.description,\n    content: article.content,\n    category_id: article.category_id,\n    tags: article.tags && article.tags.length > 0 ? article.tags : null,\n    difficulty: article.difficulty,\n    order_index: article.order_index,\n    last_updated: article.last_updated,\n  };\n}\n\n// ============================================================================\n// 批量映射工具函数\n// ============================================================================\n\n/**\n * 批量映射数据库分类到前端格式\n */\nexport function mapDatabaseCategoriesToFrontend(dbCategories: DbKnowledgeCategory[]): KnowledgeCategory[] {\n  return dbCategories.map(mapDatabaseCategoryToFrontend);\n}\n\n/**\n * 批量映射数据库文章到前端格式\n */\nexport function mapDatabaseArticlesToFrontend(dbArticles: DbKnowledgeArticle[]): KnowledgeItem[] {\n  return dbArticles.map(article => mapDatabaseArticleToFrontend(article));\n}\n\n/**\n * 批量映射数据库代码示例到前端格式\n */\nexport function mapDatabaseCodeExamplesToFrontend(dbCodeExamples: DbCodeExample[]): CodeExample[] {\n  return dbCodeExamples.map(mapDatabaseCodeExampleToFrontend);\n}\n\n/**\n * 安全的字段映射函数 - 处理可能的 null/undefined 值\n */\nexport function safeFieldMap<T, R>(\n  value: T | null | undefined,\n  mapper: (value: T) => R,\n  defaultValue: R\n): R {\n  return value !== null && value !== undefined ? mapper(value) : defaultValue;\n}\n\n/**\n * 验证映射结果的完整性\n */\nexport function validateMappedData<T extends { id: string }>(\n  data: T[],\n  requiredFields: (keyof T)[]\n): { isValid: boolean; errors: string[] } {\n  const errors: string[] = [];\n\n  data.forEach((item, index) => {\n    requiredFields.forEach(field => {\n      if (!item[field]) {\n        errors.push(`Item ${index} missing required field: ${String(field)}`);\n      }\n    });\n  });\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjB;;AAIA,kBAAkB;AAClB,MAAM,WAAmC;IACvC,YAAY;IACZ,UAAU;IACV,SAAS;IACT,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,YAAY;IACZ,cAAc;IACd,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,UAAU;IACV,aAAa;IACb,YAAY;AACd;AAUO,SAAS,aAAa,QAAgB;IAC3C,OAAO,QAAQ,CAAC,SAAS,IAAI;AAC/B;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAGO,SAAS,WACd,IAAmB,EACnB,UAAsC,CAAC,CAAC;IAExC,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,MAAM,eAAe;QAAE,GAAG,cAAc;QAAE,GAAG,OAAO;IAAC;IACrD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,cAAc,MAAM,CAAC;AAC/D;AAGO,SAAS,aACd,GAAW,EACX,UAAoC,CAAC,CAAC;IAEtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,SAAS,MAAM,CAAC;AACxD;AAGO,SAAS,WAAW,SAAiB,IAAI;IAC9C,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC/D;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAGO,SAAS,UAAU,GAAW;IACnC,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAGO,SAAS,UAAU,GAAW;IACnC,OAAO,IACJ,OAAO,CAAC,gBAAgB,CAAC,GAAG,OAAU,OAAO,KAAK,WAAW,KAAK,IAClE,OAAO,CAAC,UAAU,CAAA,OAAQ,KAAK,WAAW;AAC/C;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,eAAe,GAAQ,EAAE,IAAY,EAAE,eAAoB,SAAS;IAClF,MAAM,OAAO,KAAK,KAAK,CAAC;IACxB,IAAI,SAAS;IAEb,KAAK,MAAM,OAAO,KAAM;QACtB,IAAI,WAAW,QAAQ,WAAW,aAAa,CAAC,CAAC,OAAO,MAAM,GAAG;YAC/D,OAAO;QACT;QACA,SAAS,MAAM,CAAC,IAAI;IACtB;IAEA,OAAO;AACT;AAGO,SAAS,eAAe,GAAQ,EAAE,IAAY,EAAE,KAAU;IAC/D,MAAM,OAAO,KAAK,KAAK,CAAC;IACxB,MAAM,UAAU,KAAK,GAAG;IACxB,IAAI,UAAU;IAEd,KAAK,MAAM,OAAO,KAAM;QACtB,IAAI,CAAC,CAAC,OAAO,OAAO,KAAK,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU;YACzD,OAAO,CAAC,IAAI,GAAG,CAAC;QAClB;QACA,UAAU,OAAO,CAAC,IAAI;IACxB;IAEA,OAAO,CAAC,QAAQ,GAAG;AACrB;AAGO,SAAS,YAAe,KAAU,EAAE,GAAa;IACtD,IAAI,CAAC,KAAK;QACR,OAAO;eAAI,IAAI,IAAI;SAAO;IAC5B;IAEA,MAAM,OAAO,IAAI;IACjB,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,IAAI,KAAK,GAAG,CAAC,QAAQ;YACnB,OAAO;QACT;QACA,KAAK,GAAG,CAAC;QACT,OAAO;IACT;AACF;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,WAAW,OAAO,IAAI,CAAC,IAAI;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QACA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;AACN;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBACd,SAAiB,EACjB,SAAiB,CAAC,EAClB,WAA2B,QAAQ;IAEnC,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,SAAS,GAAG;QAC5C,OAAO,QAAQ,CAAC;YACd,KAAK;YACL;QACF;IACF;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW,EAAE,QAAgB;IACxD,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,sBAAsB,SAAiB;IAYrD,6BAA6B;IAC7B,OAAO;QACL,IAAI,GAAG,UAAU,EAAE,CAAC;QACpB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK;QACL,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;QACrB,KAAK,GAAG,UAAU,EAAE,CAAC;IACvB;AACF;AASO,SAAS,8BAA8B,UAA+B;IAC3E,OAAO;QACL,IAAI,WAAW,EAAE;QACjB,MAAM,WAAW,IAAI;QACrB,aAAa,WAAW,WAAW;QACnC,MAAM,WAAW,IAAI;QACrB,OAAO,WAAW,KAAK;QACvB,aAAa,WAAW,WAAW;QACnC,YAAY,WAAW,UAAU;IACnC;AACF;AAKO,SAAS,8BACd,QAAyD;IAEzD,OAAO;QACL,IAAI,SAAS,EAAE,IAAI,WAAW;QAC9B,MAAM,SAAS,IAAI;QACnB,aAAa,SAAS,WAAW;QACjC,MAAM,SAAS,IAAI;QACnB,OAAO,SAAS,KAAK;QACrB,aAAa,SAAS,WAAW;IACnC;AACF;AAKO,SAAS,iCAAiC,aAA4B;IAC3E,OAAO;QACL,IAAI,cAAc,EAAE;QACpB,YAAY,cAAc,UAAU;QACpC,OAAO,cAAc,KAAK;QAC1B,MAAM,cAAc,IAAI;QACxB,UAAU,cAAc,QAAQ;QAChC,aAAa,cAAc,WAAW;QACtC,aAAa,cAAc,WAAW;QACtC,YAAY,cAAc,UAAU;IACtC;AACF;AAKO,SAAS,iCACd,WAAyE;IAEzE,OAAO;QACL,IAAI,YAAY,EAAE,IAAI,WAAW;QACjC,YAAY,YAAY,SAAS;QACjC,OAAO,YAAY,KAAK;QACxB,MAAM,YAAY,IAAI;QACtB,UAAU,YAAY,QAAQ;QAC9B,aAAa,YAAY,WAAW,IAAI;QACxC,aAAa;IACf;AACF;AAMO,SAAS,6BACd,SAA6B;IAE7B,OAAO;QACL,IAAI,UAAU,EAAE;QAChB,OAAO,UAAU,KAAK;QACtB,aAAa,UAAU,WAAW;QAClC,SAAS,UAAU,OAAO;QAC1B,aAAa,UAAU,WAAW;QAClC,MAAM,UAAU,IAAI;QACpB,YAAY,UAAU,UAAU;QAChC,aAAa,UAAU,WAAW;QAClC,cAAc,UAAU,YAAY;QACpC,YAAY,UAAU,UAAU;QAChC,YAAY,UAAU,UAAU;IAClC;AACF;AAKO,SAAS,6BACd,OAAoD;IAEpD,OAAO;QACL,IAAI,QAAQ,EAAE,IAAI,WAAW;QAC7B,OAAO,QAAQ,KAAK;QACpB,aAAa,QAAQ,WAAW;QAChC,SAAS,QAAQ,OAAO;QACxB,aAAa,QAAQ,WAAW;QAChC,MAAM,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,IAAI,GAAG;QAC/D,YAAY,QAAQ,UAAU;QAC9B,aAAa,QAAQ,WAAW;QAChC,cAAc,QAAQ,YAAY;IACpC;AACF;AASO,SAAS,gCAAgC,YAAmC;IACjF,OAAO,aAAa,GAAG,CAAC;AAC1B;AAKO,SAAS,8BAA8B,UAAgC;IAC5E,OAAO,WAAW,GAAG,CAAC,CAAA,UAAW,6BAA6B;AAChE;AAKO,SAAS,kCAAkC,cAA+B;IAC/E,OAAO,eAAe,GAAG,CAAC;AAC5B;AAKO,SAAS,aACd,KAA2B,EAC3B,MAAuB,EACvB,YAAe;IAEf,OAAO,UAAU,QAAQ,UAAU,YAAY,OAAO,SAAS;AACjE;AAKO,SAAS,mBACd,IAAS,EACT,cAA2B;IAE3B,MAAM,SAAmB,EAAE;IAE3B,KAAK,OAAO,CAAC,CAAC,MAAM;QAClB,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,yBAAyB,EAAE,OAAO,QAAQ;YACtE;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/constants.ts"], "sourcesContent": ["// MySQLAi.de - 常量配置文件\n// 包含项目中使用的所有常量、配置和静态数据\n\nimport { ThemeColors, PageMetadata } from './types';\n\n// 网站基本信息\nexport const SITE_CONFIG = {\n  name: 'MySQLAi.de',\n  title: 'MySQL智能分析专家',\n  description: '专业的数据库知识分享与项目管理平台',\n  url: 'https://mysqlai.de',\n  author: 'MySQLAi Team',\n  keywords: ['MySQL', '数据库', 'AI分析', '项目管理', '知识分享', '性能优化'],\n};\n\n// MySQL主题色彩配置\nexport const THEME_COLORS: ThemeColors = {\n  primary: '#00758F',        // MySQL官方蓝\n  primaryDark: '#003545',    // 深蓝色\n  primaryLight: '#E6F3F7',   // 浅蓝色\n  accent: '#0066CC',         // 强调色\n  text: '#2D3748',           // 主文字色\n  textLight: '#718096',      // 浅文字色\n  border: '#E2E8F0',         // 边框色\n  success: '#38A169',        // 成功色\n  warning: '#D69E2E',        // 警告色\n  error: '#E53E3E',          // 错误色\n} as const;\n\n// Chen ER图标准黑色主题配置\nexport const CHEN_ER_COLORS = {\n  primary: '#000000',        // 黑色 - 实体边框\n  primaryDark: '#000000',    // 黑色 - 深色变体\n  primaryLight: '#FFFFFF',   // 白色 - 浅色背景\n  accent: '#000000',         // 黑色 - 属性边框\n  text: '#000000',           // 黑色 - 文字色\n  textLight: '#000000',      // 黑色 - 浅文字色\n  border: '#000000',         // 黑色 - 边框色\n  success: '#000000',        // 黑色 - 关系边框\n  warning: '#000000',        // 黑色 - 主键标记\n  error: '#000000',          // 黑色 - 错误色\n  white: '#FFFFFF',          // 白色 - 填充色\n  background: '#FFFFFF',     // 白色 - 背景色\n} as const;\n\n// 注意：导航菜单配置已移至 @/lib/navigation.ts 中的 MAIN_NAVIGATION\n\n// 页面元数据配置\nexport const PAGE_METADATA: Record<string, PageMetadata> = {\n  home: {\n    title: `${SITE_CONFIG.title} - ${SITE_CONFIG.description}`,\n    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',\n    keywords: [...SITE_CONFIG.keywords, '首页', '主页'],\n  },\n  knowledge: {\n    title: `MySQL知识库 - ${SITE_CONFIG.name}`,\n    description: '丰富的MySQL知识库，包含数据库优化、性能调优、最佳实践等专业内容。',\n    keywords: [...SITE_CONFIG.keywords, '知识库', '教程', '最佳实践'],\n  },\n  projects: {\n    title: `项目管理 - ${SITE_CONFIG.name}`,\n    description: '专业的项目管理工具，支持任务跟踪、进度管理、团队协作。',\n    keywords: [...SITE_CONFIG.keywords, '项目管理', '任务跟踪', '团队协作'],\n  },\n  reports: {\n    title: `报告展示 - ${SITE_CONFIG.name}`,\n    description: '支持图片、视频的多媒体项目报告展示平台。',\n    keywords: [...SITE_CONFIG.keywords, '报告展示', '多媒体', '数据可视化'],\n  },\n  about: {\n    title: `关于我们 - ${SITE_CONFIG.name}`,\n    description: '了解MySQLAi.de团队，我们的使命是为用户提供最专业的MySQL解决方案。',\n    keywords: [...SITE_CONFIG.keywords, '关于我们', '团队介绍', '公司简介'],\n  },\n  contact: {\n    title: `联系我们 - ${SITE_CONFIG.name}`,\n    description: '联系MySQLAi.de团队，获取专业的MySQL咨询和技术支持。',\n    keywords: [...SITE_CONFIG.keywords, '联系我们', '技术支持', '咨询服务'],\n  },\n  // 法律声明页面元数据\n  terms: {\n    title: `服务条款 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台服务使用条款和用户协议，明确用户权利义务，保障双方合法权益。',\n    keywords: [...SITE_CONFIG.keywords, '服务条款', '用户协议', '使用条款', '服务协议', '法律声明'],\n  },\n  privacy: {\n    title: `隐私政策 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台用户隐私保护政策，详细说明个人信息收集、使用、保护措施。',\n    keywords: [...SITE_CONFIG.keywords, '隐私政策', '个人信息保护', '数据保护', '隐私保护', '信息安全'],\n  },\n  disclaimer: {\n    title: `免责声明 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台服务免责条款和责任限制说明，明确服务范围和责任界限。',\n    keywords: [...SITE_CONFIG.keywords, '免责声明', '责任限制', '法律免责', '服务限制', '风险提示'],\n  },\n  cookies: {\n    title: `Cookie政策 - ${SITE_CONFIG.name}`,\n    description: 'MySQLAi.de平台Cookie使用说明和管理指南，保障用户知情权和选择权。',\n    keywords: [...SITE_CONFIG.keywords, 'Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理', '用户隐私'],\n  },\n  // 工具页面元数据\n  tools: {\n    title: `MySQL工具集 - ${SITE_CONFIG.name}`,\n    description: '专业的MySQL工具集合，包含ER图生成、数据库安装配置等实用工具，提升数据库开发效率。',\n    keywords: [...SITE_CONFIG.keywords, 'MySQL工具', '数据库工具', 'ER图生成', 'MySQL安装', '开发工具'],\n  },\n  'tools-er-diagram': {\n    title: `ER图生成工具 - ${SITE_CONFIG.name}`,\n    description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式，提升数据库设计效率。',\n    keywords: [...SITE_CONFIG.keywords, 'ER图生成', '数据库关系图', '数据库设计', '可视化工具', '数据库建模'],\n  },\n  'tools-mysql-installer': {\n    title: `MySQL安装工具 - ${SITE_CONFIG.name}`,\n    description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置，简化数据库部署流程。',\n    keywords: [...SITE_CONFIG.keywords, 'MySQL安装', '数据库安装', '自动配置', '版本管理', '数据库部署'],\n  },\n} as const;\n\n// 功能特性配置\nexport const FEATURES_DATA = [\n  {\n    title: 'MySQL知识库',\n    description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',\n    icon: 'Database',\n    features: [\n      '数据库性能优化',\n      '查询语句调优',\n      '索引设计最佳实践',\n      '架构设计指南',\n    ],\n  },\n  {\n    title: '项目管理',\n    description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',\n    icon: 'FolderOpen',\n    features: [\n      '任务分配与跟踪',\n      '项目进度管理',\n      '团队协作工具',\n      '时间管理优化',\n    ],\n  },\n  {\n    title: '报告展示',\n    description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',\n    icon: 'BarChart3',\n    features: [\n      '多媒体报告支持',\n      '数据可视化图表',\n      '实时数据展示',\n      '自定义报告模板',\n    ],\n  },\n] as const;\n\n// 专业特性配置\nexport const ABOUT_FEATURES = [\n  {\n    title: '智能分析',\n    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',\n    icon: 'Brain',\n  },\n  {\n    title: '专业咨询',\n    description: '资深数据库专家团队，提供一对一的专业咨询服务。',\n    icon: 'Users',\n  },\n  {\n    title: '高效管理',\n    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',\n    icon: 'Zap',\n  },\n  {\n    title: '透明报告',\n    description: '详细的项目报告和数据分析，确保项目进展透明可控。',\n    icon: 'FileText',\n  },\n  {\n    title: '7x24支持',\n    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',\n    icon: 'Clock',\n  },\n] as const;\n\n// 优势展示配置\nexport const ADVANTAGES_DATA = [\n  {\n    title: '🌍 #1 MySQL专家',\n    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',\n    details: '覆盖全球8个地区，超过5万用户信赖',\n    icon: '🌍',\n  },\n  {\n    title: '📝 兼容性与支持',\n    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',\n    details: '支持MySQL 5.7到8.0的所有主流版本',\n    icon: '📝',\n  },\n  {\n    title: '💰 灵活计费',\n    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',\n    details: '透明计费，性价比最高的MySQL服务',\n    icon: '💰',\n  },\n  {\n    title: '⚡ 全球布局',\n    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',\n    details: '全球用户享受一致的高速服务体验',\n    icon: '⚡',\n  },\n  {\n    title: '⏰ 服务保障',\n    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',\n    details: '专业运维团队，99.9%服务可用性保证',\n    icon: '⏰',\n  },\n  {\n    title: '🎈 透明计费',\n    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',\n    details: '无隐藏费用，按实际使用量计费',\n    icon: '🎈',\n  },\n] as const;\n\n// 联系方式配置\nexport const CONTACT_INFO = {\n  supportHours: '7×24小时全天候支持',\n  email: '<EMAIL>',\n  phone: '+86 ************',\n  address: '中国 · 北京 · 朝阳区',\n  socialLinks: [\n    {\n      name: 'GitHub',\n      href: 'https://github.com/mysqlai',\n      icon: 'Github',\n    },\n    {\n      name: '微信',\n      href: '#',\n      icon: 'MessageCircle',\n    },\n    {\n      name: 'QQ群',\n      href: '#',\n      icon: 'Users',\n    },\n  ],\n} as const;\n\n// 页脚配置\nexport const FOOTER_SECTIONS = [\n  {\n    title: '产品服务',\n    links: [\n      { name: 'MySQL优化', href: '/services/optimization' },\n      { name: '性能调优', href: '/services/tuning' },\n      { name: '架构设计', href: '/services/architecture' },\n      { name: '数据迁移', href: '/services/migration' },\n    ],\n  },\n  {\n    title: '解决方案',\n    links: [\n      { name: '企业级方案', href: '/solutions/enterprise' },\n      { name: '云数据库', href: '/solutions/cloud' },\n      { name: '高可用架构', href: '/solutions/ha' },\n      { name: '灾备方案', href: '/solutions/disaster-recovery' },\n    ],\n  },\n  {\n    title: '学习资源',\n    links: [\n      { name: '技术博客', href: '/blog' },\n      { name: '视频教程', href: '/tutorials' },\n      { name: 'API文档', href: '/docs' },\n      { name: '最佳实践', href: '/best-practices' },\n    ],\n  },\n  {\n    title: '关于我们',\n    links: [\n      { name: '公司介绍', href: '/about' },\n      { name: '团队成员', href: '/team' },\n      { name: '招聘信息', href: '/careers' },\n      { name: '联系我们', href: '/contact' },\n    ],\n  },\n] as const;\n\nexport const FOOTER_LEGAL_LINKS = [\n  { name: '服务条款', href: '/terms' },\n  { name: '隐私政策', href: '/privacy' },\n  { name: '免责声明', href: '/disclaimer' },\n] as const;\n\n// 动画配置\nexport const ANIMATION_CONFIG = {\n  duration: {\n    fast: 0.2,\n    normal: 0.3,\n    slow: 0.5,\n  },\n  easing: {\n    easeInOut: [0.4, 0, 0.2, 1],\n    easeOut: [0, 0, 0.2, 1],\n    easeIn: [0.4, 0, 1, 1],\n  },\n  delay: {\n    none: 0,\n    short: 0.1,\n    medium: 0.2,\n    long: 0.3,\n  },\n} as const;\n\n// 响应式断点配置\nexport const BREAKPOINTS = {\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n  '2xl': '1536px',\n} as const;\n\n// 错误消息配置\nexport const ERROR_MESSAGES = {\n  required: '此字段为必填项',\n  email: '请输入有效的邮箱地址',\n  phone: '请输入有效的手机号码',\n  minLength: (min: number) => `最少需要${min}个字符`,\n  maxLength: (max: number) => `最多允许${max}个字符`,\n  network: '网络连接失败，请稍后重试',\n  server: '服务器错误，请联系技术支持',\n  unknown: '未知错误，请稍后重试',\n} as const;\n\n// 成功消息配置\nexport const SUCCESS_MESSAGES = {\n  formSubmit: '表单提交成功！',\n  dataSaved: '数据保存成功！',\n  emailSent: '邮件发送成功！',\n  copied: '已复制到剪贴板',\n} as const;\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,uBAAuB;;;;;;;;;;;;;;;;;AAKhB,MAAM,cAAc;IACzB,MAAM;IACN,OAAO;IACP,aAAa;IACb,KAAK;IACL,QAAQ;IACR,UAAU;QAAC;QAAS;QAAO;QAAQ;QAAQ;QAAQ;KAAO;AAC5D;AAGO,MAAM,eAA4B;IACvC,SAAS;IACT,aAAa;IACb,cAAc;IACd,QAAQ;IACR,MAAM;IACN,WAAW;IACX,QAAQ;IACR,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,SAAS;IACT,aAAa;IACb,cAAc;IACd,QAAQ;IACR,MAAM;IACN,WAAW;IACX,QAAQ;IACR,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,YAAY;AACd;AAKO,MAAM,gBAA8C;IACzD,MAAM;QACJ,OAAO,GAAG,YAAY,KAAK,CAAC,GAAG,EAAE,YAAY,WAAW,EAAE;QAC1D,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAM;SAAK;IACjD;IACA,WAAW;QACT,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAO;YAAM;SAAO;IAC1D;IACA,UAAU;QACR,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAO;SAAQ;IAC7D;IACA,OAAO;QACL,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;SAAO;IAC7D;IACA,YAAY;IACZ,OAAO;QACL,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;IAC7E;IACA,SAAS;QACP,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAU;YAAQ;YAAQ;SAAO;IAC/E;IACA,YAAY;QACV,OAAO,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE;QACnC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;IAC7E;IACA,SAAS;QACP,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAY;YAAY;YAAY;YAAY;SAAO;IAC7F;IACA,UAAU;IACV,OAAO;QACL,OAAO,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE;QACvC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAW;YAAS;YAAS;YAAW;SAAO;IACrF;IACA,oBAAoB;QAClB,OAAO,CAAC,UAAU,EAAE,YAAY,IAAI,EAAE;QACtC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAS;YAAU;YAAS;YAAS;SAAQ;IACnF;IACA,yBAAyB;QACvB,OAAO,CAAC,YAAY,EAAE,YAAY,IAAI,EAAE;QACxC,aAAa;QACb,UAAU;eAAI,YAAY,QAAQ;YAAE;YAAW;YAAS;YAAQ;YAAQ;SAAQ;IAClF;AACF;AAGO,MAAM,gBAAgB;IAC3B;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,iBAAiB;IAC5B;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,eAAe;IAC1B,cAAc;IACd,OAAO;IACP,OAAO;IACP,SAAS;IACT,aAAa;QACX;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;AACH;AAGO,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM;YAAyB;YAClD;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;YACzC;gBAAE,MAAM;gBAAQ,MAAM;YAAyB;YAC/C;gBAAE,MAAM;gBAAQ,MAAM;YAAsB;SAC7C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAS,MAAM;YAAwB;YAC/C;gBAAE,MAAM;gBAAQ,MAAM;YAAmB;YACzC;gBAAE,MAAM;gBAAS,MAAM;YAAgB;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAA+B;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAa;YACnC;gBAAE,MAAM;gBAAS,MAAM;YAAQ;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAkB;SACzC;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAW;SAClC;IACH;CACD;AAEM,MAAM,qBAAqB;IAChC;QAAE,MAAM;QAAQ,MAAM;IAAS;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAW;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAc;CACrC;AAGM,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,QAAQ;QACN,WAAW;YAAC;YAAK;YAAG;YAAK;SAAE;QAC3B,SAAS;YAAC;YAAG;YAAG;YAAK;SAAE;QACvB,QAAQ;YAAC;YAAK;YAAG;YAAG;SAAE;IACxB;IACA,OAAO;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,OAAO;IACP,OAAO;IACP,WAAW,CAAC,MAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;IAC3C,WAAW,CAAC,MAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;IAC3C,SAAS;IACT,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,mBAAmB;IAC9B,YAAY;IACZ,WAAW;IACX,WAAW;IACX,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/lib/navigation.ts"], "sourcesContent": ["// MySQLAi.de - 导航配置文件\n// 管理网站导航相关的配置和工具函数\n\nimport { NavigationItem } from './types';\n\n// 工具导航配置\nexport const TOOLS_NAVIGATION: NavigationItem[] = [\n  {\n    name: 'ER图生成工具',\n    href: '/tools/er-diagram',\n    icon: 'GitBranch',\n  },\n  {\n    name: 'MySQL安装工具',\n    href: '/tools/mysql-installer',\n    icon: 'Download',\n  },\n];\n\n// 主导航菜单配置\nexport const MAIN_NAVIGATION: NavigationItem[] = [\n  {\n    name: '首页',\n    href: '/',\n    icon: 'Home',\n  },\n  {\n    name: '知识库',\n    href: '/knowledge',\n    icon: 'BookOpen',\n  },\n  {\n    name: '项目管理',\n    href: '/projects',\n    icon: 'FolderOpen',\n  },\n  {\n    name: '报告展示',\n    href: '/reports',\n    icon: 'BarChart3',\n  },\n  {\n    name: '关于我们',\n    href: '/about',\n    icon: 'Users',\n  },\n  {\n    name: '工具',\n    href: '/tools',\n    icon: 'Wrench',\n    children: TOOLS_NAVIGATION,\n  },\n  {\n    name: '联系我们',\n    href: '/contact',\n    icon: 'Mail',\n  },\n];\n\n// 页脚导航配置\nexport const FOOTER_NAVIGATION = {\n  产品服务: [\n    { name: 'MySQL优化', href: '/services/optimization' },\n    { name: '性能调优', href: '/services/tuning' },\n    { name: '架构设计', href: '/services/architecture' },\n    { name: '数据迁移', href: '/services/migration' },\n  ],\n  解决方案: [\n    { name: '企业级方案', href: '/solutions/enterprise' },\n    { name: '云数据库', href: '/solutions/cloud' },\n    { name: '高可用架构', href: '/solutions/ha' },\n    { name: '灾备方案', href: '/solutions/disaster-recovery' },\n  ],\n  学习资源: [\n    { name: '技术博客', href: '/blog' },\n    { name: '视频教程', href: '/tutorials' },\n    { name: 'API文档', href: '/docs' },\n    { name: '最佳实践', href: '/best-practices' },\n  ],\n  关于我们: [\n    { name: '公司介绍', href: '/about' },\n    { name: '团队成员', href: '/team' },\n    { name: '招聘信息', href: '/careers' },\n    { name: '联系我们', href: '/contact' },\n  ],\n};\n\n// 社交媒体链接\nexport const SOCIAL_LINKS: NavigationItem[] = [\n  {\n    name: 'GitHub',\n    href: 'https://github.com/mysqlai',\n    icon: 'Github',\n    isExternal: true,\n  },\n  {\n    name: '微信公众号',\n    href: '#wechat',\n    icon: 'MessageCircle',\n  },\n  {\n    name: 'QQ技术群',\n    href: '#qq-group',\n    icon: 'Users',\n  },\n  {\n    name: '技术博客',\n    href: '/blog',\n    icon: 'BookOpen',\n  },\n];\n\n// 法律链接\nexport const LEGAL_LINKS: NavigationItem[] = [\n  { name: '服务条款', href: '/terms' },\n  { name: '隐私政策', href: '/privacy' },\n  { name: '免责声明', href: '/disclaimer' },\n];\n\n// 导航工具函数\n\n/**\n * 检查当前路径是否为活跃导航项\n * @param href 导航链接\n * @param currentPath 当前路径\n * @returns 是否为活跃状态\n */\nexport function isActiveNavItem(href: string, currentPath: string): boolean {\n  if (href === '/') {\n    return currentPath === '/';\n  }\n  return currentPath.startsWith(href);\n}\n\n/**\n * 获取导航项的完整URL\n * @param href 相对或绝对链接\n * @param baseUrl 基础URL\n * @returns 完整URL\n */\nexport function getFullUrl(href: string, baseUrl: string = ''): string {\n  if (href.startsWith('http') || href.startsWith('//')) {\n    return href;\n  }\n  return `${baseUrl}${href}`;\n}\n\n/**\n * 检查链接是否为外部链接\n * @param href 链接地址\n * @returns 是否为外部链接\n */\nexport function isExternalLink(href: string): boolean {\n  return href.startsWith('http') || href.startsWith('//');\n}\n\n/**\n * 获取导航项的aria-label\n * @param item 导航项\n * @returns aria-label文本\n */\nexport function getNavItemAriaLabel(item: NavigationItem): string {\n  if (item.isExternal) {\n    return `${item.name} (在新窗口中打开)`;\n  }\n  return `前往${item.name}页面`;\n}\n\n/**\n * 根据路径获取面包屑导航\n * @param pathname 当前路径\n * @returns 面包屑数组\n */\nexport function getBreadcrumbs(pathname: string): NavigationItem[] {\n  const breadcrumbs: NavigationItem[] = [\n    { name: '首页', href: '/' }\n  ];\n\n  if (pathname === '/') {\n    return breadcrumbs;\n  }\n\n  // 页面的中文名称映射\n  const pageNames: Record<string, string> = {\n    'terms': '服务条款',\n    'privacy': '隐私政策',\n    'disclaimer': '免责声明',\n    'cookies': 'Cookie政策',\n    'contact': '联系我们',\n    'about': '关于我们'\n  };\n\n  // 查找匹配的主导航项\n  const mainNavItem = MAIN_NAVIGATION.find(item =>\n    pathname.startsWith(item.href) && item.href !== '/'\n  );\n\n  if (mainNavItem) {\n    breadcrumbs.push(mainNavItem);\n    // 如果找到了主导航项，直接返回，不再处理子页面逻辑\n    return breadcrumbs;\n  }\n\n  // 处理子页面路径\n  const pathSegments = pathname.split('/').filter(Boolean);\n\n  // 对于单级页面，只有在主导航中没有找到时才添加\n  if (pathSegments.length === 1) {\n    const segment = pathSegments[0];\n    const name = pageNames[segment];\n\n    if (!mainNavItem) {\n      // 只有在主导航中没有找到时，才添加到面包屑\n      if (name) {\n        // 使用页面名称映射\n        breadcrumbs.push({ name, href: pathname });\n      } else {\n        // 生成fallback名称\n        const fallbackName = segment\n          .split('-')\n          .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n          .join(' ');\n        breadcrumbs.push({ name: fallbackName, href: pathname });\n      }\n    }\n  } else if (pathSegments.length > 1) {\n    // 处理多级路径\n    for (let i = 1; i < pathSegments.length; i++) {\n      const segment = pathSegments[i];\n      const href = '/' + pathSegments.slice(0, i + 1).join('/');\n\n      const name = pageNames[segment] || segment\n        .split('-')\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n        .join(' ');\n\n      breadcrumbs.push({ name, href });\n    }\n  }\n\n  return breadcrumbs;\n}\n\n/**\n * 获取页面标题\n * @param pathname 当前路径\n * @param siteName 网站名称\n * @returns 页面标题\n */\nexport function getPageTitle(pathname: string, siteName: string = 'MySQLAi.de'): string {\n  if (pathname === '/') {\n    return `${siteName} - MySQL智能分析专家`;\n  }\n\n  const navItem = MAIN_NAVIGATION.find(item => \n    pathname.startsWith(item.href) && item.href !== '/'\n  );\n\n  if (navItem) {\n    return `${navItem.name} - ${siteName}`;\n  }\n\n  // 从路径生成标题\n  const pathSegments = pathname.split('/').filter(Boolean);\n  const lastSegment = pathSegments[pathSegments.length - 1];\n  const title = lastSegment\n    .split('-')\n    .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n    .join(' ');\n\n  return `${title} - ${siteName}`;\n}\n\n/**\n * 获取相关导航项（用于推荐链接）\n * @param currentHref 当前页面链接\n * @param maxItems 最大返回数量\n * @returns 相关导航项数组\n */\nexport function getRelatedNavItems(\n  currentHref: string, \n  maxItems: number = 3\n): NavigationItem[] {\n  return MAIN_NAVIGATION\n    .filter(item => item.href !== currentHref && item.href !== '/')\n    .slice(0, maxItems);\n}\n\n// 导航动画配置\nexport const NAV_ANIMATIONS = {\n  // 移动端菜单动画\n  mobileMenu: {\n    initial: { x: '100%' },\n    animate: { x: 0 },\n    exit: { x: '100%' },\n    transition: { type: 'spring', damping: 25, stiffness: 200 }\n  },\n  \n  // 背景遮罩动画\n  overlay: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n    exit: { opacity: 0 },\n    transition: { duration: 0.2 }\n  },\n  \n  // 菜单项动画\n  menuItem: {\n    initial: { opacity: 0, x: 20 },\n    animate: { opacity: 1, x: 0 },\n    transition: (index: number) => ({ delay: index * 0.1 })\n  },\n  \n  // Header背景变化动画\n  headerBackground: {\n    transition: { duration: 0.3 }\n  }\n};\n\n// 导航样式类名\nexport const NAV_STYLES = {\n  // 桌面端导航链接\n  desktopLink: [\n    'relative px-3 py-2 text-sm font-medium transition-all duration-200',\n    'text-mysql-text hover:text-mysql-primary',\n    'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5',\n    'before:bg-mysql-primary before:transition-all before:duration-300',\n    'hover:before:w-full'\n  ].join(' '),\n  \n  // 移动端菜单链接\n  mobileLink: [\n    'flex items-center px-4 py-3 text-mysql-text',\n    'hover:text-mysql-primary hover:bg-mysql-primary-light',\n    'rounded-lg transition-all duration-200'\n  ].join(' '),\n  \n  // 主要操作按钮\n  primaryButton: [\n    'inline-flex items-center px-4 py-2 bg-mysql-primary text-white',\n    'text-sm font-medium rounded-lg hover:bg-mysql-primary-dark',\n    'transition-colors duration-200'\n  ].join(' '),\n  \n  // 汉堡菜单按钮\n  hamburgerButton: [\n    'lg:hidden p-2 rounded-lg text-mysql-text',\n    'hover:text-mysql-primary hover:bg-mysql-primary-light',\n    'transition-all duration-200'\n  ].join(' ')\n};\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,mBAAmB;;;;;;;;;;;;;;;;;AAKZ,MAAM,mBAAqC;IAChD;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAGM,MAAM,kBAAoC;IAC/C;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAGM,MAAM,oBAAoB;IAC/B,MAAM;QACJ;YAAE,MAAM;YAAW,MAAM;QAAyB;QAClD;YAAE,MAAM;YAAQ,MAAM;QAAmB;QACzC;YAAE,MAAM;YAAQ,MAAM;QAAyB;QAC/C;YAAE,MAAM;YAAQ,MAAM;QAAsB;KAC7C;IACD,MAAM;QACJ;YAAE,MAAM;YAAS,MAAM;QAAwB;QAC/C;YAAE,MAAM;YAAQ,MAAM;QAAmB;QACzC;YAAE,MAAM;YAAS,MAAM;QAAgB;QACvC;YAAE,MAAM;YAAQ,MAAM;QAA+B;KACtD;IACD,MAAM;QACJ;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAa;QACnC;YAAE,MAAM;YAAS,MAAM;QAAQ;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAkB;KACzC;IACD,MAAM;QACJ;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAQ,MAAM;QAAW;QACjC;YAAE,MAAM;YAAQ,MAAM;QAAW;KAClC;AACH;AAGO,MAAM,eAAiC;IAC5C;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,YAAY;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAGM,MAAM,cAAgC;IAC3C;QAAE,MAAM;QAAQ,MAAM;IAAS;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAW;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAc;CACrC;AAUM,SAAS,gBAAgB,IAAY,EAAE,WAAmB;IAC/D,IAAI,SAAS,KAAK;QAChB,OAAO,gBAAgB;IACzB;IACA,OAAO,YAAY,UAAU,CAAC;AAChC;AAQO,SAAS,WAAW,IAAY,EAAE,UAAkB,EAAE;IAC3D,IAAI,KAAK,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,OAAO;QACpD,OAAO;IACT;IACA,OAAO,GAAG,UAAU,MAAM;AAC5B;AAOO,SAAS,eAAe,IAAY;IACzC,OAAO,KAAK,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC;AACpD;AAOO,SAAS,oBAAoB,IAAoB;IACtD,IAAI,KAAK,UAAU,EAAE;QACnB,OAAO,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;IACjC;IACA,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;AAC3B;AAOO,SAAS,eAAe,QAAgB;IAC7C,MAAM,cAAgC;QACpC;YAAE,MAAM;YAAM,MAAM;QAAI;KACzB;IAED,IAAI,aAAa,KAAK;QACpB,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,YAAoC;QACxC,SAAS;QACT,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,SAAS;IACX;IAEA,YAAY;IACZ,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,OACvC,SAAS,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;IAGlD,IAAI,aAAa;QACf,YAAY,IAAI,CAAC;QACjB,2BAA2B;QAC3B,OAAO;IACT;IAEA,UAAU;IACV,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAEhD,yBAAyB;IACzB,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,MAAM,UAAU,YAAY,CAAC,EAAE;QAC/B,MAAM,OAAO,SAAS,CAAC,QAAQ;QAE/B,IAAI,CAAC,aAAa;YAChB,uBAAuB;YACvB,IAAI,MAAM;gBACR,WAAW;gBACX,YAAY,IAAI,CAAC;oBAAE;oBAAM,MAAM;gBAAS;YAC1C,OAAO;gBACL,eAAe;gBACf,MAAM,eAAe,QAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;gBACR,YAAY,IAAI,CAAC;oBAAE,MAAM;oBAAc,MAAM;gBAAS;YACxD;QACF;IACF,OAAO,IAAI,aAAa,MAAM,GAAG,GAAG;QAClC,SAAS;QACT,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,UAAU,YAAY,CAAC,EAAE;YAC/B,MAAM,OAAO,MAAM,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;YAErD,MAAM,OAAO,SAAS,CAAC,QAAQ,IAAI,QAChC,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;YAER,YAAY,IAAI,CAAC;gBAAE;gBAAM;YAAK;QAChC;IACF;IAEA,OAAO;AACT;AAQO,SAAS,aAAa,QAAgB,EAAE,WAAmB,YAAY;IAC5E,IAAI,aAAa,KAAK;QACpB,OAAO,GAAG,SAAS,cAAc,CAAC;IACpC;IAEA,MAAM,UAAU,gBAAgB,IAAI,CAAC,CAAA,OACnC,SAAS,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;IAGlD,IAAI,SAAS;QACX,OAAO,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,UAAU;IACxC;IAEA,UAAU;IACV,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAChD,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;IACzD,MAAM,QAAQ,YACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IAER,OAAO,GAAG,MAAM,GAAG,EAAE,UAAU;AACjC;AAQO,SAAS,mBACd,WAAmB,EACnB,WAAmB,CAAC;IAEpB,OAAO,gBACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,KAC1D,KAAK,CAAC,GAAG;AACd;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,YAAY;QACV,SAAS;YAAE,GAAG;QAAO;QACrB,SAAS;YAAE,GAAG;QAAE;QAChB,MAAM;YAAE,GAAG;QAAO;QAClB,YAAY;YAAE,MAAM;YAAU,SAAS;YAAI,WAAW;QAAI;IAC5D;IAEA,SAAS;IACT,SAAS;QACP,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,QAAQ;IACR,UAAU;QACR,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY,CAAC,QAAkB,CAAC;gBAAE,OAAO,QAAQ;YAAI,CAAC;IACxD;IAEA,eAAe;IACf,kBAAkB;QAChB,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAGO,MAAM,aAAa;IACxB,UAAU;IACV,aAAa;QACX;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,UAAU;IACV,YAAY;QACV;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,SAAS;IACT,eAAe;QACb;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,SAAS;IACT,iBAAiB;QACf;QACA;QACA;KACD,CAAC,IAAI,CAAC;AACT", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/ui/DropdownMenu.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - DropdownMenu下拉菜单组件\n// 支持键盘导航、无障碍访问和平滑动画的可复用下拉菜单\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronDown } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { DropdownMenuProps, NavigationItem } from '@/lib/types';\n\nconst DropdownMenu = React.forwardRef<HTMLDivElement, DropdownMenuProps>(({\n  trigger,\n  items,\n  align = 'left',\n  onItemClick,\n  isOpen: controlledIsOpen,\n  onOpenChange,\n  className,\n  ...props\n}, ref) => {\n  // 路由导航\n  const router = useRouter();\n\n  // 状态管理\n  const [internalIsOpen, setInternalIsOpen] = useState(false);\n  const [focusedIndex, setFocusedIndex] = useState(-1);\n  \n  // 使用受控或非受控状态\n  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;\n  const setIsOpen = onOpenChange || setInternalIsOpen;\n  \n  // Refs\n  const triggerRef = useRef<HTMLButtonElement>(null);\n  const menuRef = useRef<HTMLDivElement>(null);\n  const itemRefs = useRef<(HTMLAnchorElement | null)[]>([]);\n  \n  // 切换菜单状态\n  const toggleMenu = () => {\n    setIsOpen(!isOpen);\n    if (!isOpen) {\n      setFocusedIndex(-1);\n    }\n  };\n  \n  // 关闭菜单\n  const closeMenu = () => {\n    setIsOpen(false);\n    setFocusedIndex(-1);\n    triggerRef.current?.focus();\n  };\n  \n  // 处理菜单项点击\n  const handleItemClick = (item: NavigationItem, event?: React.MouseEvent) => {\n    event?.preventDefault();\n    onItemClick?.(item);\n\n    // 导航到目标页面\n    if (item.isExternal) {\n      window.open(item.href, '_blank', 'noopener,noreferrer');\n    } else {\n      router.push(item.href);\n    }\n\n    closeMenu();\n  };\n  \n  // 键盘导航处理\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    switch (event.key) {\n      case 'Escape':\n        event.preventDefault();\n        closeMenu();\n        break;\n        \n      case 'ArrowDown':\n        event.preventDefault();\n        if (!isOpen) {\n          setIsOpen(true);\n          setFocusedIndex(0);\n        } else {\n          const nextIndex = focusedIndex < items.length - 1 ? focusedIndex + 1 : 0;\n          setFocusedIndex(nextIndex);\n          itemRefs.current[nextIndex]?.focus();\n        }\n        break;\n        \n      case 'ArrowUp':\n        event.preventDefault();\n        if (isOpen) {\n          const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : items.length - 1;\n          setFocusedIndex(prevIndex);\n          itemRefs.current[prevIndex]?.focus();\n        }\n        break;\n        \n      case 'Enter':\n      case ' ':\n        event.preventDefault();\n        if (!isOpen) {\n          setIsOpen(true);\n          setFocusedIndex(0);\n        } else if (focusedIndex >= 0) {\n          const item = items[focusedIndex];\n          handleItemClick(item);\n        }\n        break;\n        \n      case 'Tab':\n        if (isOpen) {\n          closeMenu();\n        }\n        break;\n    }\n  };\n  \n  // 点击外部关闭菜单\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        isOpen &&\n        triggerRef.current &&\n        menuRef.current &&\n        !triggerRef.current.contains(event.target as Node) &&\n        !menuRef.current.contains(event.target as Node)\n      ) {\n        closeMenu();\n      }\n    };\n    \n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [isOpen]);\n  \n  // 对齐样式\n  const alignmentStyles = {\n    left: 'left-0',\n    right: 'right-0',\n    center: 'left-1/2 transform -translate-x-1/2'\n  };\n  \n  // 动画配置\n  const menuVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.95,\n      y: -10,\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      y: 0,\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.95,\n      y: -10,\n    }\n  };\n  \n  return (\n    <div \n      ref={ref}\n      className={cn('relative inline-block', className)}\n      {...props}\n    >\n      {/* 触发器 */}\n      <button\n        ref={triggerRef}\n        type=\"button\"\n        onClick={toggleMenu}\n        onKeyDown={handleKeyDown}\n        className={cn(\n          'inline-flex items-center space-x-1 px-3 py-2 text-sm font-medium',\n          'text-mysql-text hover:text-mysql-primary transition-colors duration-200',\n          'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30 rounded-lg',\n          isOpen && 'text-mysql-primary'\n        )}\n        aria-expanded={isOpen ? 'true' : 'false'}\n        aria-haspopup=\"true\"\n        aria-label=\"打开菜单\"\n      >\n        {trigger}\n        <ChevronDown \n          className={cn(\n            'w-4 h-4 transition-transform duration-200',\n            isOpen && 'rotate-180'\n          )} \n        />\n      </button>\n      \n      {/* 下拉菜单 */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            ref={menuRef}\n            variants={menuVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"exit\"\n            transition={{ duration: 0.2, ease: 'easeOut' }}\n            className={cn(\n              'absolute top-full mt-2 w-56 bg-white rounded-lg shadow-xl border border-mysql-border z-50',\n              alignmentStyles[align]\n            )}\n          >\n            <div className=\"py-2\" role=\"menu\" aria-orientation=\"vertical\">\n              {items.map((item, index) => (\n                <a\n                  key={item.href}\n                  ref={(el) => {\n                    itemRefs.current[index] = el;\n                  }}\n                  href={item.href}\n                  onClick={(e) => handleItemClick(item, e)}\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      e.preventDefault();\n                      handleItemClick(item);\n                    }\n                  }}\n                  onFocus={() => setFocusedIndex(index)}\n                  className={cn(\n                    'flex items-center px-4 py-3 text-sm text-mysql-text',\n                    'hover:bg-mysql-primary-light hover:text-mysql-primary',\n                    'focus:bg-mysql-primary-light focus:text-mysql-primary focus:outline-none',\n                    'transition-colors duration-200 cursor-pointer',\n                    focusedIndex === index && 'bg-mysql-primary-light text-mysql-primary'\n                  )}\n                  role=\"menuitem\"\n                  tabIndex={-1}\n                >\n                  <span className=\"font-medium\">{item.name}</span>\n                </a>\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n});\n\nDropdownMenu.displayName = 'DropdownMenu';\n\nexport default DropdownMenu;\n"], "names": [], "mappings": ";;;;AAEA,kCAAkC;AAClC,4BAA4B;AAE5B;AACA;AACA;AAAA;AACA;AACA;AATA;;;;;;;AAYA,MAAM,6BAAe,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAoC,CAAC,EACxE,OAAO,EACP,KAAK,EACL,QAAQ,MAAM,EACd,WAAW,EACX,QAAQ,gBAAgB,EACxB,YAAY,EACZ,SAAS,EACT,GAAG,OACJ,EAAE;IACD,OAAO;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,OAAO;IACP,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAElD,aAAa;IACb,MAAM,SAAS,qBAAqB,YAAY,mBAAmB;IACnE,MAAM,YAAY,gBAAgB;IAElC,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC7C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgC,EAAE;IAExD,SAAS;IACT,MAAM,aAAa;QACjB,UAAU,CAAC;QACX,IAAI,CAAC,QAAQ;YACX,gBAAgB,CAAC;QACnB;IACF;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,UAAU;QACV,gBAAgB,CAAC;QACjB,WAAW,OAAO,EAAE;IACtB;IAEA,UAAU;IACV,MAAM,kBAAkB,CAAC,MAAsB;QAC7C,OAAO;QACP,cAAc;QAEd,UAAU;QACV,IAAI,KAAK,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,UAAU;QACnC,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;QAEA;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,OAAQ,MAAM,GAAG;YACf,KAAK;gBACH,MAAM,cAAc;gBACpB;gBACA;YAEF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,QAAQ;oBACX,UAAU;oBACV,gBAAgB;gBAClB,OAAO;oBACL,MAAM,YAAY,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe,IAAI;oBACvE,gBAAgB;oBAChB,SAAS,OAAO,CAAC,UAAU,EAAE;gBAC/B;gBACA;YAEF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,QAAQ;oBACV,MAAM,YAAY,eAAe,IAAI,eAAe,IAAI,MAAM,MAAM,GAAG;oBACvE,gBAAgB;oBAChB,SAAS,OAAO,CAAC,UAAU,EAAE;gBAC/B;gBACA;YAEF,KAAK;YACL,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,QAAQ;oBACX,UAAU;oBACV,gBAAgB;gBAClB,OAAO,IAAI,gBAAgB,GAAG;oBAC5B,MAAM,OAAO,KAAK,CAAC,aAAa;oBAChC,gBAAgB;gBAClB;gBACA;YAEF,KAAK;gBACH,IAAI,QAAQ;oBACV;gBACF;gBACA;QACJ;IACF;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UACA,WAAW,OAAO,IAClB,QAAQ,OAAO,IACf,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACzC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACtC;gBACA;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG;QAAC;KAAO;IAEX,OAAO;IACP,MAAM,kBAAkB;QACtB,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,GAAG,CAAC;QACN;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,GAAG;QACL;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,GAAG,CAAC;QACN;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;0BAGT,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,SAAS;gBACT,WAAW;gBACX,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,oEACA,2EACA,0EACA,UAAU;gBAEZ,iBAAe,SAAS,SAAS;gBACjC,iBAAc;gBACd,cAAW;;oBAEV;kCACD,8OAAC,oNAAA,CAAA,cAAW;wBACV,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6CACA,UAAU;;;;;;;;;;;;0BAMhB,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;oBAC7C,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6FACA,eAAe,CAAC,MAAM;8BAGxB,cAAA,8OAAC;wBAAI,WAAU;wBAAO,MAAK;wBAAO,oBAAiB;kCAChD,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAEC,KAAK,CAAC;oCACJ,SAAS,OAAO,CAAC,MAAM,GAAG;gCAC5B;gCACA,MAAM,KAAK,IAAI;gCACf,SAAS,CAAC,IAAM,gBAAgB,MAAM;gCACtC,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;wCACtC,EAAE,cAAc;wCAChB,gBAAgB;oCAClB;gCACF;gCACA,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,uDACA,yDACA,4EACA,iDACA,iBAAiB,SAAS;gCAE5B,MAAK;gCACL,UAAU,CAAC;0CAEX,cAAA,8OAAC;oCAAK,WAAU;8CAAe,KAAK,IAAI;;;;;;+BAvBnC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgChC;AAEA,aAAa,WAAW,GAAG;uCAEZ", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Header导航组件\n// 专业的响应式导航栏，支持桌面和移动端\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, Database, ChevronDown, ChevronRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { SITE_CONFIG } from '@/lib/constants';\nimport { NavigationProps } from '@/lib/types';\nimport { MAIN_NAVIGATION, NAV_ANIMATIONS, NAV_STYLES } from '@/lib/navigation';\nimport DropdownMenu from '@/components/ui/DropdownMenu';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nexport default function Header({ className }: HeaderProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [expandedSubmenus, setExpandedSubmenus] = useState<string[]>([]);\n\n  // 监听滚动事件，改变Header背景\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // 切换移动端菜单\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  // 关闭移动端菜单\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n    setExpandedSubmenus([]);\n  };\n\n  // 切换子菜单展开状态\n  const toggleSubmenu = (itemName: string) => {\n    setExpandedSubmenus(prev =>\n      prev.includes(itemName)\n        ? prev.filter(name => name !== itemName)\n        : [...prev, itemName]\n    );\n  };\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-mysql-border'\n          : 'bg-transparent',\n        className\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo和品牌标识 */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-3 group\"\n            onClick={closeMenu}\n          >\n            <div className=\"flex items-center justify-center w-10 h-10 bg-mysql-primary rounded-lg group-hover:bg-mysql-primary-dark transition-colors duration-200\">\n              <Database className=\"w-6 h-6 text-white\" />\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-200\">\n                {SITE_CONFIG.name}\n              </span>\n              <span className=\"text-xs text-mysql-text-light hidden sm:block\">\n                MySQL智能分析专家\n              </span>\n            </div>\n          </Link>\n\n          {/* 桌面端导航菜单 */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {MAIN_NAVIGATION.map((item) => {\n              // 检查是否有子菜单\n              if (item.children && item.children.length > 0) {\n                return (\n                  <DropdownMenu\n                    key={item.name}\n                    trigger={<span>{item.name}</span>}\n                    items={item.children}\n                    align=\"left\"\n                    className=\"relative\"\n                  />\n                );\n              }\n\n              // 普通导航项\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'relative px-3 py-2 text-sm font-medium transition-all duration-200',\n                    'text-mysql-text hover:text-mysql-primary',\n                    'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5',\n                    'before:bg-mysql-primary before:transition-all before:duration-300',\n                    'hover:before:w-full'\n                  )}\n                >\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* 右侧操作区域 */}\n          <div className=\"flex items-center space-x-4\">\n            {/* 移动端汉堡菜单按钮 */}\n            <button\n              type=\"button\"\n              onClick={toggleMenu}\n              className=\"lg:hidden p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200\"\n              aria-label=\"切换菜单\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 移动端菜单 */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <>\n            {/* 背景遮罩 */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"fixed inset-0 bg-black/50 backdrop-blur-sm lg:hidden\"\n              onClick={closeMenu}\n            />\n\n            {/* 侧边栏菜单 */}\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl lg:hidden\"\n            >\n              <div className=\"flex flex-col h-full\">\n                {/* 菜单头部 */}\n                <div className=\"flex items-center justify-between p-6 border-b border-mysql-border\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center justify-center w-8 h-8 bg-mysql-primary rounded-lg\">\n                      <Database className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <span className=\"text-lg font-bold text-mysql-text\">\n                      {SITE_CONFIG.name}\n                    </span>\n                  </div>\n                  <button\n                    type=\"button\"\n                    onClick={closeMenu}\n                    className=\"p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200\"\n                    aria-label=\"关闭菜单\"\n                  >\n                    <X className=\"w-5 h-5\" />\n                  </button>\n                </div>\n\n                {/* 菜单内容 */}\n                <nav className=\"flex-1 px-6 py-6\">\n                  <div className=\"space-y-2\">\n                    {MAIN_NAVIGATION.map((item, index) => (\n                      <motion.div\n                        key={item.name}\n                        initial={{ opacity: 0, x: 20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: index * 0.1 }}\n                      >\n                        {/* 检查是否有子菜单 */}\n                        {item.children && item.children.length > 0 ? (\n                          <div>\n                            {/* 父菜单项 */}\n                            <button\n                              type=\"button\"\n                              onClick={() => toggleSubmenu(item.name)}\n                              className=\"flex items-center justify-between w-full px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200\"\n                            >\n                              <span className=\"font-medium\">{item.name}</span>\n                              <ChevronRight\n                                className={cn(\n                                  'w-4 h-4 transition-transform duration-200',\n                                  expandedSubmenus.includes(item.name) && 'rotate-90'\n                                )}\n                              />\n                            </button>\n\n                            {/* 子菜单 */}\n                            <AnimatePresence>\n                              {expandedSubmenus.includes(item.name) && (\n                                <motion.div\n                                  initial={{ opacity: 0, height: 0 }}\n                                  animate={{ opacity: 1, height: 'auto' }}\n                                  exit={{ opacity: 0, height: 0 }}\n                                  transition={{ duration: 0.2 }}\n                                  className=\"ml-4 mt-2 space-y-1\"\n                                >\n                                  {item.children.map((subItem) => (\n                                    <Link\n                                      key={subItem.name}\n                                      href={subItem.href}\n                                      onClick={closeMenu}\n                                      className=\"flex items-center px-4 py-2 text-sm text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200\"\n                                    >\n                                      <span>{subItem.name}</span>\n                                    </Link>\n                                  ))}\n                                </motion.div>\n                              )}\n                            </AnimatePresence>\n                          </div>\n                        ) : (\n                          /* 普通菜单项 */\n                          <Link\n                            href={item.href}\n                            onClick={closeMenu}\n                            className=\"flex items-center px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200\"\n                          >\n                            <span className=\"font-medium\">{item.name}</span>\n                          </Link>\n                        )}\n                      </motion.div>\n                    ))}\n                  </div>\n                </nav>\n\n                {/* 菜单底部 */}\n                <div className=\"p-6 border-t border-mysql-border\">\n                  <Link\n                    href=\"/contact\"\n                    onClick={closeMenu}\n                    className=\"flex items-center justify-center w-full px-4 py-3 bg-mysql-primary text-white font-medium rounded-lg hover:bg-mysql-primary-dark transition-colors duration-200\"\n                  >\n                    联系我们\n                  </Link>\n                </div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,0BAA0B;AAC1B,qBAAqB;AAErB;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAbA;;;;;;;;;;AAmBe,SAAS,OAAO,EAAE,SAAS,EAAe;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,UAAU;IACV,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,UAAU;IACV,MAAM,YAAY;QAChB,cAAc;QACd,oBAAoB,EAAE;IACxB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,wEACA,kBACJ;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS;;8CAET,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,qIAAA,CAAA,cAAW,CAAC,IAAI;;;;;;sDAEnB,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;4BAAI,WAAU;sCACZ,sIAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC;gCACpB,WAAW;gCACX,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAC7C,qBACE,8OAAC,sJAAA,CAAA,UAAY;wCAEX,uBAAS,8OAAC;sDAAM,KAAK,IAAI;;;;;;wCACzB,OAAO,KAAK,QAAQ;wCACpB,OAAM;wCACN,WAAU;uCAJL,KAAK,IAAI;;;;;gCAOpB;gCAEA,QAAQ;gCACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sEACA,4CACA,yEACA,qEACA;8CAGD,KAAK,IAAI;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEV,2BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC;;sCAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;4BACV,SAAS;;;;;;sCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAK,WAAU;kEACb,qIAAA,CAAA,cAAW,CAAC,IAAI;;;;;;;;;;;;0DAGrB,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;gDACV,cAAW;0DAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,sIAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO,QAAQ;oDAAI;8DAGhC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,kBACvC,8OAAC;;0EAEC,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,cAAc,KAAK,IAAI;gEACtC,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,8OAAC,sNAAA,CAAA,eAAY;wEACX,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6CACA,iBAAiB,QAAQ,CAAC,KAAK,IAAI,KAAK;;;;;;;;;;;;0EAM9C,8OAAC,yLAAA,CAAA,kBAAe;0EACb,iBAAiB,QAAQ,CAAC,KAAK,IAAI,mBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,SAAS;wEAAE,SAAS;wEAAG,QAAQ;oEAAE;oEACjC,SAAS;wEAAE,SAAS;wEAAG,QAAQ;oEAAO;oEACtC,MAAM;wEAAE,SAAS;wEAAG,QAAQ;oEAAE;oEAC9B,YAAY;wEAAE,UAAU;oEAAI;oEAC5B,WAAU;8EAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC,4JAAA,CAAA,UAAI;4EAEH,MAAM,QAAQ,IAAI;4EAClB,SAAS;4EACT,WAAU;sFAEV,cAAA,8OAAC;0FAAM,QAAQ,IAAI;;;;;;2EALd,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;+DAa7B,SAAS,iBACT,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,SAAS;wDACT,WAAU;kEAEV,cAAA,8OAAC;4DAAK,WAAU;sEAAe,KAAK,IAAI;;;;;;;;;;;mDAtDvC,KAAK,IAAI;;;;;;;;;;;;;;;kDA+DtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/MysqlAi.De/web-app/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\n// MySQLAi.de - Footer页脚组件\n// 包含版权信息、导航链接、社交媒体链接、法律声明等内容\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Github, Twitter, Linkedin, Mail, Phone, MapPin } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface FooterProps {\n  className?: string;\n}\n\n// 页脚导航数据\nconst FOOTER_NAVIGATION = {\n  products: {\n    title: '产品服务',\n    links: [\n      { name: 'MySQL知识库', href: '/knowledge' },\n      { name: '项目管理', href: '/projects' },\n      { name: '报告展示', href: '/reports' },\n      { name: 'AI智能分析', href: '/ai-analysis' },\n    ],\n  },\n  company: {\n    title: '关于我们',\n    links: [\n      { name: '公司介绍', href: '/about' },\n      { name: '团队成员', href: '/team' },\n      { name: '新闻动态', href: '/news' },\n      { name: '招聘信息', href: '/careers' },\n    ],\n  },\n  support: {\n    title: '技术支持',\n    links: [\n      { name: '帮助中心', href: '/help' },\n      { name: '技术文档', href: '/docs' },\n      { name: '联系我们', href: '/contact' },\n      { name: '在线客服', href: '/chat' },\n    ],\n  },\n  legal: {\n    title: '法律声明',\n    links: [\n      { name: '服务条款', href: '/terms' },\n      { name: '隐私政策', href: '/privacy' },\n      { name: '免责声明', href: '/disclaimer' },\n      { name: 'Cookie政策', href: '/cookies' },\n    ],\n  },\n} as const;\n\n// 社交媒体链接\nconst SOCIAL_LINKS = [\n  {\n    name: 'GitHub',\n    href: 'https://github.com/mysqlai',\n    icon: Github,\n    color: 'hover:text-gray-900',\n  },\n  {\n    name: 'Twitter',\n    href: 'https://twitter.com/mysqlai',\n    icon: Twitter,\n    color: 'hover:text-blue-400',\n  },\n  {\n    name: 'LinkedIn',\n    href: 'https://linkedin.com/company/mysqlai',\n    icon: Linkedin,\n    color: 'hover:text-blue-600',\n  },\n  {\n    name: 'Email',\n    href: 'mailto:<EMAIL>',\n    icon: Mail,\n    color: 'hover:text-mysql-primary',\n  },\n] as const;\n\n// 联系信息\nconst CONTACT_INFO = [\n  {\n    icon: Phone,\n    text: '+86 ************',\n    href: 'tel:+8640088899999',\n  },\n  {\n    icon: Mail,\n    text: '<EMAIL>',\n    href: 'mailto:<EMAIL>',\n  },\n  {\n    icon: MapPin,\n    text: '北京市朝阳区科技园区',\n    href: '#',\n  },\n] as const;\n\nexport default function Footer({ className }: FooterProps) {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer\n      className={cn(\n        'bg-gradient-to-b from-mysql-primary-dark to-mysql-primary-dark/90 text-white',\n        className\n      )}\n    >\n      {/* 极简风格主要内容区域 */}\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        {/* 品牌和导航 - 极简一行布局 */}\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8\">\n          {/* 品牌信息 */}\n          <div className=\"text-center lg:text-left\">\n            <h3 className=\"text-xl font-bold text-white mb-2\">MySQLAi.de</h3>\n            <p className=\"text-mysql-primary-light text-sm max-w-md\">\n              专业的MySQL智能分析平台\n            </p>\n          </div>\n\n          {/* 导航链接 - 极简水平布局 */}\n          <div className=\"flex flex-wrap justify-center lg:justify-end gap-8\">\n            {Object.entries(FOOTER_NAVIGATION).map(([key, section]) => (\n              <div key={key} className=\"flex flex-col items-center lg:items-start\">\n                <h4 className=\"text-white font-medium text-sm mb-3\">{section.title}</h4>\n                <div className=\"flex flex-col space-y-2\">\n                  {section.links.slice(0, 3).map((link, linkIndex) => (\n                    <a\n                      key={linkIndex}\n                      href={link.href}\n                      className=\"text-mysql-primary-light hover:text-white transition-colors duration-200 text-xs\"\n                    >\n                      {link.name}\n                    </a>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 极简分隔线 */}\n      <div className=\"border-t border-mysql-primary-light/10\" />\n\n      {/* 极简底部区域 */}\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n          {/* 简化版权信息 */}\n          <p className=\"text-mysql-primary-light/70 text-xs\">\n            © {currentYear} MySQLAi.de\n          </p>\n\n          {/* 简化联系方式 */}\n          <div className=\"flex items-center gap-6 text-xs\">\n            <a\n              href=\"mailto:<EMAIL>\"\n              className=\"text-mysql-primary-light/70 hover:text-white transition-colors duration-200\"\n            >\n              <EMAIL>\n            </a>\n            <a\n              href=\"tel:+8640088899999\"\n              className=\"text-mysql-primary-light/70 hover:text-white transition-colors duration-200\"\n            >\n              +86 ************\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* 回到顶部按钮 */}\n      <motion.button\n        initial={{ opacity: 0, scale: 0.8 }}\n        whileInView={{ opacity: 1, scale: 1 }}\n        whileHover={{ scale: 1.1, y: -2 }}\n        whileTap={{ scale: 0.95 }}\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\n        viewport={{ once: true }}\n        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n        className=\"fixed bottom-8 right-8 p-3 bg-mysql-primary text-white rounded-full shadow-lg hover:bg-mysql-primary-dark transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-mysql-primary/30 z-50\"\n        aria-label=\"回到顶部\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n        </svg>\n      </motion.button>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;AAcA,SAAS;AACT,MAAM,oBAAoB;IACxB,UAAU;QACR,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAY;YAClC;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAU,MAAM;YAAe;SACxC;IACH;IACA,SAAS;QACP,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;SAClC;IACH;IACA,SAAS;QACP,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;IACH;IACA,OAAO;QACL,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAS;YAC/B;gBAAE,MAAM;gBAAQ,MAAM;YAAW;YACjC;gBAAE,MAAM;gBAAQ,MAAM;YAAc;YACpC;gBAAE,MAAM;gBAAY,MAAM;YAAW;SACtC;IACH;AACF;AAEA,SAAS;AACT,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAED,OAAO;AACP,MAAM,eAAe;IACnB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,MAAM;QACN,MAAM;IACR;CACD;AAEc,SAAS,OAAO,EAAE,SAAS,EAAe;IACvD,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,gFACA;;0BAIF,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,iBACpD,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAG,WAAU;sDAAuC,QAAQ,KAAK;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,0BACpC,8OAAC;oDAEC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;mDAJL;;;;;;;;;;;mCALH;;;;;;;;;;;;;;;;;;;;;0BAoBlB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAE,WAAU;;gCAAsC;gCAC9C;gCAAY;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,aAAa;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBACpC,YAAY;oBAAE,OAAO;oBAAK,GAAG,CAAC;gBAAE;gBAChC,UAAU;oBAAE,OAAO;gBAAK;gBACxB,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;gBAC7C,UAAU;oBAAE,MAAM;gBAAK;gBACvB,SAAS,IAAM,OAAO,QAAQ,CAAC;wBAAE,KAAK;wBAAG,UAAU;oBAAS;gBAC5D,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E", "debugId": null}}]}